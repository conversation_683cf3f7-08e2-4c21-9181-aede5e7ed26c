package com.morohub.apsp.api.controller;

import com.morohub.apsp.core.entity.MessageProcessingQueue;
import com.morohub.apsp.core.service.ReprocessingService;
import com.morohub.apsp.core.repository.MessageProcessingQueueRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * REST API controller for manual reprocessing operations
 * Allows manual reprocessing of failed messages using the same controller logic
 */
@RestController
@RequestMapping("/api/reprocessing")
public class ReprocessingController {

    private static final Logger logger = LoggerFactory.getLogger(ReprocessingController.class);

    @Autowired
    private ReprocessingService reprocessingService;

    @Autowired
    private MessageProcessingQueueRepository messageQueueRepository;

    /**
     * Get messages available for reprocessing
     */
    @GetMapping("/messages")
    public ResponseEntity<List<MessageProcessingQueue>> getMessagesForReprocessing(
            @RequestParam(required = false) String flowType) {
        try {
            List<MessageProcessingQueue> messages;
            
            if (flowType != null) {
                MessageProcessingQueue.FlowType flow = MessageProcessingQueue.FlowType.valueOf(flowType.toUpperCase());
                messages = reprocessingService.getMessagesForReprocessing(flow);
            } else {
                messages = reprocessingService.getMessagesForReprocessing();
            }
            
            return ResponseEntity.ok(messages);
        } catch (Exception e) {
            logger.error("❌ Error getting messages for reprocessing: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Manually reprocess a specific message
     */
    @PostMapping("/messages/{messageId}/reprocess")
    public ResponseEntity<Map<String, Object>> reprocessMessage(
            @PathVariable String messageId,
            @RequestParam String triggeredBy) {
        try {
            Optional<MessageProcessingQueue> messageOpt = messageQueueRepository.findByMessageId(messageId);
            
            if (messageOpt.isEmpty()) {
                Map<String, Object> response = Map.of(
                    "success", false,
                    "message", "Message not found",
                    "messageId", messageId
                );
                return ResponseEntity.notFound().build();
            }

            MessageProcessingQueue message = messageOpt.get();
            
            // Check if message can be reprocessed
            if (message.getStatus() == MessageProcessingQueue.ProcessingStatus.PROCESSING) {
                Map<String, Object> response = Map.of(
                    "success", false,
                    "message", "Message is currently being processed",
                    "messageId", messageId,
                    "currentStatus", message.getStatus()
                );
                return ResponseEntity.badRequest().body(response);
            }

            logger.info("🔄 Manual reprocessing triggered for message {} by {}", messageId, triggeredBy);
            
            boolean success = reprocessingService.reprocessMessage(message);
            
            Map<String, Object> response = Map.of(
                "success", success,
                "message", success ? "Reprocessing completed successfully" : "Reprocessing failed",
                "messageId", messageId,
                "flowType", message.getFlowType(),
                "triggeredBy", triggeredBy,
                "triggeredAt", LocalDateTime.now(),
                "newStatus", message.getStatus()
            );
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("❌ Error during manual reprocessing for message {}: {}", messageId, e.getMessage(), e);
            
            Map<String, Object> response = Map.of(
                "success", false,
                "message", "Reprocessing failed: " + e.getMessage(),
                "messageId", messageId
            );
            
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * Get message details by message ID
     */
    @GetMapping("/messages/{messageId}")
    public ResponseEntity<MessageProcessingQueue> getMessageDetails(@PathVariable String messageId) {
        try {
            Optional<MessageProcessingQueue> messageOpt = messageQueueRepository.findByMessageId(messageId);
            
            if (messageOpt.isPresent()) {
                return ResponseEntity.ok(messageOpt.get());
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            logger.error("❌ Error getting message details for {}: {}", messageId, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get reprocessing statistics
     */
    @GetMapping("/statistics")
    public ResponseEntity<Map<String, Object>> getReprocessingStatistics() {
        try {
            List<MessageProcessingQueue> retryMessages = reprocessingService.getMessagesForReprocessing();
            
            // Count by flow type
            long forwardFlowCount = retryMessages.stream()
                .filter(m -> m.getFlowType() == MessageProcessingQueue.FlowType.FORWARD_FLOW)
                .count();
            
            long reverseFlowCount = retryMessages.stream()
                .filter(m -> m.getFlowType() == MessageProcessingQueue.FlowType.REVERSE_FLOW)
                .count();
            
            long xmlValidationCount = retryMessages.stream()
                .filter(m -> m.getFlowType() == MessageProcessingQueue.FlowType.XML_VALIDATION)
                .count();
            
            long peppolSbdCount = retryMessages.stream()
                .filter(m -> m.getFlowType() == MessageProcessingQueue.FlowType.PEPPOL_SBD_INVOICE)
                .count();
            
            long mlsCount = retryMessages.stream()
                .filter(m -> m.getFlowType() == MessageProcessingQueue.FlowType.MLS_MESSAGE)
                .count();

            Map<String, Object> statistics = Map.of(
                "totalMessagesForReprocessing", retryMessages.size(),
                "byFlowType", Map.of(
                    "FORWARD_FLOW", forwardFlowCount,
                    "REVERSE_FLOW", reverseFlowCount,
                    "XML_VALIDATION", xmlValidationCount,
                    "PEPPOL_SBD_INVOICE", peppolSbdCount,
                    "MLS_MESSAGE", mlsCount
                ),
                "lastUpdated", LocalDateTime.now()
            );
            
            return ResponseEntity.ok(statistics);
        } catch (Exception e) {
            logger.error("❌ Error getting reprocessing statistics: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Bulk reprocess messages by flow type
     */
    @PostMapping("/bulk-reprocess")
    public ResponseEntity<Map<String, Object>> bulkReprocess(
            @RequestParam String flowType,
            @RequestParam String triggeredBy,
            @RequestParam(defaultValue = "10") int maxMessages) {
        try {
            MessageProcessingQueue.FlowType flow = MessageProcessingQueue.FlowType.valueOf(flowType.toUpperCase());
            List<MessageProcessingQueue> messages = reprocessingService.getMessagesForReprocessing(flow);
            
            int processedCount = 0;
            int successCount = 0;
            int failureCount = 0;
            
            for (MessageProcessingQueue message : messages) {
                if (processedCount >= maxMessages) {
                    break;
                }
                
                try {
                    boolean success = reprocessingService.reprocessMessage(message);
                    if (success) {
                        successCount++;
                    } else {
                        failureCount++;
                    }
                    processedCount++;
                } catch (Exception e) {
                    logger.error("❌ Error in bulk reprocessing for message {}: {}", 
                                message.getMessageId(), e.getMessage(), e);
                    failureCount++;
                    processedCount++;
                }
            }
            
            Map<String, Object> response = Map.of(
                "totalProcessed", processedCount,
                "successful", successCount,
                "failed", failureCount,
                "flowType", flowType,
                "triggeredBy", triggeredBy,
                "triggeredAt", LocalDateTime.now()
            );
            
            logger.info("✅ Bulk reprocessing completed: {} processed, {} successful, {} failed", 
                       processedCount, successCount, failureCount);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("❌ Error during bulk reprocessing: {}", e.getMessage(), e);
            
            Map<String, Object> response = Map.of(
                "success", false,
                "message", "Bulk reprocessing failed: " + e.getMessage(),
                "flowType", flowType
            );
            
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * Health check for reprocessing system
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        try {
            Map<String, Object> health = Map.of(
                "status", "UP",
                "timestamp", LocalDateTime.now(),
                "reprocessingService", "ACTIVE"
            );
            
            return ResponseEntity.ok(health);
        } catch (Exception e) {
            logger.error("❌ Error in reprocessing health check: {}", e.getMessage(), e);
            
            Map<String, Object> health = Map.of(
                "status", "DOWN",
                "timestamp", LocalDateTime.now(),
                "error", e.getMessage()
            );
            
            return ResponseEntity.internalServerError().body(health);
        }
    }
}
