package com.morohub.apsp.core.service;

import com.helger.peppolid.factory.PeppolIdentifierFactory;
import com.helger.peppolid.IParticipantIdentifier;
import com.helger.peppolid.IDocumentTypeIdentifier;
import com.helger.peppolid.IProcessIdentifier;
import com.helger.phase4.dynamicdiscovery.AS4EndpointDetailProviderPeppol;
import com.helger.phase4.dynamicdiscovery.IAS4EndpointDetailProvider;
import com.helger.phase4.model.pmode.leg.PModeLegProtocol;
import com.helger.phase4.peppol.Phase4PeppolSender;
import com.helger.phase4.sender.AbstractAS4UserMessageBuilder.ESimpleUserMessageSendResult;
import com.helger.phase4.client.IAS4ClientBuildMessageCallback;
import com.helger.phase4.messaging.domain.AS4UserMessage;
import com.helger.phase4.mgr.MetaAS4Manager;
import com.helger.phase4.model.pmode.leg.PModeLegSecurity;
import com.helger.phase4.model.pmode.PMode;
import com.helger.phase4.model.pmode.PModeParty;
import com.helger.phase4.model.pmode.resolve.IPModeResolver;
import com.helger.phase4.util.AS4ResourceHelper;
import com.helger.scope.mgr.ScopeManager;
import com.helger.smpclient.peppol.ISMPServiceMetadataProvider;
import com.helger.phase4.crypto.AS4CryptoFactoryProperties;
import com.helger.phase4.crypto.IAS4CryptoFactory;
import com.morohub.apsp.config.AS4CryptoConfiguration;
import com.morohub.apsp.config.CountryConfigurationService;
import com.morohub.apsp.config.model.XmlParsingConfig;
import com.morohub.apsp.config.model.SbdhConfiguration;
import com.helger.phase4.crypto.IAS4CryptoFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.w3c.dom.Document;
import org.w3c.dom.NodeList;
import org.w3c.dom.Element;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import jakarta.annotation.Nonnull;
import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import java.io.StringWriter;
import java.io.ByteArrayInputStream;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.UUID;
import java.security.KeyStore;
import java.io.InputStream;
import com.morohub.apsp.config.ConfigurableResourceLoader;
import com.morohub.apsp.common.exception.AS4Exception;
import com.morohub.apsp.common.exception.ConnectivityException;
import com.morohub.apsp.common.exception.ValidationException;
import com.morohub.apsp.core.entity.ErrorMasterData;

/**
 * Production-ready AS4 service for converting validated UBL invoices to AS4 messages
 * Supports both dummy mode (testing) and production mode (real Peppol network)
 */
@Service
public class AS4ConversionService {

    private static final Logger LOGGER = LoggerFactory.getLogger(AS4ConversionService.class);

    @Value("${as4.mode:dummy}")
    private String as4Mode;

    @Value("${as4.endpoint.url:https://httpbin.org/post}")
    private String endpointUrl;

    @Value("${as4.security.enabled:true}")
    private boolean securityEnabled;

    @Value("${as4.validation.enabled:true}")
    private boolean validationEnabled;

    // Keystore Configuration
    @Value("${app.config.keystore.base-path:keystore/}")
    private String keystoreBasePath;

    @Value("${as4.keystore.path:cert.p12}")
    private String keystorePath;

    @Value("${as4.keystore.password:TFd20lJQ4f8j}")
    private String keystorePassword;

    @Value("${as4.keystore.key.alias:cert}")
    private String keyAlias;

    @Value("${as4.keystore.key.password:TFd20lJQ4f8j}")
    private String keyPassword;

    @Value("${as4.truststore.path:cert.p12}")
    private String truststorePath;

    @Value("${as4.truststore.password:TFd20lJQ4f8j}")
    private String truststorePassword;

    @Autowired
    private AS4CryptoConfiguration cryptoConfiguration;

    @Autowired
    private IAS4CryptoFactory as4CryptoFactory;

    @Autowired
    private CountryConfigurationService countryConfigurationService;

    @Autowired
    private ConfigurableResourceLoader resourceLoader;

    @Autowired
    private MessageQueueService messageQueueService;

    @PostConstruct
    public void initializeAS4() {
        LOGGER.info("=== Initializing Production-Ready AS4 Service ===");
        LOGGER.info("AS4 Mode: {}", as4Mode);
        LOGGER.info("Security Enabled: {}", securityEnabled);
        LOGGER.info("Validation Enabled: {}", validationEnabled);
        LOGGER.info("Endpoint: {}", endpointUrl);

        // Check keystore availability
        checkKeystoreAvailability();

        try {
            // Initialize Phase4 scope management
            if (!ScopeManager.isGlobalScopePresent()) {
                ScopeManager.onGlobalBegin("morohub-as4-service");
                LOGGER.info("✅ Phase4 global scope initialized");
            }

            // Configure Phase4 crypto settings with our keystore
            configureCryptoSettings();

            // Initialize MetaAS4Manager
            MetaAS4Manager.getInstance();
            LOGGER.info("✅ MetaAS4Manager initialized");

        } catch (Exception e) {
            LOGGER.error("❌ Failed to initialize AS4 service: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to initialize AS4 service", e);
        }

        LOGGER.info("✅ AS4 service initialized successfully for {} mode", as4Mode);
    }

    @PreDestroy
    public void shutdownAS4() {
        LOGGER.info("Shutting down AS4 service...");
        try {
            if (ScopeManager.isGlobalScopePresent()) {
                ScopeManager.onGlobalEnd();
                LOGGER.info("✅ Phase4 global scope cleaned up");
            }
        } catch (Exception e) {
            LOGGER.warn("⚠️ Error during AS4 shutdown: {}", e.getMessage());
        }
    }

    /**
     * Configure Phase4 crypto settings with our keystore
     */
    private void configureCryptoSettings() {
        LOGGER.info("=== Configuring Phase4 Crypto Settings ===");

        if (!securityEnabled) {
            LOGGER.warn("⚠️ Security is disabled - crypto settings will not be configured");
            return;
        }

        try {
            // Check if crypto configuration is available
            if (cryptoConfiguration.isCryptoConfigured()) {
                LOGGER.info("✅ Crypto configuration is properly set up");
                LOGGER.info("📁 Keystore info: {}", cryptoConfiguration.getKeystoreInfo());
            } else {
                LOGGER.warn("⚠️ Crypto configuration is not properly set up");
                if ("production".equals(as4Mode)) {
                    throw new RuntimeException("Crypto configuration is mandatory for production mode");
                }
            }

        } catch (Exception e) {
            LOGGER.error("❌ Failed to configure crypto settings: {}", e.getMessage(), e);
            if ("production".equals(as4Mode)) {
                throw new AS4Exception("AS4_003", "Failed to configure crypto settings for production mode: " + e.getMessage(),
                    ErrorMasterData.ErrorType.CONFIGURATION_ERROR,
                    ErrorMasterData.ErrorSeverity.CRITICAL, false, null, null, e);
            } else {
                LOGGER.warn("⚠️ Continuing without crypto configuration in {} mode", as4Mode);
            }
        }
    }

    /**
     * Convert validated UBL XML to AS4 message and send
     */
    public AS4ConversionResult convertAndSend(String validatedXml) {
        String transactionId = UUID.randomUUID().toString();
        AS4ConversionResult result = new AS4ConversionResult();
        result.setStartTime(System.currentTimeMillis());
        result.setAs4MessageId(transactionId);

        try {
            LOGGER.info("=== Starting AS4 Conversion ===");
            LOGGER.info("Transaction ID: {}", transactionId);
            LOGGER.info("Mode: {}, Security: {}", as4Mode, securityEnabled);

            // Post to message processing queue for retry capability
            try {
                messageQueueService.addAS4ConversionToQueue(validatedXml, "DEFAULT", "INVOICE",
                    transactionId, null);
                LOGGER.info("✅ AS4 conversion request added to processing queue: {}", transactionId);
            } catch (Exception queueException) {
                LOGGER.warn("⚠️ Failed to add to processing queue, continuing with direct processing: {}",
                           queueException.getMessage());
            }

            // Step 1: Extract AS4 metadata from UBL XML
            AS4MessageMetadata metadata = extractAS4Metadata(validatedXml);
            result.setMetadata(metadata);

            // Step 2: Validate message for AS4 conversion
            String validationError = validateMessageForAS4(metadata);
            if (validationError != null) {
                result.setSuccess(false);
                result.setErrorMessage("AS4 validation failed: " + validationError);
                result.setEndTime(System.currentTimeMillis());
                return result;
            }

            // Step 3: Convert to AS4 message based on mode

            LOGGER.info("🏭 PRODUCTION/TEST MODE: Using Phase4PeppolSender");
            result = sendAS4Message(validatedXml, metadata, result);


            result.setEndTime(System.currentTimeMillis());
            LOGGER.info("✅ AS4 conversion completed in {} ms", result.getDuration());

            return result;

        } catch (AS4Exception e) {
            LOGGER.error("❌ AS4 conversion failed with AS4Exception: {}", e.getMessage(), e);
            result.setSuccess(false);
            result.setErrorMessage(e.getMessage());
            result.setEndTime(System.currentTimeMillis());
            throw e; // Re-throw to be handled by GlobalExceptionHandler
        } catch (Exception e) {
            LOGGER.error("❌ AS4 conversion failed with unexpected error", e);
            result.setSuccess(false);
            result.setErrorMessage("AS4 conversion failed: " + e.getMessage());
            result.setEndTime(System.currentTimeMillis());

            // Convert to AS4Exception
            throw new AS4Exception("AS4_001", "AS4 conversion failed: " + e.getMessage(),
                ErrorMasterData.ErrorType.TECHNICAL_CONNECTIVITY_ERROR,
                ErrorMasterData.ErrorSeverity.HIGH, true, transactionId, null, e);
        }
    }

    private String validateMessageForAS4(AS4MessageMetadata metadata) {
        LOGGER.debug("Validating AS4 message metadata...");

        if (metadata.getSenderParticipantId() == null) {
            return "Sender participant ID is missing";
        }

        if (metadata.getReceiverParticipantId() == null) {
            return "Receiver participant ID is missing";
        }

        // Validate participant ID values are not empty
        if (metadata.getSenderParticipantId().getValue() == null ||
            metadata.getSenderParticipantId().getValue().trim().isEmpty()) {
            return "Sender participant ID value is empty";
        }

        if (metadata.getReceiverParticipantId().getValue() == null ||
            metadata.getReceiverParticipantId().getValue().trim().isEmpty()) {
            return "Receiver participant ID value is empty";
        }

        if (metadata.getDocumentTypeId() == null) {
            return "Document type ID is missing";
        }

        if (metadata.getProcessId() == null) {
            return "Process ID is missing";
        }

        if (metadata.getCountryCode() == null || metadata.getCountryCode().trim().isEmpty()) {
            return "Country code (CountryC1) is missing - required for SBDH creation";
        }

        // Production mode additional validations
        if ("production".equals(as4Mode)) {
            if (!securityEnabled) {
                return "Security must be enabled in production mode";
            }

            if (!metadata.getSenderParticipantId().getValue().startsWith("9908:")) {
                return "Invalid sender participant ID format for production";
            }
        }

        LOGGER.info("✅ AS4 message validation passed");
        return null; // No validation errors
    }

    private AS4MessageMetadata extractAS4Metadata(String xml) throws Exception {
        LOGGER.info("Extracting AS4 metadata from UBL XML");

        DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
        factory.setNamespaceAware(true);
        DocumentBuilder builder = factory.newDocumentBuilder();
        Document doc = builder.parse(new ByteArrayInputStream(xml.getBytes(StandardCharsets.UTF_8)));

        AS4MessageMetadata metadata = new AS4MessageMetadata();

        // Get global config for default values
        XmlParsingConfig globalConfig = countryConfigurationService.getXmlParsingConfig("DEFAULT", "INVOICE");

        // Determine document type and country from XML
        String documentType = determineDocumentType(doc, globalConfig);
        String countryCode = extractCountryCodeFromXml(doc);
        if (countryCode == null || countryCode.trim().isEmpty()) {
            countryCode = globalConfig.getGlobalDefaultCountryCode(); // Use configurable default country
        }

        LOGGER.info("📄 Document type: {}, Country: {}", documentType, countryCode);

        // Get country and document-specific configuration
        XmlParsingConfig config =
            countryConfigurationService.getXmlParsingConfig(countryCode, documentType);

        // Extract sender participant ID and scheme using configurable element names
        String senderId = extractParticipantId(doc, config.getSenderPartyElement(), config);
        String senderSchemeId = extractSchemeId(doc, config.getSenderPartyElement(), config);
        if (senderId != null && !senderId.trim().isEmpty()) {
            metadata.setSenderParticipantId(
                    PeppolIdentifierFactory.INSTANCE.createParticipantIdentifierWithDefaultScheme(senderId)
            );
            metadata.setSenderSchemeId(senderSchemeId);
            LOGGER.info("👤 Extracted sender participant ID: {} with scheme: {}", senderId, senderSchemeId);
        } else {
            LOGGER.warn("⚠️ No sender participant ID found in UBL XML");
        }

        // Extract receiver participant ID and scheme using configurable element names
        String receiverId = extractParticipantId(doc, config.getReceiverPartyElement(), config);
        String receiverSchemeId = extractSchemeId(doc, config.getReceiverPartyElement(), config);
        if (receiverId != null && !receiverId.trim().isEmpty()) {
            metadata.setReceiverParticipantId(
                    PeppolIdentifierFactory.INSTANCE.createParticipantIdentifierWithDefaultScheme(receiverId)
            );
            metadata.setReceiverSchemeId(receiverSchemeId);
            LOGGER.info("👥 Extracted receiver participant ID: {} with scheme: {}", receiverId, receiverSchemeId);
        } else {
            LOGGER.warn("⚠️ No receiver participant ID found in UBL XML");
        }

        // Ensure we have participant IDs - use country-specific defaults if not found
        if (metadata.getSenderParticipantId() == null) {
            String defaultSenderId = config.getDefaultSenderParticipantId();
            LOGGER.warn("⚠️ Using default sender participant ID: {}", defaultSenderId);
            metadata.setSenderParticipantId(
                    PeppolIdentifierFactory.INSTANCE.createParticipantIdentifierWithDefaultScheme(defaultSenderId)
            );
        }

        if (metadata.getReceiverParticipantId() == null) {
            String defaultReceiverId = config.getDefaultReceiverParticipantId();
            LOGGER.warn("⚠️ Using default receiver participant ID: {}", defaultReceiverId);
            metadata.setReceiverParticipantId(
                    PeppolIdentifierFactory.INSTANCE.createParticipantIdentifierWithDefaultScheme(defaultReceiverId)
            );
        }

        // Extract document type ID using configuration
        NodeList customizationIds = doc.getElementsByTagName(config.getCustomizationIdElement());
        if (customizationIds.getLength() > 0) {
            String customizationId = customizationIds.item(0).getTextContent();
            // Use country and document-specific document type ID
            String documentTypeId = config.getDefaultDocumentTypeId();
            metadata.setDocumentTypeId(
                    PeppolIdentifierFactory.INSTANCE.createDocumentTypeIdentifierWithDefaultScheme(documentTypeId)
            );
            LOGGER.info("📋 Using document type ID: {}", documentTypeId);
        }

        // Extract process ID using configuration
        NodeList profileIds = doc.getElementsByTagName(config.getProfileIdElement());
        if (profileIds.getLength() > 0) {
            String profileId = profileIds.item(0).getTextContent();
            metadata.setProcessId(
                    PeppolIdentifierFactory.INSTANCE.createProcessIdentifierWithDefaultScheme(profileId)
            );
        }

        // Extract invoice ID using configuration
        NodeList invoiceIds = doc.getElementsByTagName(config.getInvoiceIdElement());
        if (invoiceIds.getLength() > 0) {
            metadata.setInvoiceId(invoiceIds.item(0).getTextContent());
        }

        // Extract ProfileExecutionID using configuration
        NodeList profileExecutionIds = doc.getElementsByTagName(config.getProfileExecutionIdElement());
        if (profileExecutionIds.getLength() > 0) {
            String profileExecutionId = profileExecutionIds.item(0).getTextContent();
            metadata.setProfileExecutionId(profileExecutionId);
            LOGGER.info("📋 Extracted ProfileExecutionID: {}", profileExecutionId);
        } else {
            String defaultProfileExecutionId = config.getDefaultProfileExecutionId();
            metadata.setProfileExecutionId(defaultProfileExecutionId);
            LOGGER.info("📋 Using default ProfileExecutionID: {}", defaultProfileExecutionId);
        }

        // Extract IssueDate using configuration
        NodeList issueDates = doc.getElementsByTagName(config.getIssueDateElement());
        if (issueDates.getLength() > 0) {
            String issueDate = issueDates.item(0).getTextContent();
            metadata.setIssueDate(issueDate);
            LOGGER.info("📅 Extracted IssueDate: {}", issueDate);
        } else {
            // Use current date as fallback
            String currentDate = java.time.LocalDate.now().toString();
            metadata.setIssueDate(currentDate);
            LOGGER.info("📅 Using current date as IssueDate: {}", currentDate);
        }

        // Use country code from configuration (already determined above)
        metadata.setCountryCode(config.getDefaultCountryCode());

        // Generate message and conversation IDs using configurable prefixes
        metadata.setMessageId(config.getMessageIdPrefix() + System.currentTimeMillis());
        metadata.setConversationId(config.getConversationIdPrefix() + metadata.getInvoiceId());

        LOGGER.info("✅ AS4 metadata extracted successfully");
        LOGGER.info("📄 Invoice ID: {}", metadata.getInvoiceId());
        LOGGER.info("📊 Message ID: {}", metadata.getMessageId());
        LOGGER.info("👤 Sender: {}", metadata.getSenderParticipantId());
        LOGGER.info("👥 Receiver: {}", metadata.getReceiverParticipantId());
        LOGGER.info("📋 Document Type: {}", metadata.getDocumentTypeId());
        LOGGER.info("⚙️ Process: {}", metadata.getProcessId());
        LOGGER.info("🌍 Country Code: {}", metadata.getCountryCode());

        return metadata;
    }

    /**
     * Determine document type from XML root element using configurable mapping
     */
    private String determineDocumentType(Document doc, XmlParsingConfig config) {
        String rootElementName = doc.getDocumentElement().getLocalName();
        if (rootElementName == null) {
            rootElementName = doc.getDocumentElement().getNodeName();
        }

        // Use configurable document type mapping
        String documentType = config.getDocumentTypeFromRootElement(rootElementName);
        if (documentType.equals(config.getDefaultDocumentType()) && !rootElementName.equals("unknown")) {
            LOGGER.warn("⚠️ Unknown document type: {}, defaulting to {}", rootElementName, documentType);
        }

        return documentType;
    }

    /**
     * Extract participant ID from specific party element using configurable element names
     */
    private String extractParticipantId(Document doc, String partyElementName, XmlParsingConfig config) {
        try {
            // Look for the party element using configurable prefix
            NodeList partyElements = doc.getElementsByTagName(config.getPartyElementPrefix() + partyElementName);
            if (partyElements.getLength() > 0) {
                Element partyElement = (Element) partyElements.item(0);

                // Look for EndpointID within this party using configurable element name
                NodeList endpointIds = partyElement.getElementsByTagName(config.getEndpointIdElement());
                if (endpointIds.getLength() > 0) {
                    String participantId = endpointIds.item(0).getTextContent();
                    if (participantId != null && !participantId.trim().isEmpty()) {
                        LOGGER.debug("Found participant ID in {}: {}", partyElementName, participantId);
                        return participantId.trim();
                    }
                }

                // Fallback: Look for PartyIdentification/ID using configurable element name
                NodeList partyIds = partyElement.getElementsByTagName(config.getPartyIdElement());
                if (partyIds.getLength() > 0) {
                    String participantId = partyIds.item(0).getTextContent();
                    if (participantId != null && !participantId.trim().isEmpty()) {
                        LOGGER.debug("Found participant ID (fallback) in {}: {}", partyElementName, participantId);
                        return participantId.trim();
                    }
                }
            }

            LOGGER.warn("⚠️ No participant ID found in {}", partyElementName);
            return null;

        } catch (Exception e) {
            LOGGER.error("❌ Error extracting participant ID from {}: {}", partyElementName, e.getMessage());
            return null;
        }
    }

    /**
     * Extract scheme ID from EndpointID element using configurable element names
     */
    private String extractSchemeId(Document doc, String partyElementName, XmlParsingConfig config) {
        try {
            // Look for the party element using configurable prefix
            NodeList partyElements = doc.getElementsByTagName(config.getPartyElementPrefix() + partyElementName);
            if (partyElements.getLength() > 0) {
                Element partyElement = (Element) partyElements.item(0);

                // Look for EndpointID within this party using configurable element name
                NodeList endpointIds = partyElement.getElementsByTagName(config.getEndpointIdElement());
                if (endpointIds.getLength() > 0) {
                    Element endpointElement = (Element) endpointIds.item(0);
                    String schemeId = endpointElement.getAttribute(config.getSchemeIdAttribute());
                    if (schemeId != null && !schemeId.trim().isEmpty()) {
                        LOGGER.debug("Found scheme ID in {}: {}", partyElementName, schemeId);
                        return schemeId.trim();
                    }
                }
            }

            LOGGER.debug("No scheme ID found in {}", partyElementName);
            return null;

        } catch (Exception e) {
            LOGGER.error("❌ Error extracting scheme ID from {}: {}", partyElementName, e.getMessage());
            return null;
        }
    }

    /**
     * Extract country code from XML document
     * Looks for COUNTRY_C1 in BusinessScope or country code in address elements
     */
    private String extractCountryCodeFromXml(Document doc) {
        // Get a temporary config to access default values
        XmlParsingConfig tempConfig = countryConfigurationService.getXmlParsingConfig("DEFAULT", "INVOICE");

        // Get global defaults for element names using configurable defaults
        XmlParsingConfig globalConfig =
            countryConfigurationService.getXmlParsingConfig(
                tempConfig.getGlobalDefaultCountryCode(),
                tempConfig.getDefaultDocumentType());

        try {
            // First try to find COUNTRY_C1 in BusinessScope (SBDH format)
            NodeList scopes = doc.getElementsByTagName(globalConfig.getScopeElement());
            for (int i = 0; i < scopes.getLength(); i++) {
                Element scope = (Element) scopes.item(i);
                NodeList types = scope.getElementsByTagName(globalConfig.getTypeElement());
                if (types.getLength() > 0 && globalConfig.getCountryC1Type().equals(types.item(0).getTextContent())) {
                    NodeList instanceIds = scope.getElementsByTagName(globalConfig.getInstanceIdentifierElement());
                    if (instanceIds.getLength() > 0) {
                        String countryCode = instanceIds.item(0).getTextContent();
                        if (countryCode != null && !countryCode.trim().isEmpty()) {
                            LOGGER.info("🌍 Found country code in BusinessScope: {}", countryCode);
                            return countryCode.trim();
                        }
                    }
                }
            }

            // Second try: Look for country code in postal address elements
            NodeList countryElements = doc.getElementsByTagName(globalConfig.getCountryCodeElement());
            for (int i = 0; i < countryElements.getLength(); i++) {
                Element countryElement = (Element) countryElements.item(i);
                // Check if this is within a Country element using configurable parent element name
                if (countryElement.getParentNode() != null &&
                    countryElement.getParentNode().getNodeName().contains(globalConfig.getCountryParentElement())) {
                    String countryCode = countryElement.getTextContent();
                    if (countryCode != null && !countryCode.trim().isEmpty()) {
                        LOGGER.info("🌍 Found country code in address: {}", countryCode);
                        return countryCode.trim();
                    }
                }
            }

            // Third try: Look for any country-related elements
            NodeList countrySubentities = doc.getElementsByTagName(globalConfig.getCountrySubentityElement());
            if (countrySubentities.getLength() > 0) {
                // If we have country subentity, try to infer country from common patterns
                String subentity = countrySubentities.item(0).getTextContent();
                if (subentity != null) {
                    // This is a fallback - in real scenarios you'd have proper country mapping
                    LOGGER.info("🌍 Found country subentity, using default country code");
                }
            }

            LOGGER.warn("⚠️ No country code found in XML document");
            return null;

        } catch (Exception e) {
            LOGGER.error("❌ Error extracting country code from XML: {}", e.getMessage());
            return null;
        }
    }

    /**
     * Create custom SBDH with specific values as per requirements using configurable settings
     */
    private String createCustomSBDH(String ublXml, AS4MessageMetadata metadata) {
        // Get configuration for SBDH creation
        String documentType = determineDocumentTypeFromMetadata(metadata);
        XmlParsingConfig config =
            countryConfigurationService.getXmlParsingConfig(metadata.getCountryCode(), documentType);
        SbdhConfiguration sbdhConfig = config.getSbdhConfiguration();
        try {
            // Remove XML declaration if exists
            String cleanedUblXml = ublXml.replaceFirst("<\\?xml.*\\?>", "").trim();

            DocumentBuilderFactory factory = DocumentBuilderFactory.newInstance();
            factory.setNamespaceAware(true);
            DocumentBuilder builder = factory.newDocumentBuilder();
            Document doc = builder.newDocument();

            // Root element: StandardBusinessDocument
            Element sbd = doc.createElementNS("http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader", "StandardBusinessDocument");
            sbd.setAttributeNS("http://www.w3.org/2000/xmlns/", "xmlns:sh", "http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader");
            doc.appendChild(sbd);

            // Header
            Element sbdh = doc.createElementNS("http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader", "sh:StandardBusinessDocumentHeader");
            sbd.appendChild(sbdh);

            // Header Version using configuration
            Element headerVersion = doc.createElementNS("http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader", "sh:HeaderVersion");
            headerVersion.setTextContent(sbdhConfig.getHeaderVersion());
            sbdh.appendChild(headerVersion);

            // Sender with formatted participant ID (scheme:id)
            Element sender = doc.createElementNS("http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader", "sh:Sender");
            Element senderId = doc.createElementNS("http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader", "sh:Identifier");
            senderId.setAttribute("Authority", sbdhConfig.getAuthority());
            senderId.setTextContent(metadata.getFormattedSenderParticipantId());
            sender.appendChild(senderId);
            sbdh.appendChild(sender);

            // Receiver with formatted participant ID (scheme:id)
            Element receiver = doc.createElementNS("http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader", "sh:Receiver");
            Element receiverId = doc.createElementNS("http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader", "sh:Identifier");
            receiverId.setAttribute("Authority", sbdhConfig.getAuthority());
            receiverId.setTextContent(metadata.getFormattedReceiverParticipantId());
            receiver.appendChild(receiverId);
            sbdh.appendChild(receiver);

            // Document Identification with configurable values
            Element docId = doc.createElementNS("http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader", "sh:DocumentIdentification");

            Element standard = doc.createElementNS("http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader", "sh:Standard");
            standard.setTextContent(sbdhConfig.getStandard());
            docId.appendChild(standard);

            Element typeVersion = doc.createElementNS("http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader", "sh:TypeVersion");
            typeVersion.setTextContent(sbdhConfig.getTypeVersion());
            docId.appendChild(typeVersion);

            Element instanceId = doc.createElementNS("http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader", "sh:InstanceIdentifier");
            // Use ProfileExecutionID + Invoice ID as instance identifier
//            String instanceIdentifier = metadata.getProfileExecutionId() + "-" + metadata.getInvoiceId();
//            instanceId.setTextContent(instanceIdentifier);
            // Use UUID as instance identifier
            String instanceIdentifier = UUID.randomUUID().toString();
            instanceId.setTextContent(instanceIdentifier);
            docId.appendChild(instanceId);


            Element type = doc.createElementNS("http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader", "sh:Type");
            type.setTextContent(sbdhConfig.getDocumentType());
            docId.appendChild(type);

            Element creationDate = doc.createElementNS("http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader", "sh:CreationDateAndTime");
            // Use IssueDate with current time
            String creationDateTime = LocalDate.now().toString() + "T" +
                    LocalTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss"));
            creationDate.setTextContent(creationDateTime);
            docId.appendChild(creationDate);

            sbdh.appendChild(docId);

            // Business Scope with COUNTRY_C1
            Element businessScope = doc.createElementNS("http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader", "sh:BusinessScope");

            // Document ID scope using configuration
            Element docScope = createSBDHScope(doc, sbdhConfig.getDocumentIdScopeType(),
                metadata.getDocumentTypeId().getValue(), sbdhConfig.getDocumentIdScopeIdentifier());
            businessScope.appendChild(docScope);

            // Process ID scope using configuration
            Element processScope = createSBDHScope(doc, sbdhConfig.getProcessIdScopeType(),
                metadata.getProcessId().getValue(), sbdhConfig.getProcessIdScopeIdentifier());
            businessScope.appendChild(processScope);

            // Country scope using configuration
            Element countryScope = createSBDHScope(doc, config.getCountryC1Type(), metadata.getCountryCode(), null);
            businessScope.appendChild(countryScope);

            sbdh.appendChild(businessScope);

            // Parse and append UBL invoice
            Document ublDoc = builder.parse(new ByteArrayInputStream(cleanedUblXml.getBytes(StandardCharsets.UTF_8)));
            Element invoiceElement = ublDoc.getDocumentElement();
            sbd.appendChild(doc.importNode(invoiceElement, true));

            // Convert to string
            TransformerFactory tf = TransformerFactory.newInstance();
            Transformer transformer = tf.newTransformer();
            transformer.setOutputProperty(OutputKeys.INDENT, "yes");
            StringWriter writer = new StringWriter();
            transformer.transform(new DOMSource(doc), new StreamResult(writer));

            String result = writer.toString();
            LOGGER.info("✅ Custom SBDH created with:");
            LOGGER.info("   📋 Instance ID: {}", instanceIdentifier);
            LOGGER.info("   📅 Creation Date: {}", creationDateTime);
            LOGGER.info("   👤 Sender: {}", metadata.getFormattedSenderParticipantId());
            LOGGER.info("   👥 Receiver: {}", metadata.getFormattedReceiverParticipantId());
            LOGGER.info("   🌍 Country: {}", metadata.getCountryCode());

            return result;

        } catch (Exception e) {
            LOGGER.error("❌ Failed to create custom SBDH: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to create custom SBDH", e);
        }
    }

    /**
     * Determine document type from metadata for SBDH creation using configurable mapping
     */
    private String determineDocumentTypeFromMetadata(AS4MessageMetadata metadata) {
        XmlParsingConfig tempConfig = countryConfigurationService.getXmlParsingConfig("DEFAULT", "INVOICE");

        if (metadata.getDocumentTypeId() != null) {
            String docTypeValue = metadata.getDocumentTypeId().getValue().toLowerCase();

            // Check against configurable document type mapping
            if (docTypeValue.contains("invoice")) {
                return tempConfig.getDocumentTypeFromRootElement("invoice");
            } else if (docTypeValue.contains("creditnote")) {
                return tempConfig.getDocumentTypeFromRootElement("creditnote");
            } else if (docTypeValue.contains("applicationresponse")) {
                return tempConfig.getDocumentTypeFromRootElement("applicationresponse");
            }
        }
        return tempConfig.getDefaultDocumentType(); // Configurable default fallback
    }

    private Element createSBDHScope(Document doc, String type, String instanceId, String identifier) {
        Element scope = doc.createElementNS("http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader", "sh:Scope");

        Element typeElement = doc.createElementNS("http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader", "sh:Type");
        typeElement.setTextContent(type);
        scope.appendChild(typeElement);

        Element instanceElement = doc.createElementNS("http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader", "sh:InstanceIdentifier");
        instanceElement.setTextContent(instanceId);
        scope.appendChild(instanceElement);

        if (identifier != null) {
            Element identifierElement = doc.createElementNS("http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader", "sh:Identifier");
            identifierElement.setTextContent(identifier);
            scope.appendChild(identifierElement);
        }

        return scope;
    }

    /**
     * Get configurable message ID with prefix
     */
    private String getConfigurableMessageId() {
        XmlParsingConfig tempConfig = countryConfigurationService.getXmlParsingConfig("DEFAULT", "INVOICE");
        return tempConfig.getMessageIdPrefix() + System.currentTimeMillis();
    }

    /**
     * Get configurable conversation ID with prefix
     */
    private String getConfigurableConversationId() {
        XmlParsingConfig tempConfig = countryConfigurationService.getXmlParsingConfig("DEFAULT", "INVOICE");
        return tempConfig.getConversationIdPrefix() + System.currentTimeMillis();
    }

    public AS4ConversionResult sendAS4Message(String xml, AS4MessageMetadata metadata, AS4ConversionResult result) {
        LOGGER.info("🏭 Sending AS4 message in {} mode", as4Mode);



        try {
            LOGGER.info("🌍 Setting country code for SBDH: {}", metadata.getCountryCode());
            LOGGER.info("👤 From Party ID: {}", metadata.getSenderParticipantId().getValue());
            LOGGER.info("👥 To Party ID: {}", metadata.getReceiverParticipantId().getValue());

            // Create custom SBDH with extracted values
            String customSbdhXml = createCustomSBDH(xml, metadata);
            LOGGER.info("Forward flow xml : {}",customSbdhXml);


            // Use sbdhBuilder() for pre-built SBDH as per Phase4 documentation
            result=processAs4message(metadata, result, customSbdhXml,endpointUrl);

            return result;

        } catch (Exception e) {
            LOGGER.error("❌ Production AS4 send failed", e);
            result.setSuccess(false);
            result.setErrorMessage("Production AS4 send failed: " + e.getMessage());
            return result;
        }
    }

    public AS4ConversionResult processAs4message(AS4MessageMetadata metadata, AS4ConversionResult result, String customSbdhXml,String endpointUrl) {
        Phase4PeppolSender.SBDHBuilder builder = Phase4PeppolSender.sbdhBuilder()
                .documentTypeID(metadata.getDocumentTypeId())
                .processID(metadata.getProcessId())
                .senderParticipantID(metadata.getSenderParticipantId())
                .receiverParticipantID(metadata.getReceiverParticipantId())
                .fromPartyID(metadata.getSenderParticipantId().getValue())  // Set from party ID
                .toPartyID(metadata.getReceiverParticipantId().getValue())  // Set to party ID
                .countryC1(metadata.getCountryCode())  // Set country code for SBDH
                .cryptoFactory(as4CryptoFactory)  // Set crypto factory for signing
                .payload(customSbdhXml.getBytes(StandardCharsets.UTF_8))
                .buildMessageCallback(new IAS4ClientBuildMessageCallback() {
                    public void onAS4Message(@Nonnull AS4UserMessage aAS4UserMessage) {
                        LOGGER.debug("✅ AS4 message built successfully with country code: {}", metadata.getCountryCode());
                        LOGGER.debug("📊 Message ID: {}", result.getAs4MessageId());
                    }
                });

        // Configure crypto settings for the sender
        builder = cryptoConfiguration.configurePhase4PeppolSender(builder);

        // Debug crypto factory status
        LOGGER.info("🔐 Crypto factory configured: {}", as4CryptoFactory != null ? "✅ Available" : "❌ Missing");
        LOGGER.info("🔐 Keystore info: {}", cryptoConfiguration.getKeystoreInfo());

        LOGGER.info("🏭 PRODUCTION MODE: Using real Peppol network");

        // Production mode: Use SMP discovery (no direct endpoint)
        // This is the correct way for live Peppol network
        if ("production".equals(as4Mode)) {
            LOGGER.info("🌐 REAL PRODUCTION: Using SMP discovery (no direct endpoint)");
            // For real production: NO endpointURL, NO endpointDetailProvider
            // Phase4 will automatically discover via SMP

        } else {
            // For test/dummy modes with production sender: allow direct endpoint
            if (endpointUrl != null && !endpointUrl.isEmpty() &&
                    !endpointUrl.equals("${as4.endpoint.url}")) {

                LOGGER.warn("⚠️ {} MODE with direct endpoint - FOR TESTING ONLY", as4Mode.toUpperCase());
                LOGGER.info("🌐 Using direct endpoint: {}", endpointUrl);
                builder.endpointURL(endpointUrl);

                // For dummy mode, use TrustAllEndpointDetailProvider to bypass certificate validation
                if ("dummy".equals(as4Mode)) {
                    LOGGER.warn("⚠️ DUMMY MODE: Using direct endpoint with certificate validation BYPASSED");
                    // Pass the full keystore path (base path + file name) to TrustAllEndpointDetailProvider
                    String fullKeystorePath = keystoreBasePath.endsWith("/") ?
                        keystoreBasePath + keystorePath : keystoreBasePath + "/" + keystorePath;
                    TrustAllEndpointDetailProvider trustAllProvider = new TrustAllEndpointDetailProvider(
                            endpointUrl, fullKeystorePath, keystorePassword, keyAlias);
                    builder.endpointDetailProvider(trustAllProvider);
                    LOGGER.info("✅ Certificate validation BYPASSED for development/testing");
                } else {
                    // For production mode with direct endpoint (should not happen in real production)
                    LOGGER.warn("⚠️ Production mode with direct endpoint - this should only be used for testing");
                    SimpleEndpointDetailProvider endpointProvider = new SimpleEndpointDetailProvider(endpointUrl);
                    builder.endpointDetailProvider(endpointProvider);
                    LOGGER.info("✅ Using SimpleEndpointDetailProvider for production mode");
                }
            } else {
                LOGGER.info("🌐 Using SMP discovery for endpoint resolution");
            }
        }

        // Send the AS4 message
        ESimpleUserMessageSendResult sendResult = builder.sendMessageAndCheckForReceipt();

        if (sendResult == ESimpleUserMessageSendResult.SUCCESS) {
            result.setSuccess(true);
            result.setMessage("✅ Production AS4 message sent successfully");
            result.setEndpointUsed("production".equals(as4Mode) ? "SMP Discovery" : endpointUrl);

            LOGGER.info("✅ Production AS4 send successful");
            LOGGER.info("📊 Transaction ID: {}", result.getAs4MessageId());
            LOGGER.info("🌐 Endpoint: {}", result.getEndpointUsed());
        } else {
            result.setSuccess(false);
            result.setErrorMessage("Production AS4 send failed: " + sendResult);
            LOGGER.error("❌ Production AS4 send failed: {}", sendResult);
        }
        return result;
    }

    private String createAS4MessageStructure(String xml, AS4MessageMetadata metadata) {
        String timestamp = LocalDateTime.now().toString();

        return String.format("""
            <?xml version="1.0" encoding="UTF-8"?>
            <soap:Envelope xmlns:soap="http://schemas.xmlsoap.org/soap/envelope/"
                           xmlns:eb="http://docs.oasis-open.org/ebxml-msg/ebms/v3.0/ns/core/200704/">
              <soap:Header>
                <eb:Messaging>
                  <eb:UserMessage>
                    <eb:MessageInfo>
                      <eb:Timestamp>%s</eb:Timestamp>
                      <eb:MessageId>%s</eb:MessageId>
                      <eb:ConversationId>%s</eb:ConversationId>
                    </eb:MessageInfo>
                    <eb:PartyInfo>
                      <eb:From>
                        <eb:PartyId type="urn:oasis:names:tc:ebcore:partyid-type:unregistered">%s</eb:PartyId>
                      </eb:From>
                      <eb:To>
                        <eb:PartyId type="urn:oasis:names:tc:ebcore:partyid-type:unregistered">%s</eb:PartyId>
                      </eb:To>
                    </eb:PartyInfo>
                    <eb:CollaborationInfo>
                      <eb:Service>%s</eb:Service>
                      <eb:Action>http://docs.oasis-open.org/ebxml-msg/ebms/v3.0/ns/core/200704/test</eb:Action>
                      <eb:ConversationId>%s</eb:ConversationId>
                    </eb:CollaborationInfo>
                    <eb:PayloadInfo>
                      <eb:PartInfo href="cid:invoice@morohub.as4">
                        <eb:PartProperties>
                          <eb:Property name="MimeType">application/xml</eb:Property>
                        </eb:PartProperties>
                      </eb:PartInfo>
                    </eb:PayloadInfo>
                  </eb:UserMessage>
                </eb:Messaging>
              </soap:Header>
              <soap:Body>
                <!-- UBL Invoice payload attached as MIME part -->
              </soap:Body>
            </soap:Envelope>

            MIME Attachment:
            Content-ID: <invoice@morohub.as4>
            Content-Type: application/xml

            %s
            """,
                timestamp,
                metadata.getMessageId() != null ? metadata.getMessageId() : getConfigurableMessageId(),
                metadata.getConversationId() != null ? metadata.getConversationId() : getConfigurableConversationId(),
                metadata.getSenderParticipantId() != null ? metadata.getSenderParticipantId().getValue() : "unknown-sender",
                metadata.getReceiverParticipantId() != null ? metadata.getReceiverParticipantId().getValue() : "unknown-receiver",
                metadata.getDocumentTypeId() != null ? metadata.getDocumentTypeId().getValue() : "unknown-doctype",
                metadata.getConversationId() != null ? metadata.getConversationId() : getConfigurableConversationId(),
                xml.length() > 500 ? xml.substring(0, 500) + "..." : xml
        );
    }

    private void checkKeystoreAvailability() {
        LOGGER.info("=== Checking Keystore Availability ===");

        // Check keystore using configurable resource loader
        Resource keystoreResource = resourceLoader.loadResource(keystoreBasePath, keystorePath);
        if (keystoreResource.exists()) {
            LOGGER.info("✅ Keystore found at: {}",
                resourceLoader.getResourceLocation(keystoreBasePath + keystorePath));

            if (securityEnabled) {
                try {
                    // Test keystore access
                    validateKeystore();
                    LOGGER.info("✅ Keystore validation successful");
                } catch (Exception e) {
                    LOGGER.warn("⚠️ Keystore validation failed: {}", e.getMessage());
                }
            }
        } else {
            LOGGER.warn("⚠️ Keystore not found at: {}",
                resourceLoader.getResourceLocation(keystoreBasePath + keystorePath));
            LOGGER.info("💡 Run generate-certificates.bat to create dummy certificates");
        }

        // Check truststore using configurable resource loader
        Resource truststoreResource = resourceLoader.loadResource(keystoreBasePath, truststorePath);
        if (truststoreResource.exists()) {
            LOGGER.info("✅ Truststore found at: {}",
                resourceLoader.getResourceLocation(keystoreBasePath + truststorePath));
        } else {
            LOGGER.warn("⚠️ Truststore not found at: {}",
                resourceLoader.getResourceLocation(keystoreBasePath + truststorePath));
        }
    }

    private void validateKeystore() throws Exception {
        Resource keystoreResource = resourceLoader.loadResource(keystoreBasePath, keystorePath);
        try (InputStream is = keystoreResource.getInputStream()) {
            KeyStore keystore = KeyStore.getInstance("PKCS12");
            keystore.load(is, keystorePassword.toCharArray());

            if (!keystore.containsAlias(keyAlias)) {
                throw new Exception("Key alias '" + keyAlias + "' not found in keystore");
            }

            // Test private key access
            keystore.getKey(keyAlias, keyPassword.toCharArray());
            LOGGER.info("✅ Private key accessible for alias: {}", keyAlias);
        }
    }

    /**
     * Get AS4 system status including keystore information
     */
    public String getSystemStatus() {
        Resource keystoreResource = resourceLoader.loadResource(keystoreBasePath, keystorePath);
        Resource truststoreResource = resourceLoader.loadResource(keystoreBasePath, truststorePath);

        String cryptoStatus = cryptoConfiguration != null && cryptoConfiguration.isCryptoConfigured()
                ? "✅ Configured" : "❌ Not Configured";

        String endpointInfo = "production".equals(as4Mode) ? "SMP Discovery" : endpointUrl;

        return String.format(
                "AS4 System Status: Mode=%s, Security=%s, Validation=%s, Endpoint=%s, Keystore=%s, Truststore=%s, Crypto=%s",
                as4Mode,
                securityEnabled,
                validationEnabled,
                endpointInfo,
                keystoreResource.exists() ? "✅ Available" : "❌ Missing",
                truststoreResource.exists() ? "✅ Available" : "❌ Missing",
                cryptoStatus
        );
    }
}
