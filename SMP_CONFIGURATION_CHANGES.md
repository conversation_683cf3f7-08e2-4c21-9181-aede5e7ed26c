# SMP Configuration Changes - Phase4 Automatic Discovery

## Overview
This document describes the changes made to implement configurable ESML DNS values for Phase4 automatic SMP discovery, supporting both dummy (test) and production modes.

## Key Changes

### 1. New SMPConfiguration Class
- **File**: `src/main/java/com/morohub/apsp/config/SMPConfiguration.java`
- **Purpose**: Centralized configuration for SMP discovery with ESML DNS values
- **Features**:
  - Configurable ESML DNS values for production and test environments
  - Simplified 2-mode approach: dummy (test) and production
  - Integration with Phase4 automatic SMP discovery

### 2. Configuration Properties

#### Base Configuration (`application.properties`)
```properties
# ESML (Service Metadata Locator) Configuration
peppol.esml.dns.production=edelivery.tech.ec.europa.eu.
peppol.esml.dns.test=acc.edelivery.tech.ec.europa.eu.
```

#### Dummy Mode (`application-dummy.properties`)
- Uses test ESML DNS: `acc.edelivery.tech.ec.europa.eu.`
- Supports direct endpoint for testing
- Certificate validation bypassed for development

#### Production Mode (`application-production.properties`)
- Uses production ESML DNS: `edelivery.tech.ec.europa.eu.`
- Automatic SMP discovery via Phase4
- Strict certificate validation

### 3. AS4ConversionService Updates
- **File**: `src/main/java/com/morohub/apsp/core/service/AS4ConversionService.java`
- **Changes**:
  - Integrated SMPConfiguration for centralized SMP settings
  - Simplified logic for 2 modes (dummy/production)
  - Phase4 automatic SMP discovery when no direct endpoint provided

### 4. SMPLookupService Updates
- **File**: `src/main/java/com/morohub/apsp/core/service/SMPLookupService.java`
- **Changes**:
  - Added deprecation notice - Phase4 handles SMP discovery automatically
  - Kept for legacy/manual lookup scenarios
  - Simplified bypass logic

## How It Works

### Production Mode
1. `as4.mode=production` is set
2. Phase4 uses automatic SMP discovery
3. ESML DNS: `edelivery.tech.ec.europa.eu.`
4. No direct endpoint URL provided
5. Phase4 discovers recipient endpoints via SMP

### Dummy Mode (Test)
1. `as4.mode=dummy` is set
2. Two options:
   - **Direct Endpoint**: Use `as4.endpoint.url` for testing
   - **SMP Discovery**: Phase4 uses test ESML DNS `acc.edelivery.tech.ec.europa.eu.`

## Benefits

### 1. Simplified Configuration
- Only 2 modes instead of complex discovery modes
- Clear separation between test and production environments
- Configurable DNS values for different Peppol networks

### 2. Phase4 Integration
- Leverages Phase4's built-in SMP discovery capabilities
- Reduces custom SMP lookup code
- Better alignment with Phase4 best practices

### 3. Flexibility
- Easy to switch between test and production ESML DNS
- Support for custom SMP URLs when needed
- Maintains backward compatibility

## Configuration Examples

### For Testing with Direct Endpoint
```properties
as4.mode=dummy
as4.endpoint.url=http://localhost:8081/test-endpoint
```

### For Testing with SMP Discovery
```properties
as4.mode=dummy
# as4.endpoint.url=  # Comment out for SMP discovery
# Will use acc.edelivery.tech.ec.europa.eu. for SMP lookup
```

### For Production
```properties
as4.mode=production
# as4.endpoint.url=  # No direct endpoint in production
# Will use edelivery.tech.ec.europa.eu. for SMP lookup
```

## Migration Notes

### From Previous Implementation
1. Remove complex `smp.discovery.mode` configurations
2. Set appropriate `as4.mode` (dummy or production)
3. Configure ESML DNS values if different from defaults
4. Phase4 will handle SMP discovery automatically

### Testing
- Use dummy mode with direct endpoint for local testing
- Use dummy mode without endpoint for integration testing with test SMP
- Use production mode for live Peppol network

## Technical Details

### ESML DNS Values
- **Production**: `edelivery.tech.ec.europa.eu.` - Official Peppol production SML
- **Test**: `acc.edelivery.tech.ec.europa.eu.` - Official Peppol test SML

### Phase4 Behavior
- When no `endpointURL` is provided, Phase4 automatically performs SMP discovery
- Phase4 uses the configured ESML DNS for SMP lookup
- Certificate validation follows the configured mode (strict for production, relaxed for dummy)

This implementation provides a clean, maintainable approach to SMP configuration while leveraging Phase4's automatic discovery capabilities.
