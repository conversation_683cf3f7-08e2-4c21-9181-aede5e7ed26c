package com.morohub.apsp.core.service;

import com.helger.peppolid.factory.PeppolIdentifierFactory;
import com.helger.peppolid.IParticipantIdentifier;
import com.helger.peppolid.IDocumentTypeIdentifier;
import com.helger.peppolid.IProcessIdentifier;
import com.helger.phase4.dynamicdiscovery.AS4EndpointDetailProviderPeppol;
import com.helger.phase4.dynamicdiscovery.IAS4EndpointDetailProvider;
import com.helger.smpclient.peppol.ISMPServiceMetadataProvider;
import com.helger.smpclient.peppol.SMPClientReadOnly;
import com.morohub.apsp.core.entity.MessageProcessingQueue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.net.URI;
import java.security.cert.X509Certificate;

import com.morohub.apsp.common.exception.AS4Exception;
import com.morohub.apsp.common.exception.ConnectivityException;
import com.morohub.apsp.core.entity.ErrorMasterData;

/**
 * Service for performing SMP (Service Metadata Publisher) lookups
 * to discover recipient endpoints
 */
@Service
public class SMPLookupService {

    private static final Logger logger = LoggerFactory.getLogger(SMPLookupService.class);
    @Value("${smp.lookup.bypass:false}")
    private boolean smpLookupBypass;

    @Value("${smp.lookup.bypass.url:}")
    private String smpBypassUrl;
    @Value("${peppol.smp.url:}")
    private String customSmpUrl;


    /**
     * Create participant identifier from string
     */
    private IParticipantIdentifier createParticipantIdentifier(String participantIdString) {
        try {
            // Handle different formats: "scheme::value" or "scheme:value" or just "value"
            if (participantIdString.contains("::")) {
                String[] parts = participantIdString.split("::", 2);
                return PeppolIdentifierFactory.INSTANCE.createParticipantIdentifierWithDefaultScheme(parts[1]);
            } else if (participantIdString.contains(":")) {
                String[] parts = participantIdString.split(":", 2);
                return PeppolIdentifierFactory.INSTANCE.createParticipantIdentifierWithDefaultScheme(parts[1]);
            } else {
                return PeppolIdentifierFactory.INSTANCE.createParticipantIdentifierWithDefaultScheme(participantIdString);
            }
        } catch (Exception e) {
            logger.warn("⚠️ Error parsing participant ID '{}', using default: {}", participantIdString, e.getMessage());
            return PeppolIdentifierFactory.INSTANCE.createParticipantIdentifierWithDefaultScheme("default");
        }
    }



    /**
     * Perform SMP lookup specifically for MLS message sending
     */


    public SMPLookupResult performMLSSMPLookup(String receiverParticipantId,String messageId) {
        try {
            logger.info("🔍 Performing MLS SMP lookup for participant: {}", receiverParticipantId);

            if (smpLookupBypass) {
                logger.warn("⚠️ SMP Lookup bypassed – using dummy URL: {}", smpBypassUrl);
                return SMPLookupResult.success(smpBypassUrl, null);
            }

            // Create Peppol identifiers for MLS
            IParticipantIdentifier participantId = createParticipantIdentifier(receiverParticipantId);
            IDocumentTypeIdentifier documentTypeId = createMLSDocumentTypeIdentifier();
            IProcessIdentifier processId = createMLSProcessIdentifier();

            logger.debug("📋 MLS SMP lookup parameters:");
            logger.debug("  - Participant ID: {}", participantId.getURIEncoded());
            logger.debug("  - Document Type: {}", documentTypeId.getURIEncoded());
            logger.debug("  - Process ID: {}", processId.getURIEncoded());

            // Create endpoint detail provider for SMP lookup
            IAS4EndpointDetailProvider endpointProvider = createEndpointDetailProvider();

            // Initialize the provider with lookup parameters
            endpointProvider.init(documentTypeId, processId, participantId);

            // Get endpoint URL
            String endpointUrl = endpointProvider.getReceiverAPEndpointURL();

            if (endpointUrl == null || endpointUrl.trim().isEmpty()) {
                logger.warn("⚠️ MLS SMP lookup returned empty endpoint URL for participant: {}", receiverParticipantId);
                throw ConnectivityException.dnsFailure("PEPPOL_001",
                    "MLS SMP lookup returned empty endpoint URL for participant: " + receiverParticipantId,
                    "SMP_ENDPOINT", messageId, null, null);
            }

            // Get certificate if available
            X509Certificate certificate = endpointProvider.getReceiverAPCertificate();

            logger.info("✅ MLS SMP lookup successful - Endpoint: {}", endpointUrl);
            logger.debug("🔐 Certificate available: {}", certificate != null ? "Yes" : "No");

            return SMPLookupResult.success(endpointUrl, certificate);

        } catch (Exception e) {
            logger.error("❌ MLS SMP lookup failed for participant {}: {}", receiverParticipantId, e.getMessage(), e);
            return SMPLookupResult.failure("MLS SMP lookup failed: " + e.getMessage());
        }
    }


    /**
     * Create MLS document type identifier
     */
    private IDocumentTypeIdentifier createMLSDocumentTypeIdentifier() {
        try {
            String mlsDocumentTypeId = "urn:oasis:names:specification:ubl:schema:xsd:ApplicationResponse-2::ApplicationResponse##urn:fdc:peppol.eu:poacc:trns:mls:3::2.1";
            return PeppolIdentifierFactory.INSTANCE.createDocumentTypeIdentifierWithDefaultScheme(mlsDocumentTypeId);
        } catch (Exception e) {
            logger.warn("⚠️ Error creating MLS document type identifier: {}", e.getMessage());
            throw new AS4Exception("PEPPOL_002", "Failed to create MLS document type identifier: " + e.getMessage(),
                ErrorMasterData.ErrorType.CONFIGURATION_ERROR,
                ErrorMasterData.ErrorSeverity.HIGH, false, null, null, e);
        }
    }

    /**
     * Create MLS process identifier
     */
    private IProcessIdentifier createMLSProcessIdentifier() {
        try {
            String mlsProcessId = "urn:fdc:peppol.eu:2017:poacc:mls:01:1.0";
            return PeppolIdentifierFactory.INSTANCE.createProcessIdentifierWithDefaultScheme(mlsProcessId);
        } catch (Exception e) {
            logger.warn("⚠️ Error creating MLS process identifier: {}", e.getMessage());
            throw new AS4Exception("PEPPOL_002", "Failed to create MLS process identifier: " + e.getMessage(),
                ErrorMasterData.ErrorType.CONFIGURATION_ERROR,
                ErrorMasterData.ErrorSeverity.HIGH, false, null, null, e);
        }
    }

    /**
     * Create endpoint detail provider for SMP lookup
     */
    private IAS4EndpointDetailProvider createEndpointDetailProvider() {
        try {
            ISMPServiceMetadataProvider smpClient;

            if (customSmpUrl != null && !customSmpUrl.trim().isEmpty()) {
                logger.debug("🌐 Using custom SMP URL: {}", customSmpUrl);
                smpClient = new SMPClientReadOnly(new URI(customSmpUrl));
            } else {
                // Use default Peppol SMP host URI (replace if you have a preferred one)
                URI defaultSmpUri = new URI("https://smp.peppolcentral.org");
                smpClient = new SMPClientReadOnly(defaultSmpUri);
            }

            return new AS4EndpointDetailProviderPeppol(smpClient);

        } catch (Exception e) {
            logger.error("❌ Error creating endpoint detail provider: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to create endpoint detail provider", e);
        }
    }
    /**
     * Result class for SMP lookup operations
     */
    public static class SMPLookupResult {
        private boolean success;
        private String endpointUrl;
        private X509Certificate certificate;
        private String errorMessage;

        public static SMPLookupResult success(String endpointUrl, X509Certificate certificate) {
            SMPLookupResult result = new SMPLookupResult();
            result.success = true;
            result.endpointUrl = endpointUrl;
            result.certificate = certificate;
            return result;
        }

        public static SMPLookupResult failure(String errorMessage) {
            SMPLookupResult result = new SMPLookupResult();
            result.success = false;
            result.errorMessage = errorMessage;
            return result;
        }

        // Getters
        public boolean isSuccess() { return success; }
        public String getEndpointUrl() { return endpointUrl; }
        public X509Certificate getCertificate() { return certificate; }
        public String getErrorMessage() { return errorMessage; }
    }
}
