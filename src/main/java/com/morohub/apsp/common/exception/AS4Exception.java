package com.morohub.apsp.common.exception;

import com.morohub.apsp.core.entity.ErrorMasterData;

/**
 * Base exception class for all AS4-related exceptions
 * Provides standardized error handling across the application
 */
public class AS4Exception extends RuntimeException {

    private final String errorCode;
    private final ErrorMasterData.ErrorType errorType;
    private final ErrorMasterData.ErrorSeverity severity;
    private final boolean retryable;
    private final String messageId;
    private final String requestId;

    public AS4Exception(String errorCode, String message) {
        super(message);
        this.errorCode = errorCode;
        this.errorType = ErrorMasterData.ErrorType.OTHERS;
        this.severity = ErrorMasterData.ErrorSeverity.MEDIUM;
        this.retryable = false;
        this.messageId = null;
        this.requestId = null;
    }

    public AS4Exception(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.errorType = ErrorMasterData.ErrorType.OTHERS;
        this.severity = ErrorMasterData.ErrorSeverity.MEDIUM;
        this.retryable = false;
        this.messageId = null;
        this.requestId = null;
    }

    public AS4Exception(String errorCode, String message, ErrorMasterData.ErrorType errorType) {
        super(message);
        this.errorCode = errorCode;
        this.errorType = errorType;
        this.severity = ErrorMasterData.ErrorSeverity.MEDIUM;
        this.retryable = false;
        this.messageId = null;
        this.requestId = null;
    }

    public AS4Exception(String errorCode, String message, ErrorMasterData.ErrorType errorType, 
                       ErrorMasterData.ErrorSeverity severity) {
        super(message);
        this.errorCode = errorCode;
        this.errorType = errorType;
        this.severity = severity;
        this.retryable = false;
        this.messageId = null;
        this.requestId = null;
    }

    public AS4Exception(String errorCode, String message, ErrorMasterData.ErrorType errorType, 
                       ErrorMasterData.ErrorSeverity severity, boolean retryable) {
        super(message);
        this.errorCode = errorCode;
        this.errorType = errorType;
        this.severity = severity;
        this.retryable = retryable;
        this.messageId = null;
        this.requestId = null;
    }

    public AS4Exception(String errorCode, String message, ErrorMasterData.ErrorType errorType, 
                       ErrorMasterData.ErrorSeverity severity, boolean retryable, 
                       String messageId, String requestId) {
        super(message);
        this.errorCode = errorCode;
        this.errorType = errorType;
        this.severity = severity;
        this.retryable = retryable;
        this.messageId = messageId;
        this.requestId = requestId;
    }

    public AS4Exception(String errorCode, String message, ErrorMasterData.ErrorType errorType, 
                       ErrorMasterData.ErrorSeverity severity, boolean retryable, 
                       String messageId, String requestId, Throwable cause) {
        super(message, cause);
        this.errorCode = errorCode;
        this.errorType = errorType;
        this.severity = severity;
        this.retryable = retryable;
        this.messageId = messageId;
        this.requestId = requestId;
    }

    // Getters
    public String getErrorCode() {
        return errorCode;
    }

    public ErrorMasterData.ErrorType getErrorType() {
        return errorType;
    }

    public ErrorMasterData.ErrorSeverity getSeverity() {
        return severity;
    }

    public boolean isRetryable() {
        return retryable;
    }

    public String getMessageId() {
        return messageId;
    }

    public String getRequestId() {
        return requestId;
    }

    @Override
    public String toString() {
        return String.format("AS4Exception{errorCode='%s', errorType=%s, severity=%s, retryable=%s, messageId='%s', requestId='%s', message='%s'}", 
                           errorCode, errorType, severity, retryable, messageId, requestId, getMessage());
    }
}
