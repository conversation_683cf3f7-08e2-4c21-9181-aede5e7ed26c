package com.morohub.apsp.api.controller;

import com.morohub.apsp.core.entity.MessageProcessingQueue;
import com.morohub.apsp.core.service.MessageProcessingService;
import com.morohub.apsp.core.service.MessageQueueService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

import com.morohub.apsp.common.exception.AS4Exception;
import com.morohub.apsp.core.entity.ErrorMasterData;

/**
 * REST Controller for message processing operations
 */
@RestController
@RequestMapping("/api/message-processing")
public class MessageProcessingController {

    private static final Logger logger = LoggerFactory.getLogger(MessageProcessingController.class);

    @Autowired
    private MessageProcessingService messageProcessingService;

    @Autowired
    private MessageQueueService messageQueueService;

    /**
     * Get processing statistics
     */
    @GetMapping("/statistics")
    public ResponseEntity<MessageProcessingService.ProcessingStatistics> getStatistics() {
        try {
            logger.debug("📊 Getting processing statistics");
            MessageProcessingService.ProcessingStatistics stats = messageProcessingService.getProcessingStatistics();
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            logger.error("❌ Error getting statistics: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get queue statistics
     */
    @GetMapping("/queue/statistics")
    public ResponseEntity<MessageQueueService.QueueStatistics> getQueueStatistics() {
        try {
            logger.debug("📊 Getting queue statistics");
            MessageQueueService.QueueStatistics stats = messageQueueService.getQueueStatistics();
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            logger.error("❌ Error getting queue statistics: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get messages ready for processing
     */
    @GetMapping("/queue/ready")
    public ResponseEntity<List<MessageProcessingQueue>> getMessagesReadyForProcessing() {
        try {
            logger.debug("📋 Getting messages ready for processing");
            List<MessageProcessingQueue> messages = messageQueueService.getMessagesReadyForProcessing();
            return ResponseEntity.ok(messages);
        } catch (Exception e) {
            logger.error("❌ Error getting ready messages: {}", e.getMessage(), e);

            // Throw AS4Exception for error management system
            throw new AS4Exception("SYS_001", "Failed to get ready messages: " + e.getMessage(),
                ErrorMasterData.ErrorType.SYSTEM_ERROR,
                ErrorMasterData.ErrorSeverity.MEDIUM, false, null, UUID.randomUUID().toString(), e);
        }
    }

    /**
     * Manually process a specific message
     */
    @PostMapping("/process/{messageId}")
    public ResponseEntity<Map<String, Object>> processMessage(@PathVariable Long messageId) {
        try {
            logger.info("🔄 Manual processing requested for message ID: {}", messageId);
            
            MessageProcessingService.ProcessingResult result = messageProcessingService.processMessageById(messageId);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", result.isSuccess());
            response.put("message", result.getMessage());
            response.put("messageId", messageId);
            
            if (result.isSuccess()) {
                logger.info("✅ Manual processing started for message: {}", messageId);
                return ResponseEntity.ok(response);
            } else {
                logger.warn("⚠️ Manual processing failed for message {}: {}", messageId, result.getMessage());
                return ResponseEntity.badRequest().body(response);
            }
            
        } catch (AS4Exception e) {
            logger.error("❌ AS4 error in manual processing: {}", e.getMessage(), e);
            throw e; // Re-throw for error management system
        } catch (Exception e) {
            logger.error("❌ Unexpected error in manual processing: {}", e.getMessage(), e);

            // Throw AS4Exception for error management system
            throw new AS4Exception("SYS_001", "Manual processing failed: " + e.getMessage(),
                ErrorMasterData.ErrorType.SYSTEM_ERROR,
                ErrorMasterData.ErrorSeverity.HIGH, false, messageId.toString(), UUID.randomUUID().toString(), e);
        }
    }

    /**
     * Enable/disable message processing
     */
    @PostMapping("/toggle")
    public ResponseEntity<Map<String, Object>> toggleProcessing(@RequestParam boolean enabled) {
        try {
            logger.info("🔧 Toggling message processing: {}", enabled ? "enabled" : "disabled");
            
            messageProcessingService.setProcessingEnabled(enabled);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Message processing " + (enabled ? "enabled" : "disabled"));
            response.put("enabled", enabled);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("❌ Error toggling processing: {}", e.getMessage(), e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Error: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * Add a test message to the queue
     */
    @PostMapping("/queue/add-test")
    public ResponseEntity<Map<String, Object>> addTestMessage(
            @RequestParam(defaultValue = "DEFAULT") String countryCode,
            @RequestParam(defaultValue = "INVOICE") String documentType) {
        try {
            logger.info("🧪 Adding test message to queue");
            
            String testUblXml = createTestUblXml(documentType);
            
            MessageProcessingQueue message = messageQueueService.addMessageToQueue(testUblXml, countryCode, documentType,null);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Test message added to queue");
            response.put("messageId", message.getMessageId());
            response.put("id", message.getId());
            
            logger.info("✅ Test message added: {}", message.getMessageId());
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("❌ Error adding test message: {}", e.getMessage(), e);
            Map<String, Object> response = new HashMap<>();
            response.put("success", false);
            response.put("message", "Error: " + e.getMessage());
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * Get health status
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> getHealth() {
        try {
            MessageProcessingService.ProcessingStatistics stats = messageProcessingService.getProcessingStatistics();
            
            Map<String, Object> health = new HashMap<>();
            health.put("status", "UP");
            health.put("processingEnabled", stats.isProcessingEnabled());
            health.put("totalMessages", stats.getTotalMessages());
            health.put("readyToSend", stats.getReadyToSend());
            health.put("processing", stats.getProcessing());
            health.put("error", stats.getError());
            
            return ResponseEntity.ok(health);
            
        } catch (Exception e) {
            logger.error("❌ Error getting health status: {}", e.getMessage(), e);
            Map<String, Object> health = new HashMap<>();
            health.put("status", "DOWN");
            health.put("error", e.getMessage());
            return ResponseEntity.internalServerError().body(health);
        }
    }

    /**
     * Create test UBL XML for testing
     */
    private String createTestUblXml(String documentType) {
        String template = switch (documentType.toUpperCase()) {
            case "CREDITNOTE" -> """
                <?xml version="1.0" encoding="UTF-8"?>
                <CreditNote xmlns="urn:oasis:names:specification:ubl:schema:xsd:CreditNote-2"
                           xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
                           xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2">
                    <cbc:ID>TEST-CN-001</cbc:ID>
                    <cbc:IssueDate>2025-06-30</cbc:IssueDate>
                    <cac:AccountingSupplierParty>
                        <cac:Party>
                            <cac:EndpointID schemeID="0088">9908:test-sender</cac:EndpointID>
                        </cac:Party>
                    </cac:AccountingSupplierParty>
                    <cac:AccountingCustomerParty>
                        <cac:Party>
                            <cac:EndpointID schemeID="0088">9908:test-receiver</cac:EndpointID>
                        </cac:Party>
                    </cac:AccountingCustomerParty>
                </CreditNote>
                """;
            case "APPLICATIONRESPONSE" -> """
                <?xml version="1.0" encoding="UTF-8"?>
                <ApplicationResponse xmlns="urn:oasis:names:specification:ubl:schema:xsd:ApplicationResponse-2"
                                   xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
                                   xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2">
                    <cbc:ID>TEST-AR-001</cbc:ID>
                    <cbc:IssueDate>2025-06-30</cbc:IssueDate>
                    <cac:SenderParty>
                        <cac:EndpointID schemeID="0088">9908:test-sender</cac:EndpointID>
                    </cac:SenderParty>
                    <cac:ReceiverParty>
                        <cac:EndpointID schemeID="0088">9908:test-receiver</cac:EndpointID>
                    </cac:ReceiverParty>
                </ApplicationResponse>
                """;
            default -> """
                <?xml version="1.0" encoding="UTF-8"?>
                <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
                        xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
                        xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2">
                    <cbc:ID>TEST-INV-001</cbc:ID>
                    <cbc:IssueDate>2025-06-30</cbc:IssueDate>
                    <cac:AccountingSupplierParty>
                        <cac:Party>
                            <cac:EndpointID schemeID="0088">9908:test-sender</cac:EndpointID>
                        </cac:Party>
                    </cac:AccountingSupplierParty>
                    <cac:AccountingCustomerParty>
                        <cac:Party>
                            <cac:EndpointID schemeID="0088">9908:test-receiver</cac:EndpointID>
                        </cac:Party>
                    </cac:AccountingCustomerParty>
                </Invoice>
                """;
        };
        
        return template.trim();
    }
}
