package com.morohub.apsp.config;

import com.morohub.apsp.common.dto.ErrorResponse;
import com.morohub.apsp.common.dto.MultipleErrorResponse;
import com.morohub.apsp.common.exception.AS4Exception;
import com.morohub.apsp.common.exception.AuthenticationException;
import com.morohub.apsp.common.exception.ConnectivityException;
import com.morohub.apsp.common.exception.ValidationException;
import com.morohub.apsp.core.service.ErrorManagementService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.method.annotation.MethodArgumentTypeMismatchException;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.ConstraintViolationException;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * Global exception handler for standardized error responses
 * Implements Requirements 2 & 3: Appropriate error codes and multiple errors support
 */
@RestControllerAdvice
public class GlobalExceptionHandler {

    private static final Logger logger = LoggerFactory.getLogger(GlobalExceptionHandler.class);

    @Autowired
    private ErrorManagementService errorManagementService;

    /**
     * Handle AS4Exception and its subclasses
     */
    @ExceptionHandler(AS4Exception.class)
    public ResponseEntity<ErrorResponse> handleAS4Exception(AS4Exception ex) {
        HttpServletRequest request = getCurrentRequest();
        
        // Log the error
        errorManagementService.logError(ex, request);
        
        // Generate response
        ErrorResponse errorResponse = errorManagementService.generateErrorResponse(ex);
        
        if (request != null) {
            errorResponse.setPath(request.getRequestURI());
            errorResponse.setMethod(request.getMethod());
        }

        HttpStatus httpStatus = getHttpStatusForException(ex);
        
        logger.error("AS4Exception handled: {} - {}", ex.getErrorCode(), ex.getMessage());
        
        return ResponseEntity.status(httpStatus).body(errorResponse);
    }

    /**
     * Handle ValidationException with multiple validation errors
     */
    @ExceptionHandler(ValidationException.class)
    public ResponseEntity<MultipleErrorResponse> handleValidationException(ValidationException ex) {
        HttpServletRequest request = getCurrentRequest();
        String requestId = UUID.randomUUID().toString();
        
        // Log the error
        errorManagementService.logError(ex, request);
        
        // Create multiple error response for validation errors
        MultipleErrorResponse.MultipleErrorResponseBuilder builder = MultipleErrorResponse.builder()
                .requestId(requestId)
                .messageId(ex.getMessageId());

        if (request != null) {
            builder.path(request.getRequestURI()).method(request.getMethod());
        }

        // Add main validation error
        ErrorResponse mainError = errorManagementService.generateErrorResponse(ex);
        builder.addError(mainError);

        // Add individual validation errors
        if (ex.hasValidationErrors()) {
            for (ValidationException.ValidationError validationError : ex.getValidationErrors()) {
                ErrorResponse fieldError = ErrorResponse.builder()
                        .errorCode("FIELD_VALIDATION_ERROR")
                        .errorType("FIELD_VALIDATION_FAILURE")
                        .errorDescription(String.format("Field '%s': %s", 
                                        validationError.getField(), validationError.getMessage()))
                        .severity("HIGH")
                        .retryable(false)
                        .requestId(requestId)
                        .messageId(ex.getMessageId())
                        .build();
                builder.addError(fieldError);
            }
        }

        MultipleErrorResponse response = builder.build();
        
        logger.error("ValidationException handled: {} validation errors for {}", 
                    ex.getErrorCount(), ex.getMessageId());
        
        return ResponseEntity.badRequest().body(response);
    }

    /**
     * Handle ConnectivityException
     */
    @ExceptionHandler(ConnectivityException.class)
    public ResponseEntity<ErrorResponse> handleConnectivityException(ConnectivityException ex) {
        HttpServletRequest request = getCurrentRequest();
        
        // Log the error
        errorManagementService.logError(ex, request);
        
        // Generate response
        ErrorResponse errorResponse = errorManagementService.generateErrorResponse(ex);
        
        // Add connectivity-specific information
        if (ex.getEndpoint() != null) {
            errorResponse.getAdditionalInfo().put("endpoint", ex.getEndpoint());
        }
        if (ex.getResponseTime() > 0) {
            errorResponse.getAdditionalInfo().put("responseTimeMs", ex.getResponseTime());
        }

        if (request != null) {
            errorResponse.setPath(request.getRequestURI());
            errorResponse.setMethod(request.getMethod());
        }

        HttpStatus httpStatus = ex.getHttpStatusCode() > 0 ? 
                HttpStatus.valueOf(ex.getHttpStatusCode()) : HttpStatus.SERVICE_UNAVAILABLE;
        
        logger.error("ConnectivityException handled: {} - {} (endpoint: {})", 
                    ex.getErrorCode(), ex.getMessage(), ex.getEndpoint());
        
        return ResponseEntity.status(httpStatus).body(errorResponse);
    }

    /**
     * Handle AuthenticationException
     */
    @ExceptionHandler(AuthenticationException.class)
    public ResponseEntity<ErrorResponse> handleAuthenticationException(AuthenticationException ex) {
        HttpServletRequest request = getCurrentRequest();
        
        // Log the error
        errorManagementService.logError(ex, request);
        
        // Generate response
        ErrorResponse errorResponse = errorManagementService.generateErrorResponse(ex);
        
        if (request != null) {
            errorResponse.setPath(request.getRequestURI());
            errorResponse.setMethod(request.getMethod());
        }

        HttpStatus httpStatus = HttpStatus.valueOf(ex.getHttpStatusCode());
        
        logger.error("AuthenticationException handled: {} - {}", ex.getErrorCode(), ex.getMessage());
        
        return ResponseEntity.status(httpStatus).body(errorResponse);
    }

    /**
     * Handle Spring validation errors (MethodArgumentNotValidException)
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<MultipleErrorResponse> handleMethodArgumentNotValid(MethodArgumentNotValidException ex) {
        HttpServletRequest request = getCurrentRequest();
        String requestId = UUID.randomUUID().toString();
        
        MultipleErrorResponse.MultipleErrorResponseBuilder builder = MultipleErrorResponse.builder()
                .requestId(requestId);

        if (request != null) {
            builder.path(request.getRequestURI()).method(request.getMethod());
        }

        // Add field validation errors
        ex.getBindingResult().getFieldErrors().forEach(fieldError -> {
            ErrorResponse error = ErrorResponse.builder()
                    .errorCode("FIELD_VALIDATION_ERROR")
                    .errorType("FIELD_VALIDATION_FAILURE")
                    .errorDescription(String.format("Field '%s': %s", 
                                    fieldError.getField(), fieldError.getDefaultMessage()))
                    .severity("HIGH")
                    .retryable(false)
                    .requestId(requestId)
                    .build();
            builder.addError(error);
        });

        MultipleErrorResponse response = builder.build();
        
        logger.error("Validation errors: {} field errors", response.getTotalErrors());
        
        return ResponseEntity.badRequest().body(response);
    }

    /**
     * Handle constraint violation exceptions
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public ResponseEntity<MultipleErrorResponse> handleConstraintViolation(ConstraintViolationException ex) {
        HttpServletRequest request = getCurrentRequest();
        String requestId = UUID.randomUUID().toString();
        
        MultipleErrorResponse.MultipleErrorResponseBuilder builder = MultipleErrorResponse.builder()
                .requestId(requestId);

        if (request != null) {
            builder.path(request.getRequestURI()).method(request.getMethod());
        }

        ex.getConstraintViolations().forEach(violation -> {
            ErrorResponse error = ErrorResponse.builder()
                    .errorCode("CONSTRAINT_VIOLATION")
                    .errorType("CONSTRAINT_VALIDATION_FAILURE")
                    .errorDescription(String.format("Property '%s': %s", 
                                    violation.getPropertyPath(), violation.getMessage()))
                    .severity("HIGH")
                    .retryable(false)
                    .requestId(requestId)
                    .build();
            builder.addError(error);
        });

        MultipleErrorResponse response = builder.build();
        
        logger.error("Constraint violations: {} violations", response.getTotalErrors());
        
        return ResponseEntity.badRequest().body(response);
    }

    /**
     * Handle generic exceptions
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<ErrorResponse> handleGenericException(Exception ex) {
        HttpServletRequest request = getCurrentRequest();
        String requestId = UUID.randomUUID().toString();
        
        // Log the error
        String stackTrace = getStackTraceAsString(ex);
        errorManagementService.logError("SYSTEM_ERROR", null, requestId, ex.getMessage(), stackTrace, request);
        
        ErrorResponse errorResponse = ErrorResponse.builder()
                .errorCode("SYSTEM_ERROR")
                .errorType("SYSTEM_ERROR")
                .errorDescription("An unexpected error occurred")
                .severity("HIGH")
                .retryable(false)
                .requestId(requestId)
                .build();

        if (request != null) {
            errorResponse.setPath(request.getRequestURI());
            errorResponse.setMethod(request.getMethod());
        }

        logger.error("Unhandled exception: {}", ex.getMessage(), ex);
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(errorResponse);
    }

    /**
     * Get HTTP status for AS4Exception
     */
    private HttpStatus getHttpStatusForException(AS4Exception ex) {
        // Check if error master data has specific HTTP status
        return errorManagementService.getErrorMasterData(ex.getErrorCode())
                .map(errorMaster -> HttpStatus.valueOf(errorMaster.getHttpStatusCode()))
                .orElse(HttpStatus.INTERNAL_SERVER_ERROR);
    }

    /**
     * Get current HTTP request
     */
    private HttpServletRequest getCurrentRequest() {
        try {
            ServletRequestAttributes attributes = (ServletRequestAttributes) RequestContextHolder.currentRequestAttributes();
            return attributes.getRequest();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * Convert stack trace to string
     */
    private String getStackTraceAsString(Throwable throwable) {
        java.io.StringWriter sw = new java.io.StringWriter();
        java.io.PrintWriter pw = new java.io.PrintWriter(sw);
        throwable.printStackTrace(pw);
        return sw.toString();
    }
}
