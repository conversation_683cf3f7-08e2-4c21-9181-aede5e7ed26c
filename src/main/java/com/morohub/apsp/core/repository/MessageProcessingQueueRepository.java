package com.morohub.apsp.core.repository;

import com.morohub.apsp.core.entity.MessageProcessingQueue;
import com.morohub.apsp.core.entity.MessageProcessingQueue.ProcessingStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Repository for managing message processing queue
 */
@Repository
public interface MessageProcessingQueueRepository extends JpaRepository<MessageProcessingQueue, Long> {

    /**
     * Find message by message ID
     */
    Optional<MessageProcessingQueue> findByMessageId(String messageId);

    /**
     * Find all messages with specific status
     */
    List<MessageProcessingQueue> findByStatus(ProcessingStatus status);

    /**
     * Find messages ready for processing (READY_TO_SEND or RETRY status)
     */
    @Query("SELECT m FROM MessageProcessingQueue m WHERE m.status IN ('READY_TO_SEND', 'RETRY') ORDER BY m.createdDate ASC")
    List<MessageProcessingQueue> findMessagesReadyForProcessing();

    /**
     * Find messages that need retry (RETRY status and not exceeded max retries)
     */
    @Query("SELECT m FROM MessageProcessingQueue m WHERE m.status = 'RETRY' AND m.retryCount < m.maxRetries ORDER BY m.lastProcessed ASC")
    List<MessageProcessingQueue> findMessagesForRetry();

    /**
     * Find messages by status and created date range
     */
    @Query("SELECT m FROM MessageProcessingQueue m WHERE m.status = :status AND m.createdDate BETWEEN :startDate AND :endDate")
    List<MessageProcessingQueue> findByStatusAndDateRange(
            @Param("status") ProcessingStatus status,
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate
    );

    /**
     * Count messages by status
     */
    long countByStatus(ProcessingStatus status);

    /**
     * Find messages that have been processing for too long (stuck messages)
     */
    @Query("SELECT m FROM MessageProcessingQueue m WHERE m.status = 'PROCESSING' AND m.lastProcessed < :cutoffTime")
    List<MessageProcessingQueue> findStuckMessages(@Param("cutoffTime") LocalDateTime cutoffTime);

    /**
     * Find messages by receiver participant ID
     */
    List<MessageProcessingQueue> findByReceiverParticipantId(String receiverParticipantId);

    /**
     * Find messages by country code and document type
     */
    List<MessageProcessingQueue> findByCountryCodeAndDocumentType(String countryCode, String documentType);

    /**
     * Check if message ID already exists
     */
    boolean existsByMessageId(String messageId);

    /**
     * Find recent error messages
     */
    @Query("SELECT m FROM MessageProcessingQueue m WHERE m.status = 'ERROR' ORDER BY m.lastUpdated DESC")
    List<MessageProcessingQueue> findRecentErrorMessages();

    /**
     * Find completed messages in date range
     */
    @Query("SELECT m FROM MessageProcessingQueue m WHERE m.status = 'COMPLETED' AND m.lastUpdated BETWEEN :startDate AND :endDate ORDER BY m.lastUpdated DESC")
    List<MessageProcessingQueue> findCompletedMessagesInDateRange(
            @Param("startDate") LocalDateTime startDate,
            @Param("endDate") LocalDateTime endDate
    );

    /**
     * Find messages by flow type and status
     */
    List<MessageProcessingQueue> findByFlowTypeAndStatusOrderByCreatedDateAsc(
        MessageProcessingQueue.FlowType flowType,
        MessageProcessingQueue.ProcessingStatus status);

    /**
     * Find messages by flow type
     */
    List<MessageProcessingQueue> findByFlowType(MessageProcessingQueue.FlowType flowType);
}
