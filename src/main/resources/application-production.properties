# MoroHub AS4 Service - Production Configuration
# ==============================================
# For live Peppol network operations with SMP lookup

# AS4 Mode
as4.mode=production
as4.security.enabled=true
as4.validation.enabled=true
as4.certificate.validation.enabled=true

# NO ENDPOINT URL - Uses SMP lookup to discover receiver endpoints automatically
# as4.endpoint.url=  # COMMENTED OUT - SM<PERSON> will discover the endpoint

# SMP Configuration for Production Mode
# Uses production ESML DNS for automatic SMP discovery

# File Configuration Paths (inherits from main application.properties)
# app.config.country-config.path=country-config.json
# app.config.schematron.base-path=schematron/
# app.config.keystore.base-path=keystore/

# Production Certificates - Use real certificate provided by receiver
as4.keystore.path=cert.p12
as4.keystore.password=TFd20lJQ4f8j
as4.keystore.key.alias=cert
as4.keystore.key.password=TFd20lJQ4f8j

as4.truststore.path=cert.p12
as4.truststore.password=TFd20lJQ4f8j

# Production Participant IDs
peppol.sender.participant.id=9908:987654321
peppol.receiver.participant.id=9908:123456789

# SMP (Service Metadata Publisher) Configuration
# Leave empty for automatic discovery, or specify custom SMP
# peppol.smp.url=${PEPPOL_SMP_URL:}

# Logging for Production
logging.level.com.morohub.apsp=INFO
logging.level.com.helger.phase4=WARN
logging.level.root=ERROR

# Phase4 Settings for Production
phase4.debug.http=false
phase4.debug.soap=false
phase4.send.retry.count=5
phase4.send.retry.interval.ms=10000

# Connection Settings (production timeouts)
connection.timeout.ms=60000
connection.socket.timeout.ms=120000
connection.connection.request.timeout.ms=30000

# Validation (strict for production)
validation.strict.mode=true
validation.schema.validation=true
validation.business.rules.validation=true

# Security Settings (production-grade)
security.signature.algorithm=http://www.w3.org/2001/04/xmldsig-more#rsa-sha256
security.digest.algorithm=http://www.w3.org/2001/04/xmlenc#sha256
security.encryption.algorithm=http://www.w3.org/2001/04/xmlenc#aes256-cbc

# SMP Configuration for Production
smp.url=https://smp.peppol.at

# Message Processing (production settings)
message.duplicate.check.enabled=true
message.duplicate.disposal.minutes=1440

# File Processing
file.processing.temp.dir=/var/tmp/as4
file.processing.max.size.mb=50

# Monitoring and Health Checks
management.endpoints.web.exposure.include=health,info,metrics
management.endpoint.health.show-details=when-authorized

# Security Headers
server.servlet.session.cookie.secure=true
server.servlet.session.cookie.http-only=true
