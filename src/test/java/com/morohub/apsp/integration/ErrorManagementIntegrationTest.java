package com.morohub.apsp.integration;

import com.morohub.apsp.common.dto.ErrorResponse;
import com.morohub.apsp.common.dto.MultipleErrorResponse;
import com.morohub.apsp.common.exception.AS4Exception;
import com.morohub.apsp.common.exception.ValidationException;
import com.morohub.apsp.core.entity.ErrorLog;
import com.morohub.apsp.core.entity.ErrorMasterData;
import com.morohub.apsp.core.repository.ErrorLogRepository;
import com.morohub.apsp.core.repository.ErrorMasterDataRepository;
import com.morohub.apsp.core.service.ErrorManagementService;
import com.morohub.apsp.core.service.RetryService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureWebMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

/**
 * Integration test for the comprehensive error management system
 * Tests all four requirements across the entire application
 */
@SpringBootTest
@AutoConfigureWebMvc
@ActiveProfiles("test")
@Transactional
public class ErrorManagementIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @Autowired
    private ErrorManagementService errorManagementService;

    @Autowired
    private RetryService retryService;

    @Autowired
    private ErrorMasterDataRepository errorMasterDataRepository;

    @Autowired
    private ErrorLogRepository errorLogRepository;

    /**
     * Test Requirement 1: Master data for all errors maintained in database
     */
    @Test
    public void testErrorMasterDataRequirement() {
        // Verify that error master data is properly initialized
        Optional<ErrorMasterData> authError = errorMasterDataRepository.findByErrorCode("AUTH_001");
        assertTrue(authError.isPresent(), "AUTH_001 error should be present in master data");
        
        ErrorMasterData auth001 = authError.get();
        assertEquals("AUTH_001", auth001.getErrorCode());
        assertEquals(Integer.valueOf(401), auth001.getHttpStatusCode());
        assertEquals(ErrorMasterData.ErrorType.AUTHENTICATION_FAILURE, auth001.getErrorType());
        assertNotNull(auth001.getErrorDescription());
        assertFalse(auth001.getRetryRequired());

        // Test connectivity error with retry configuration
        Optional<ErrorMasterData> connError = errorMasterDataRepository.findByErrorCode("CONN_001");
        assertTrue(connError.isPresent(), "CONN_001 error should be present in master data");
        
        ErrorMasterData conn001 = connError.get();
        assertEquals("CONN_001", conn001.getErrorCode());
        assertEquals(Integer.valueOf(503), conn001.getHttpStatusCode());
        assertEquals(ErrorMasterData.ErrorType.TECHNICAL_CONNECTIVITY_ERROR, conn001.getErrorType());
        assertTrue(conn001.getRetryRequired());
        assertNotNull(conn001.getRetryIntervalMs());
        assertNotNull(conn001.getMaxRetryAttempts());
        assertTrue(conn001.isValidRetryConfiguration());
    }

    /**
     * Test Requirement 2: Appropriate error codes and descriptions in API responses
     */
    @Test
    public void testAppropriateErrorCodesRequirement() throws Exception {
        // Test authentication error (401)
        mockMvc.perform(post("/api/invoice/validate")
                .contentType(MediaType.APPLICATION_JSON)
                .content("invalid-xml"))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.errorCode").exists())
                .andExpect(jsonPath("$.errorDescription").exists())
                .andExpect(jsonPath("$.timestamp").exists());

        // Test validation error (400) with country-specific validation
        mockMvc.perform(post("/api/invoice/validate/INVALID/INVOICE")
                .contentType(MediaType.APPLICATION_JSON)
                .content("<invalid>xml</invalid>"))
                .andExpect(status().isBadRequest())
                .andExpect(jsonPath("$.errorCode").value("VAL_004"))
                .andExpect(jsonPath("$.errorType").value("DOCUMENT_CAPABILITY_MISMATCH"));
    }

    /**
     * Test Requirement 3: Multiple errors in single response
     */
    @Test
    public void testMultipleErrorsRequirement() {
        // Create a validation exception with multiple errors
        List<ValidationException.ValidationError> errors = List.of(
            new ValidationException.ValidationError("field1", "Field 1 is required"),
            new ValidationException.ValidationError("field2", "Field 2 has invalid format"),
            new ValidationException.ValidationError("field3", "Field 3 exceeds maximum length")
        );

        ValidationException validationException = ValidationException.schematronValidation(
            "VAL_002", "Multiple validation errors", errors, "MSG_123", "REQ_456");

        // Generate multiple error response
        MultipleErrorResponse response = errorManagementService.generateMultipleErrorResponse(
            List.of(validationException), "REQ_456", "MSG_123");

        assertNotNull(response);
        assertEquals(4, response.getTotalErrors()); // 1 main + 3 field errors
        assertEquals("REQ_456", response.getRequestId());
        assertEquals("MSG_123", response.getMessageId());
        assertTrue(response.isAllValidationErrors());
        assertNotNull(response.getSummary());
    }

    /**
     * Test Requirement 4: Automatic retry and manual operations
     */
    @Test
    public void testAutomaticRetryRequirement() {
        // Create a retryable error
        ErrorMasterData retryableError = new ErrorMasterData();
        retryableError.setErrorCode("TEST_RETRY_001");
        retryableError.setHttpStatusCode(503);
        retryableError.setErrorType(ErrorMasterData.ErrorType.TECHNICAL_CONNECTIVITY_ERROR);
        retryableError.setErrorDescription("Test retryable error");
        retryableError.setRetryRequired(true);
        retryableError.setRetryIntervalMs(1000L);
        retryableError.setMaxRetryAttempts(3);
        retryableError.setSeverity(ErrorMasterData.ErrorSeverity.HIGH);
        retryableError.setActive(true);
        retryableError.setCreatedBy("TEST");
        
        ErrorMasterData savedError = errorMasterDataRepository.save(retryableError);

        // Log an error occurrence
        ErrorLog errorLog = errorManagementService.logError("TEST_RETRY_001", "MSG_TEST", "REQ_TEST",
            "Test error message", "Test stack trace", null);

        assertNotNull(errorLog);
        assertEquals("MSG_TEST", errorLog.getMessageId());
        assertEquals("REQ_TEST", errorLog.getRequestId());
        assertEquals(ErrorLog.ProcessingStatus.RETRY_SCHEDULED, errorLog.getProcessingStatus());
        assertNotNull(errorLog.getNextRetryAt());

        // Test manual retry
        boolean retryResult = retryService.manualRetry(errorLog.getId(), "TEST_USER");
        // Note: This might fail in test environment due to missing dependencies, but the method should execute

        // Test manual discard
        boolean discardResult = retryService.discardError(errorLog.getId(), "TEST_USER", "Test discard reason");
        assertTrue(discardResult);

        // Verify error was discarded
        Optional<ErrorLog> updatedErrorLog = errorLogRepository.findById(errorLog.getId());
        assertTrue(updatedErrorLog.isPresent());
        assertEquals(ErrorLog.ProcessingStatus.DISCARDED, updatedErrorLog.get().getProcessingStatus());
        assertEquals("TEST_USER", updatedErrorLog.get().getResolvedBy());
    }

    /**
     * Test error management API endpoints
     */
    @Test
    public void testErrorManagementAPIEndpoints() throws Exception {
        // Test get error master data
        mockMvc.perform(get("/api/error-management/master-data/AUTH_001"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.errorCode").value("AUTH_001"))
                .andExpect(jsonPath("$.httpStatusCode").value(401));

        // Test get unresolved errors
        mockMvc.perform(get("/api/error-management/logs/unresolved"))
                .andExpect(status().isOk());

        // Test get retry statistics
        mockMvc.perform(get("/api/error-management/statistics/retry"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.totalErrorsLast24Hours").exists())
                .andExpect(jsonPath("$.unresolvedErrors").exists());

        // Test error dashboard
        mockMvc.perform(get("/api/error-management/dashboard"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.retryStatistics").exists())
                .andExpect(jsonPath("$.lastUpdated").exists());

        // Test health check
        mockMvc.perform(get("/api/error-management/health"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.status").value("UP"));
    }

    /**
     * Test error response generation from AS4Exception
     */
    @Test
    public void testAS4ExceptionErrorResponse() {
        AS4Exception as4Exception = new AS4Exception("AS4_001", "AS4 processing failed",
            ErrorMasterData.ErrorType.TECHNICAL_CONNECTIVITY_ERROR,
            ErrorMasterData.ErrorSeverity.HIGH, true, "MSG_123", "REQ_456");

        ErrorResponse response = errorManagementService.generateErrorResponse(as4Exception);

        assertNotNull(response);
        assertEquals("AS4_001", response.getErrorCode());
        assertEquals("AS4 processing failed", response.getErrorDescription());
        assertEquals("TECHNICAL_CONNECTIVITY_ERROR", response.getErrorType());
        assertEquals("HIGH", response.getSeverity());
        assertTrue(response.isRetryable());
        assertEquals("MSG_123", response.getMessageId());
        assertEquals("REQ_456", response.getRequestId());
    }

    /**
     * Test error logging and retrieval
     */
    @Test
    public void testErrorLoggingAndRetrieval() {
        // Log multiple errors for the same message
        errorManagementService.logError("VAL_001", "MSG_MULTI", "REQ_MULTI",
            "First validation error", "Stack trace 1", null);
        errorManagementService.logError("VAL_002", "MSG_MULTI", "REQ_MULTI",
            "Second validation error", "Stack trace 2", null);

        // Retrieve errors for the message
        List<ErrorLog> errorLogs = errorManagementService.getErrorLogsForMessage("MSG_MULTI");
        assertEquals(2, errorLogs.size());

        // Test marking error as resolved
        ErrorLog firstError = errorLogs.get(0);
        errorManagementService.markErrorAsResolved(firstError.getId(), "TEST_USER", "Manually resolved");

        // Verify resolution
        Optional<ErrorLog> resolvedError = errorLogRepository.findById(firstError.getId());
        assertTrue(resolvedError.isPresent());
        assertTrue(resolvedError.get().getResolved());
        assertEquals("TEST_USER", resolvedError.get().getResolvedBy());
        assertEquals("Manually resolved", resolvedError.get().getResolutionNotes());
    }

    /**
     * Test retry statistics
     */
    @Test
    public void testRetryStatistics() {
        RetryService.RetryStatistics stats = retryService.getRetryStatistics();
        
        assertNotNull(stats);
        assertTrue(stats.getTotalErrorsLast24Hours() >= 0);
        assertTrue(stats.getUnresolvedErrors() >= 0);
        assertTrue(stats.getErrorsReadyForRetry() >= 0);
        assertTrue(stats.getErrorsExceededMaxRetries() >= 0);
    }
}
