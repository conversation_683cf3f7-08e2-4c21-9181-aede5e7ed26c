2025-07-02 10:29:16.602 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - MetaAS4Manager is initialized with in-memory data structures
2025-07-02 10:29:16.623 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - Creating AS4 managers using factory class com.helger.phase4.mgr.AS4ManagerFactoryInMemory
2025-07-02 10:29:16.654 [main] INFO  c.h.phase4.profile.AS4ProfileManager - 1 AS4 profile is registered 
2025-07-02 10:29:16.654 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - MetaAS4Manager was initialized
2025-07-02 10:30:11.542 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - MetaAS4Manager is initialized with in-memory data structures
2025-07-02 10:30:11.542 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - Creating AS4 managers using factory class com.helger.phase4.mgr.AS4ManagerFactoryInMemory
2025-07-02 10:30:11.589 [main] INFO  c.h.phase4.profile.AS4ProfileManager - 1 AS4 profile is registered 
2025-07-02 10:30:11.589 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - MetaAS4Manager was initialized
2025-07-02 10:32:28.140 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - MetaAS4Manager is initialized with in-memory data structures
2025-07-02 10:32:28.156 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - Creating AS4 managers using factory class com.helger.phase4.mgr.AS4ManagerFactoryInMemory
2025-07-02 10:32:28.201 [main] INFO  c.h.phase4.profile.AS4ProfileManager - 1 AS4 profile is registered 
2025-07-02 10:32:28.201 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - MetaAS4Manager was initialized
2025-07-02 10:37:50.106 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - MetaAS4Manager is initialized with in-memory data structures
2025-07-02 10:37:50.106 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - Creating AS4 managers using factory class com.helger.phase4.mgr.AS4ManagerFactoryInMemory
2025-07-02 10:37:50.137 [main] INFO  c.h.phase4.profile.AS4ProfileManager - 1 AS4 profile is registered 
2025-07-02 10:37:50.153 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - MetaAS4Manager was initialized
2025-07-02 10:41:28.546 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - MetaAS4Manager is initialized with in-memory data structures
2025-07-02 10:41:28.562 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - Creating AS4 managers using factory class com.helger.phase4.mgr.AS4ManagerFactoryInMemory
2025-07-02 10:41:28.609 [main] INFO  c.h.phase4.profile.AS4ProfileManager - 1 AS4 profile is registered 
2025-07-02 10:41:28.620 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - MetaAS4Manager was initialized
2025-07-02 11:06:27.061 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - MetaAS4Manager is initialized with in-memory data structures
2025-07-02 11:06:27.077 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - Creating AS4 managers using factory class com.helger.phase4.mgr.AS4ManagerFactoryInMemory
2025-07-02 11:06:27.124 [main] INFO  c.h.phase4.profile.AS4ProfileManager - 1 AS4 profile is registered 
2025-07-02 11:06:27.124 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - MetaAS4Manager was initialized
2025-07-02 11:27:21.202 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - MetaAS4Manager is initialized with in-memory data structures
2025-07-02 11:27:21.218 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - Creating AS4 managers using factory class com.helger.phase4.mgr.AS4ManagerFactoryInMemory
2025-07-02 11:27:21.249 [main] INFO  c.h.phase4.profile.AS4ProfileManager - 1 AS4 profile is registered 
2025-07-02 11:27:21.249 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - MetaAS4Manager was initialized
2025-07-02 11:29:07.317 [http-nio-8081-exec-7] INFO  c.h.p.s.AS4BidirectionalClientHelper - Sending AS4 UserMessage to 'http://localhost:8081/reverse-flow/receive-as4-message' with max. 1 retries
2025-07-02 11:29:07.901 [http-nio-8081-exec-7] INFO  c.helger.phase4.wss.WSSConfigManager - None of the WSSConfig Security Providers is already installed - doing it now
2025-07-02 11:29:07.986 [http-nio-8081-exec-7] INFO  c.h.p.messaging.crypto.AS4Signer - Now signing AS4 message [SOAP_12]. KeyIdentifierType=BST_DIRECT_REFERENCE; KeyAlias=cert; SignAlgo=http://www.w3.org/2001/04/xmldsig-more#rsa-sha256; DigestAlgo=http://www.w3.org/2001/04/xmlenc#sha256; C14NAlgo=http://www.w3.org/2001/10/xml-exc-c14n#
2025-07-02 11:29:08.095 [http-nio-8081-exec-7] INFO  c.h.p.messaging.crypto.AS4Encryptor - Now encrypting AS4 MIME message. KeyIdentifierType=BST_DIRECT_REFERENCE; EncAlgo=http://www.w3.org/2009/xmlenc11#aes128-gcm; KeyEncAlgo=RSA_OAEP_XENC11; MgfAlgo=http://www.w3.org/2009/xmlenc11#mgf1sha256; DigestAlgo=http://www.w3.org/2001/04/xmlenc#sha256; CertificateSubjectCN=CN=POP000688,OU=PEPPOL TEST AP,O=SunTec Business Solutions DMCC,C=AE
2025-07-02 11:29:08.300 [http-nio-8081-exec-7] INFO  c.h.phase4.util.AS4ResourceHelper - Converting [[Entity-Class: HttpMimeMessageEntity, Content-Type: multipart/related; 
	boundary="----=_Part_0_1739044190.1751435948219"; 
	type="application/soap+xml"; charset=UTF-8, Content-Encoding: null, chunked: false]; MimeMsg=[com.helger.phase4.messaging.mime.AS4MimeMessage@49ee6fe0; IsRepeatable=false]] to a repeatable HTTP entity using file C:\Users\<USER>\AppData\Local\Temp\phase4-res-14415449963074576844.tmp
2025-07-02 11:29:08.406 [http-nio-8081-exec-7] INFO  c.helger.phase4.http.BasicHttpPoster - Starting to transmit AS4 Message to 'http://localhost:8081/reverse-flow/receive-as4-message'
2025-07-02 11:29:08.967 [http-nio-8081-exec-7] INFO  c.helger.phase4.http.BasicHttpPoster - Failed transmitting AS4 Message to 'http://localhost:8081/reverse-flow/receive-as4-message' after 557 ms
2025-07-02 11:29:08.969 [http-nio-8081-exec-7] WARN  c.helger.phase4.http.BasicHttpPoster - Error sending message '2b26f965-0e12-48d4-8304-1f079aa2dc60@phase4' to 'http://localhost:8081/reverse-flow/receive-as4-message': ExtendedHttpResponseException -  [500]
All 4 headers returned
  Content-Type=application/soap+xml;charset=UTF-8
  Content-Length=2245
  Date=Wed, 02 Jul 2025 05:59:08 GMT
  Connection=close
Response Body (in UTF-8):
<?xml version="1.0" encoding="UTF-8"?>
<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope" xmlns:eb="http://docs.oasis-open.org/ebxml-msg/ebms/v3.0/ns/core/200704/">
  <soap:Header>
    <eb:Messaging soap:mustUnderstand="true">
      <eb:SignalMessage>
        <eb:MessageInfo>
          <eb:Timestamp>2025-07-02T05:59:08.953600300Z</eb:Timestamp>
          <eb:MessageId>7357367c-e85c-40a0-835a-d0b7c4501ccb</eb:MessageId>
        </eb:MessageInfo>
        <eb:Error errorCode="EBMS:0004" severity="failure" shortDescription="Other">
          <eb:Description xml:lang="en">Failed to add message to processing queue: Failed to add message to queue: could not execute statement [ERROR: null value in column &quot;flow_type&quot; of relation &quot;message_processing_queue&quot; violates not-null constraint
  Detail: Failing row contains (19, DEFAULT, 2025-07-02 11:29:08.8966, INVOICE, null, null, null, 2025-07-02 11:29:08.8966, 3, MSG-2c1c6a47-b63f-4ba9-b6be-809222b0acee, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, &lt;Invoice xmlns=&quot;urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, &lt;BusinessScope 
                  &gt;
   &lt;Scope&gt;
      &lt;Type&gt;DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type&quot; of relation &quot;message_processing_queue]</eb:Description>
        </eb:Error>
      </eb:SignalMessage>
    </eb:Messaging>
  </soap:Header>
  <soap:Body/>
</soap:Envelope> - waiting 10000 ms, than retrying
2025-07-02 11:29:19.086 [http-nio-8081-exec-7] INFO  c.helger.phase4.http.BasicHttpPoster - Retry #1/1 for sending message with ID '2b26f965-0e12-48d4-8304-1f079aa2dc60@phase4'
2025-07-02 11:29:19.087 [http-nio-8081-exec-7] INFO  c.helger.phase4.http.BasicHttpPoster - Starting to transmit AS4 Message to 'http://localhost:8081/reverse-flow/receive-as4-message'
2025-07-02 11:29:19.211 [http-nio-8081-exec-7] INFO  c.helger.phase4.http.BasicHttpPoster - Failed transmitting AS4 Message to 'http://localhost:8081/reverse-flow/receive-as4-message' after 123 ms
2025-07-02 11:29:19.218 [http-nio-8081-exec-7] ERROR c.h.p.s.AbstractAS4UserMessageBuilder - Exception sending AS4 user message
com.helger.phase4.util.Phase4Exception: Wrapped Phase4Exception
	at com.helger.phase4.sender.AbstractAS4UserMessageBuilderMIMEPayload.mainSendMessage(AbstractAS4UserMessageBuilderMIMEPayload.java:237)
	at com.helger.phase4.sender.AbstractAS4MessageBuilder.sendMessage(AbstractAS4MessageBuilder.java:856)
	at com.helger.phase4.sender.AbstractAS4UserMessageBuilder.sendMessageAndCheckForReceipt(AbstractAS4UserMessageBuilder.java:798)
	at com.helger.phase4.sender.AbstractAS4UserMessageBuilder.sendMessageAndCheckForReceipt(AbstractAS4UserMessageBuilder.java:764)
	at com.morohub.apsp.core.service.AS4ConversionService.processAs4message(AS4ConversionService.java:902)
	at com.morohub.apsp.core.service.AS4ConversionService.sendAS4Message(AS4ConversionService.java:824)
	at com.morohub.apsp.core.service.AS4ConversionService.convertAndSend(AS4ConversionService.java:250)
	at com.morohub.apsp.api.controller.XmlValidationController.validateAndConvertToAs4(XmlValidationController.java:78)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:254)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:182)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: com.helger.httpclient.response.ExtendedHttpResponseException:  [500]
All 4 headers returned
  Content-Type=application/soap+xml;charset=UTF-8
  Content-Length=2249
  Date=Wed, 02 Jul 2025 05:59:19 GMT
  Connection=close
Response Body (in UTF-8):
<?xml version="1.0" encoding="UTF-8"?>
<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope" xmlns:eb="http://docs.oasis-open.org/ebxml-msg/ebms/v3.0/ns/core/200704/">
  <soap:Header>
    <eb:Messaging soap:mustUnderstand="true">
      <eb:SignalMessage>
        <eb:MessageInfo>
          <eb:Timestamp>2025-07-02T05:59:19.207768200Z</eb:Timestamp>
          <eb:MessageId>f34f543e-612f-431b-a23b-e9879a2edced</eb:MessageId>
        </eb:MessageInfo>
        <eb:Error errorCode="EBMS:0004" severity="failure" shortDescription="Other">
          <eb:Description xml:lang="en">Failed to add message to processing queue: Failed to add message to queue: could not execute statement [ERROR: null value in column &quot;flow_type&quot; of relation &quot;message_processing_queue&quot; violates not-null constraint
  Detail: Failing row contains (20, DEFAULT, 2025-07-02 11:29:19.195769, INVOICE, null, null, null, 2025-07-02 11:29:19.195769, 3, MSG-5ff22f30-1496-4e14-a059-597ba6668f3c, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, &lt;Invoice xmlns=&quot;urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, &lt;BusinessScope 
                  &gt;
   &lt;Scope&gt;
      &lt;Type&gt;DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type&quot; of relation &quot;message_processing_queue]</eb:Description>
        </eb:Error>
      </eb:SignalMessage>
    </eb:Messaging>
  </soap:Header>
  <soap:Body/>
</soap:Envelope>
	at com.helger.httpclient.response.ExtendedHttpResponseException.create(ExtendedHttpResponseException.java:211)
	at com.helger.httpclient.response.ResponseHandlerHttpEntity.handleResponse(ResponseHandlerHttpEntity.java:55)
	at com.helger.phase4.sender.AS4BidirectionalClientHelper.lambda$sendAS4UserMessageAndReceiveAS4SignalMessage$0(AS4BidirectionalClientHelper.java:125)
	at com.helger.phase4.client.AbstractAS4Client.lambda$sendMessageWithRetries$1(AbstractAS4Client.java:589)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:247)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:188)
	at com.helger.httpclient.HttpClientManager.execute(HttpClientManager.java:137)
	at com.helger.httpclient.HttpClientManager.execute(HttpClientManager.java:102)
	at com.helger.phase4.http.BasicHttpPoster.sendGenericMessage(BasicHttpPoster.java:201)
	at com.helger.phase4.http.BasicHttpPoster.sendGenericMessageWithRetries(BasicHttpPoster.java:317)
	at com.helger.phase4.client.AbstractAS4Client.sendMessageWithRetries(AbstractAS4Client.java:591)
	at com.helger.phase4.sender.AS4BidirectionalClientHelper.sendAS4UserMessageAndReceiveAS4SignalMessage(AS4BidirectionalClientHelper.java:137)
	at com.helger.phase4.sender.AbstractAS4UserMessageBuilderMIMEPayload.mainSendMessage(AbstractAS4UserMessageBuilderMIMEPayload.java:211)
	... 55 common frames omitted
2025-07-02 11:31:10.558 [http-nio-8081-exec-3] INFO  c.h.p.s.AS4BidirectionalClientHelper - Sending AS4 UserMessage to 'http://localhost:8081/reverse-flow/receive-as4-message' with max. 1 retries
2025-07-02 11:31:10.586 [http-nio-8081-exec-3] INFO  c.h.p.messaging.crypto.AS4Signer - Now signing AS4 message [SOAP_12]. KeyIdentifierType=BST_DIRECT_REFERENCE; KeyAlias=cert; SignAlgo=http://www.w3.org/2001/04/xmldsig-more#rsa-sha256; DigestAlgo=http://www.w3.org/2001/04/xmlenc#sha256; C14NAlgo=http://www.w3.org/2001/10/xml-exc-c14n#
2025-07-02 11:31:10.594 [http-nio-8081-exec-3] INFO  c.h.p.messaging.crypto.AS4Encryptor - Now encrypting AS4 MIME message. KeyIdentifierType=BST_DIRECT_REFERENCE; EncAlgo=http://www.w3.org/2009/xmlenc11#aes128-gcm; KeyEncAlgo=RSA_OAEP_XENC11; MgfAlgo=http://www.w3.org/2009/xmlenc11#mgf1sha256; DigestAlgo=http://www.w3.org/2001/04/xmlenc#sha256; CertificateSubjectCN=CN=POP000688,OU=PEPPOL TEST AP,O=SunTec Business Solutions DMCC,C=AE
2025-07-02 11:31:10.607 [http-nio-8081-exec-3] INFO  c.h.phase4.util.AS4ResourceHelper - Converting [[Entity-Class: HttpMimeMessageEntity, Content-Type: multipart/related; 
	boundary="----=_Part_2_692291731.1751436070597"; 
	type="application/soap+xml"; charset=UTF-8, Content-Encoding: null, chunked: false]; MimeMsg=[com.helger.phase4.messaging.mime.AS4MimeMessage@3a280b09; IsRepeatable=false]] to a repeatable HTTP entity using file C:\Users\<USER>\AppData\Local\Temp\phase4-res-3990631650379948535.tmp
2025-07-02 11:31:10.639 [http-nio-8081-exec-3] INFO  c.helger.phase4.http.BasicHttpPoster - Starting to transmit AS4 Message to 'http://localhost:8081/reverse-flow/receive-as4-message'
2025-07-02 11:31:10.769 [http-nio-8081-exec-3] INFO  c.helger.phase4.http.BasicHttpPoster - Failed transmitting AS4 Message to 'http://localhost:8081/reverse-flow/receive-as4-message' after 129 ms
2025-07-02 11:31:10.769 [http-nio-8081-exec-3] WARN  c.helger.phase4.http.BasicHttpPoster - Error sending message '84c3acf9-262b-4950-92bd-45626adac231@phase4' to 'http://localhost:8081/reverse-flow/receive-as4-message': ExtendedHttpResponseException -  [500]
All 4 headers returned
  Content-Type=application/soap+xml;charset=UTF-8
  Content-Length=2249
  Date=Wed, 02 Jul 2025 06:01:10 GMT
  Connection=close
Response Body (in UTF-8):
<?xml version="1.0" encoding="UTF-8"?>
<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope" xmlns:eb="http://docs.oasis-open.org/ebxml-msg/ebms/v3.0/ns/core/200704/">
  <soap:Header>
    <eb:Messaging soap:mustUnderstand="true">
      <eb:SignalMessage>
        <eb:MessageInfo>
          <eb:Timestamp>2025-07-02T06:01:10.767495200Z</eb:Timestamp>
          <eb:MessageId>fb05cac6-863a-4c4d-ba6e-7e229bde4220</eb:MessageId>
        </eb:MessageInfo>
        <eb:Error errorCode="EBMS:0004" severity="failure" shortDescription="Other">
          <eb:Description xml:lang="en">Failed to add message to processing queue: Failed to add message to queue: could not execute statement [ERROR: null value in column &quot;flow_type&quot; of relation &quot;message_processing_queue&quot; violates not-null constraint
  Detail: Failing row contains (22, DEFAULT, 2025-07-02 11:31:10.758493, INVOICE, null, null, null, 2025-07-02 11:31:10.758493, 3, MSG-3edaf855-0ff8-47d8-8d91-7785a456d901, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, &lt;Invoice xmlns=&quot;urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, &lt;BusinessScope 
                  &gt;
   &lt;Scope&gt;
      &lt;Type&gt;DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type&quot; of relation &quot;message_processing_queue]</eb:Description>
        </eb:Error>
      </eb:SignalMessage>
    </eb:Messaging>
  </soap:Header>
  <soap:Body/>
</soap:Envelope> - waiting 10000 ms, than retrying
2025-07-02 11:31:20.784 [http-nio-8081-exec-3] INFO  c.helger.phase4.http.BasicHttpPoster - Retry #1/1 for sending message with ID '84c3acf9-262b-4950-92bd-45626adac231@phase4'
2025-07-02 11:31:20.784 [http-nio-8081-exec-3] INFO  c.helger.phase4.http.BasicHttpPoster - Starting to transmit AS4 Message to 'http://localhost:8081/reverse-flow/receive-as4-message'
2025-07-02 11:31:20.913 [http-nio-8081-exec-3] INFO  c.helger.phase4.http.BasicHttpPoster - Failed transmitting AS4 Message to 'http://localhost:8081/reverse-flow/receive-as4-message' after 129 ms
2025-07-02 11:31:20.915 [http-nio-8081-exec-3] ERROR c.h.p.s.AbstractAS4UserMessageBuilder - Exception sending AS4 user message
com.helger.phase4.util.Phase4Exception: Wrapped Phase4Exception
	at com.helger.phase4.sender.AbstractAS4UserMessageBuilderMIMEPayload.mainSendMessage(AbstractAS4UserMessageBuilderMIMEPayload.java:237)
	at com.helger.phase4.sender.AbstractAS4MessageBuilder.sendMessage(AbstractAS4MessageBuilder.java:856)
	at com.helger.phase4.sender.AbstractAS4UserMessageBuilder.sendMessageAndCheckForReceipt(AbstractAS4UserMessageBuilder.java:798)
	at com.helger.phase4.sender.AbstractAS4UserMessageBuilder.sendMessageAndCheckForReceipt(AbstractAS4UserMessageBuilder.java:764)
	at com.morohub.apsp.core.service.AS4ConversionService.processAs4message(AS4ConversionService.java:902)
	at com.morohub.apsp.core.service.AS4ConversionService.sendAS4Message(AS4ConversionService.java:824)
	at com.morohub.apsp.core.service.AS4ConversionService.convertAndSend(AS4ConversionService.java:250)
	at com.morohub.apsp.api.controller.XmlValidationController.validateAndConvertToAs4(XmlValidationController.java:78)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:254)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:182)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: com.helger.httpclient.response.ExtendedHttpResponseException:  [500]
All 4 headers returned
  Content-Type=application/soap+xml;charset=UTF-8
  Content-Length=2249
  Date=Wed, 02 Jul 2025 06:01:20 GMT
  Connection=close
Response Body (in UTF-8):
<?xml version="1.0" encoding="UTF-8"?>
<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope" xmlns:eb="http://docs.oasis-open.org/ebxml-msg/ebms/v3.0/ns/core/200704/">
  <soap:Header>
    <eb:Messaging soap:mustUnderstand="true">
      <eb:SignalMessage>
        <eb:MessageInfo>
          <eb:Timestamp>2025-07-02T06:01:20.912204300Z</eb:Timestamp>
          <eb:MessageId>b88376dd-efdc-4066-8bf6-915efd640a92</eb:MessageId>
        </eb:MessageInfo>
        <eb:Error errorCode="EBMS:0004" severity="failure" shortDescription="Other">
          <eb:Description xml:lang="en">Failed to add message to processing queue: Failed to add message to queue: could not execute statement [ERROR: null value in column &quot;flow_type&quot; of relation &quot;message_processing_queue&quot; violates not-null constraint
  Detail: Failing row contains (23, DEFAULT, 2025-07-02 11:31:20.903205, INVOICE, null, null, null, 2025-07-02 11:31:20.903205, 3, MSG-c7a05025-e5d2-4621-a677-d5f0b0511d77, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, &lt;Invoice xmlns=&quot;urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, &lt;BusinessScope 
                  &gt;
   &lt;Scope&gt;
      &lt;Type&gt;DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type&quot; of relation &quot;message_processing_queue]</eb:Description>
        </eb:Error>
      </eb:SignalMessage>
    </eb:Messaging>
  </soap:Header>
  <soap:Body/>
</soap:Envelope>
	at com.helger.httpclient.response.ExtendedHttpResponseException.create(ExtendedHttpResponseException.java:211)
	at com.helger.httpclient.response.ResponseHandlerHttpEntity.handleResponse(ResponseHandlerHttpEntity.java:55)
	at com.helger.phase4.sender.AS4BidirectionalClientHelper.lambda$sendAS4UserMessageAndReceiveAS4SignalMessage$0(AS4BidirectionalClientHelper.java:125)
	at com.helger.phase4.client.AbstractAS4Client.lambda$sendMessageWithRetries$1(AbstractAS4Client.java:589)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:247)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:188)
	at com.helger.httpclient.HttpClientManager.execute(HttpClientManager.java:137)
	at com.helger.httpclient.HttpClientManager.execute(HttpClientManager.java:102)
	at com.helger.phase4.http.BasicHttpPoster.sendGenericMessage(BasicHttpPoster.java:201)
	at com.helger.phase4.http.BasicHttpPoster.sendGenericMessageWithRetries(BasicHttpPoster.java:317)
	at com.helger.phase4.client.AbstractAS4Client.sendMessageWithRetries(AbstractAS4Client.java:591)
	at com.helger.phase4.sender.AS4BidirectionalClientHelper.sendAS4UserMessageAndReceiveAS4SignalMessage(AS4BidirectionalClientHelper.java:137)
	at com.helger.phase4.sender.AbstractAS4UserMessageBuilderMIMEPayload.mainSendMessage(AbstractAS4UserMessageBuilderMIMEPayload.java:211)
	... 55 common frames omitted
2025-07-02 11:39:52.664 [SpringApplicationShutdownHook] INFO  c.helger.phase4.wss.WSSConfigManager - Cleaning up WSSConfig. Security Providers will also be removed.
2025-07-02 11:40:06.983 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - MetaAS4Manager is initialized with in-memory data structures
2025-07-02 11:40:06.983 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - Creating AS4 managers using factory class com.helger.phase4.mgr.AS4ManagerFactoryInMemory
2025-07-02 11:40:07.030 [main] INFO  c.h.phase4.profile.AS4ProfileManager - 1 AS4 profile is registered 
2025-07-02 11:40:07.030 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - MetaAS4Manager was initialized
2025-07-02 11:40:28.248 [http-nio-8081-exec-2] INFO  c.h.p.s.AS4BidirectionalClientHelper - Sending AS4 UserMessage to 'http://localhost:8081/reverse-flow/receive-as4-message' with max. 1 retries
2025-07-02 11:40:28.820 [http-nio-8081-exec-2] INFO  c.helger.phase4.wss.WSSConfigManager - None of the WSSConfig Security Providers is already installed - doing it now
2025-07-02 11:40:28.908 [http-nio-8081-exec-2] INFO  c.h.p.messaging.crypto.AS4Signer - Now signing AS4 message [SOAP_12]. KeyIdentifierType=BST_DIRECT_REFERENCE; KeyAlias=cert; SignAlgo=http://www.w3.org/2001/04/xmldsig-more#rsa-sha256; DigestAlgo=http://www.w3.org/2001/04/xmlenc#sha256; C14NAlgo=http://www.w3.org/2001/10/xml-exc-c14n#
2025-07-02 11:40:29.002 [http-nio-8081-exec-2] INFO  c.h.p.messaging.crypto.AS4Encryptor - Now encrypting AS4 MIME message. KeyIdentifierType=BST_DIRECT_REFERENCE; EncAlgo=http://www.w3.org/2009/xmlenc11#aes128-gcm; KeyEncAlgo=RSA_OAEP_XENC11; MgfAlgo=http://www.w3.org/2009/xmlenc11#mgf1sha256; DigestAlgo=http://www.w3.org/2001/04/xmlenc#sha256; CertificateSubjectCN=CN=POP000688,OU=PEPPOL TEST AP,O=SunTec Business Solutions DMCC,C=AE
2025-07-02 11:40:29.171 [http-nio-8081-exec-2] INFO  c.h.phase4.util.AS4ResourceHelper - Converting [[Entity-Class: HttpMimeMessageEntity, Content-Type: multipart/related; 
	boundary="----=_Part_0_1547174460.1751436629106"; 
	type="application/soap+xml"; charset=UTF-8, Content-Encoding: null, chunked: false]; MimeMsg=[com.helger.phase4.messaging.mime.AS4MimeMessage@48b4c90e; IsRepeatable=false]] to a repeatable HTTP entity using file C:\Users\<USER>\AppData\Local\Temp\phase4-res-5185643754159334357.tmp
2025-07-02 11:40:29.224 [http-nio-8081-exec-2] INFO  c.helger.phase4.http.BasicHttpPoster - Starting to transmit AS4 Message to 'http://localhost:8081/reverse-flow/receive-as4-message'
2025-07-02 11:40:30.467 [http-nio-8081-exec-2] INFO  c.helger.phase4.http.BasicHttpPoster - Finished transmitting AS4 Message to 'http://localhost:8081/reverse-flow/receive-as4-message' after 1239 ms
2025-07-02 11:40:30.468 [http-nio-8081-exec-2] INFO  c.h.p.s.AS4BidirectionalClientHelper - Successfully transmitted AS4 UserMessage with message ID '2eec6d48-6bf0-4434-8994-78b25a68d00e@phase4' to 'http://localhost:8081/reverse-flow/receive-as4-message'
2025-07-02 11:40:30.474 [http-nio-8081-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Received Content-Type string: 'application/soap+xml;charset=UTF-8'
2025-07-02 11:40:30.479 [http-nio-8081-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Received Content-Type object: [MimeType@0x224a2bde: contentType=APPLICATION; subType=soap+xml; parameters=[[MimeTypeParameter@0x2afabcde: Attribute=charset; Value=UTF-8; ValueRequiresQuoting=false]]]
2025-07-02 11:40:30.484 [http-nio-8081-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Received plain message
2025-07-02 11:40:30.498 [http-nio-8081-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Successfully parsed payload as XML
2025-07-02 11:40:30.500 [http-nio-8081-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Determined SOAP version SOAP_12 from XML root element namespace URI 'http://www.w3.org/2003/05/soap-envelope'
2025-07-02 11:40:30.503 [http-nio-8081-exec-2] DEBUG c.h.p.s.s.SOAPHeaderElementProcessorRegistry - Successfully registered SOAP header element processor for {http://docs.oasis-open.org/ebxml-msg/ebms/v3.0/ns/core/200704/}Messaging
2025-07-02 11:40:30.505 [http-nio-8081-exec-2] DEBUG c.h.p.s.s.SOAPHeaderElementProcessorRegistry - Successfully registered SOAP header element processor for {http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd}Security
2025-07-02 11:40:30.506 [http-nio-8081-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Received the following SOAP 1.2 document:
2025-07-02 11:40:30.527 [http-nio-8081-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - <?xml version="1.0" encoding="UTF-8"?><soap:Envelope xmlns:eb="http://docs.oasis-open.org/ebxml-msg/ebms/v3.0/ns/core/200704/" xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
  <soap:Header>
    <eb:Messaging soap:mustUnderstand="true">
      <eb:SignalMessage>
        <eb:MessageInfo>
          <eb:Timestamp>2025-07-02T06:10:30.458264200Z</eb:Timestamp>
          <eb:MessageId>5220fc91-fb30-43e2-b122-d39a9516ea1d</eb:MessageId>
          <eb:RefToMessageId>2eec6d48-6bf0-4434-8994-78b25a68d00e@phase4</eb:RefToMessageId>
        </eb:MessageInfo>
        <eb:Receipt>
          <ebbp:NonRepudiationInformation xmlns:ebbp="http://docs.oasis-open.org/ebxml-bp/ebbp-signals-2.0">
            <ebbp:MessagePartNRInformation>
              <ds:Reference xmlns:ds="http://www.w3.org/2000/09/xmldsig#" URI="#_1">
                <ds:DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256"/>
                <ds:DigestValue>z5xMpwVhPLGD53J00zjzSAKCUqC0IOqVxw8NOKu6/GI=</ds:DigestValue>
              </ds:Reference>
            </ebbp:MessagePartNRInformation>
          </ebbp:NonRepudiationInformation>
        </eb:Receipt>
      </eb:SignalMessage>
    </eb:Messaging>
  </soap:Header>
  <soap:Body/>
</soap:Envelope>
2025-07-02 11:40:30.527 [http-nio-8081-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Without any incoming attachments
2025-07-02 11:40:30.533 [http-nio-8081-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Processing SOAP header element {http://docs.oasis-open.org/ebxml-msg/ebms/v3.0/ns/core/200704/}Messaging with processor com.helger.phase4.servlet.soap.SOAPHeaderElementProcessorExtractEbms3Messaging@56a4b1f4
2025-07-02 11:40:30.557 [http-nio-8081-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Message contains no SOAP header element with QName {http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd}Security
2025-07-02 11:40:30.559 [http-nio-8081-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Determined AS4 profile ID 'peppol' for current message
2025-07-02 11:40:38.162 [pool-2-thread-1] INFO  c.h.p.s.AS4BidirectionalClientHelper - Sending AS4 UserMessage to 'http://***************:8080/suntec/as4' with max. 1 retries
2025-07-02 11:40:38.205 [pool-2-thread-1] INFO  c.h.p.messaging.crypto.AS4Signer - Now signing AS4 message [SOAP_12]. KeyIdentifierType=BST_DIRECT_REFERENCE; KeyAlias=cert; SignAlgo=http://www.w3.org/2001/04/xmldsig-more#rsa-sha256; DigestAlgo=http://www.w3.org/2001/04/xmlenc#sha256; C14NAlgo=http://www.w3.org/2001/10/xml-exc-c14n#
2025-07-02 11:40:38.213 [pool-2-thread-1] INFO  c.h.p.messaging.crypto.AS4Encryptor - Now encrypting AS4 MIME message. KeyIdentifierType=BST_DIRECT_REFERENCE; EncAlgo=http://www.w3.org/2009/xmlenc11#aes128-gcm; KeyEncAlgo=RSA_OAEP_XENC11; MgfAlgo=http://www.w3.org/2009/xmlenc11#mgf1sha256; DigestAlgo=http://www.w3.org/2001/04/xmlenc#sha256; CertificateSubjectCN=CN=POP000688,OU=PEPPOL TEST AP,O=SunTec Business Solutions DMCC,C=AE
2025-07-02 11:40:38.227 [pool-2-thread-1] INFO  c.h.phase4.util.AS4ResourceHelper - Converting [[Entity-Class: HttpMimeMessageEntity, Content-Type: multipart/related; 
	boundary="----=_Part_2_726001751.1751436638216"; 
	type="application/soap+xml"; charset=UTF-8, Content-Encoding: null, chunked: false]; MimeMsg=[com.helger.phase4.messaging.mime.AS4MimeMessage@680afcab; IsRepeatable=false]] to a repeatable HTTP entity using file C:\Users\<USER>\AppData\Local\Temp\phase4-res-17417060755331709381.tmp
2025-07-02 11:40:38.255 [pool-2-thread-1] INFO  c.helger.phase4.http.BasicHttpPoster - Starting to transmit AS4 Message to 'http://***************:8080/suntec/as4'
2025-07-02 11:40:39.169 [pool-2-thread-1] INFO  c.helger.phase4.http.BasicHttpPoster - Finished transmitting AS4 Message to 'http://***************:8080/suntec/as4' after 913 ms
2025-07-02 11:40:39.169 [pool-2-thread-1] INFO  c.h.p.s.AS4BidirectionalClientHelper - Successfully transmitted AS4 UserMessage with message ID '502cbf1b-2d7a-41bc-b001-c1dd690b2569@phase4' to 'http://***************:8080/suntec/as4'
2025-07-02 11:40:39.169 [pool-2-thread-1] DEBUG c.h.p.servlet.AS4IncomingHandler - Received Content-Type string: 'application/soap+xml;charset=UTF-8'
2025-07-02 11:40:39.169 [pool-2-thread-1] DEBUG c.h.p.servlet.AS4IncomingHandler - Received Content-Type object: [MimeType@0x3dfe6619: contentType=APPLICATION; subType=soap+xml; parameters=[[MimeTypeParameter@0x48774743: Attribute=charset; Value=UTF-8; ValueRequiresQuoting=false]]]
2025-07-02 11:40:39.170 [pool-2-thread-1] DEBUG c.h.p.servlet.AS4IncomingHandler - Received plain message
2025-07-02 11:40:39.171 [pool-2-thread-1] DEBUG c.h.p.servlet.AS4IncomingHandler - Successfully parsed payload as XML
2025-07-02 11:40:39.172 [pool-2-thread-1] DEBUG c.h.p.servlet.AS4IncomingHandler - Determined SOAP version SOAP_12 from XML root element namespace URI 'http://www.w3.org/2003/05/soap-envelope'
2025-07-02 11:40:39.172 [pool-2-thread-1] DEBUG c.h.p.s.s.SOAPHeaderElementProcessorRegistry - Successfully registered SOAP header element processor for {http://docs.oasis-open.org/ebxml-msg/ebms/v3.0/ns/core/200704/}Messaging
2025-07-02 11:40:39.172 [pool-2-thread-1] DEBUG c.h.p.s.s.SOAPHeaderElementProcessorRegistry - Successfully registered SOAP header element processor for {http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd}Security
2025-07-02 11:40:39.172 [pool-2-thread-1] DEBUG c.h.p.servlet.AS4IncomingHandler - Received the following SOAP 1.2 document:
2025-07-02 11:40:39.194 [pool-2-thread-1] DEBUG c.h.p.servlet.AS4IncomingHandler - <?xml version="1.0" encoding="UTF-8"?><env:Envelope xmlns:env="http://www.w3.org/2003/05/soap-envelope"><env:Header><eb:Messaging xmlns:eb="http://docs.oasis-open.org/ebxml-msg/ebms/v3.0/ns/core/200704/" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" env:mustUnderstand="true" wsu:Id="_1246da6e-edc4-443a-bfd8-8899bfe119dd"><eb:SignalMessage xmlns:ns3="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader" xmlns:ns4="http://schemas.xmlsoap.org/soap/envelope/" xmlns:ns6="http://www.w3.org/2000/09/xmldsig#" xmlns:ns7="http://docs.oasis-open.org/ebxml-bp/ebbp-signals-2.0"><eb:MessageInfo><eb:Timestamp>2025-07-02T11:35:58.940+05:30</eb:Timestamp><eb:MessageId><EMAIL></eb:MessageId><eb:RefToMessageId>502cbf1b-2d7a-41bc-b001-c1dd690b2569@phase4</eb:RefToMessageId></eb:MessageInfo><eb:Receipt><ns7:NonRepudiationInformation><ns7:MessagePartNRInformation><ns6:Reference URI="#phase4-msg-07c000f5-6e0d-464b-94af-1d2a9644d5f1"><ns6:Transforms><ns6:Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#"><ec:InclusiveNamespaces xmlns:S12="http://www.w3.org/2003/05/soap-envelope" xmlns:ds="http://www.w3.org/2000/09/xmldsig#" xmlns:ec="http://www.w3.org/2001/10/xml-exc-c14n#" xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd" PrefixList="S12"/></ns6:Transform></ns6:Transforms><ns6:DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256"/><ns6:DigestValue>brRF41tycsnKWX3RrYfHREsTq4R1Pj3QZzVraP9yBrM=</ns6:DigestValue></ns6:Reference></ns7:MessagePartNRInformation><ns7:MessagePartNRInformation><ns6:Reference URI="#id-d8232041-02e0-464e-bf9e-70076862b4c3"><ns6:Transforms><ns6:Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#"/></ns6:Transforms><ns6:DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256"/><ns6:DigestValue>hfyumGOfkeJMLGQOGo2dQ6m+HuUjePuFb2+KA5vnMIU=</ns6:DigestValue></ns6:Reference></ns7:MessagePartNRInformation><ns7:MessagePartNRInformation><ns6:Reference URI="cid:phase4-att-3502d036-e0b1-4275-bcf3-577ccd3a2af0@cid"><ns6:Transforms><ns6:Transform Algorithm="http://docs.oasis-open.org/wss/oasis-wss-SwAProfile-1.1#Attachment-Content-Signature-Transform"/></ns6:Transforms><ns6:DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256"/><ns6:DigestValue>wmENTwQGTKYbRpuOc6Kb/xX2KvSzZ7GvZKm82M1J3Hw=</ns6:DigestValue></ns6:Reference></ns7:MessagePartNRInformation></ns7:NonRepudiationInformation></eb:Receipt></eb:SignalMessage></eb:Messaging><wsse:Security xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" env:mustUnderstand="true"><wsse:BinarySecurityToken EncodingType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary" ValueType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-x509-token-profile-1.0#X509v3" wsu:Id="G4fcbdeea-5879-4419-8705-ffb58587dfb3">MIIF1jCCA76gAwIBAgIQMgzvzyeZp6t40POzb3eveDANBgkqhkiG9w0BAQsFADBrMQswCQYDVQQGEwJCRTEZMBcGA1UEChMQT3BlblBFUFBPTCBBSVNCTDEWMBQGA1UECxMNRk9SIFRFU1QgT05MWTEpMCcGA1UEAxMgUEVQUE9MIEFDQ0VTUyBQT0lOVCBURVNUIENBIC0gRzIwHhcNMjQwNzEwMDAwMDAwWhcNMjYwNjMwMjM1OTU5WjBjMQswCQYDVQQGEwJBRTEnMCUGA1UECgweU3VuVGVjIEJ1c2luZXNzIFNvbHV0aW9ucyBETUNDMRcwFQYDVQQLDA5QRVBQT0wgVEVTVCBBUDESMBAGA1UEAwwJUE9QMDAwNjg4MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAqhONiEsAp5uWgVgCLesq61ajWEHP1Szh+uj82bCB/HI6xTtwi2/8pu3zma4dcniWmmnO5YtOU0qZ5vgqmeJOnUWw6gc1F52fJ+Aw1Tug9HkM5AOU8pa4B/B1J2nBvys8kmqFUOVu6Qaw45uRIThlpn9yD+Fd+OmLiWpLCP1HS8lNpfOJawQSrDIhPvDJ/fUvg2wBqlL+rgQk3eTBTIvaq/l/elJtfG9DZeUqtH1q58sXmtk3QUounFw/xgInGh7r/zyHOmVr7nroZFYklMWCgBJLi0IjKGDfIPFDcyCr75P0/9xMW4+MpWtTk4oyshGkfjUUt6V8lXRcpZTNCLtJEQIDAQABo4IBfDCCAXgwDAYDVR0TAQH/BAIwADAOBgNVHQ8BAf8EBAMCA6gwFgYDVR0lAQH/BAwwCgYIKwYBBQUHAwIwHQYDVR0OBBYEFJV3L4wCnCE32Hx3o6IfiIy5EziVMF0GA1UdHwRWMFQwUqBQoE6GTGh0dHA6Ly9wa2ktY3JsLnN5bWF1dGguY29tL2NhXzZhOTM3NzM0YTM5M2EwODA1YmYzM2NkYThiMzMxMDkzL0xhdGVzdENSTC5jcmwwNwYIKwYBBQUHAQEEKzApMCcGCCsGAQUFBzABhhtodHRwOi8vcGtpLW9jc3Auc3ltYXV0aC5jb20wHwYDVR0jBBgwFoAUa29LtvE3uis8fxjNuiuyuXwqN+swLQYKYIZIAYb4RQEQAwQfMB0GE2CGSAGG+EUBEAECAwEBgamQ4QMWBjk1NzYwODA5BgpghkgBhvhFARAFBCswKQIBABYkYUhSMGNITTZMeTl3YTJrdGNtRXVjM2x0WVhWMGFDNWpiMjA9MA0GCSqGSIb3DQEBCwUAA4ICAQBTlb8BNoleok32WxiWdGiFFaP9zExVg/I71FpiVzirOsIhY5gj5CeRY4RrJD+lD32d9LaWh58H3VEpVEMfQKJM7g87GySX33OdHdcAU28j8zgdD2HjCd3I4KcGH7eGbFhWn2SjBedqwX1U0zZn8K9SYnfSswsCiQmHZV+BJqCq0oie2S+JPREvFskXydTUv14FocAu61zynyKwuXP6R9eMOTxbivRLGU6KWZnCc8w+EKjlj/EYCYvkCh55iTPK9T1Nkt6ZUcFMyPPuGfdcjwrO2Dl4DCj2Db7/wHa/bF9veZVlDyJmIURv6JjG7JFuMRJcC4tC/3YU6d2VyP9I9i1gYl7QRS0MDt7cKLM7qzQSZ/j9EJWFXWXI5lcMp6nud00Znbbk7kwS3SLqoQFOJAm3GKfOaFipS1BvLFzS5bl1gGtlJVXqhcsv2GlFFGbxm99l3AWjMusF5YpFNVbWfb5NEQ9FC2grfuCLc/pUA3SYYUi7H+jzP/zeLuwWJMLtZSApfzAmH4J1T7S407FAJlX87EsXShS5sBZFxPivzClX4SgGTNUQ+1DsuWFdk4XBd2Dy8YH9q22dBwhfMwMoO2tiuo0CKAIi7M3o0vluDVnyunIngZZL4VRGqhEBSxTHp2yVeFDWRUwq3uc+bOlJ+W9mnYzbSRH1YWXY2X2EGQeB/g==</wsse:BinarySecurityToken><wsse:BinarySecurityToken EncodingType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary" ValueType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-x509-token-profile-1.0#X509v3" wsu:Id="X509-e30e33a3-bc3e-45c9-878e-2e2258d04757">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</wsse:BinarySecurityToken><ds:Signature xmlns:ds="http://www.w3.org/2000/09/xmldsig#" Id="SIG-3bff8d4a-3d57-4794-a937-1f4ec482bd50"><ds:SignedInfo><ds:CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#"><ec:InclusiveNamespaces xmlns:ec="http://www.w3.org/2001/10/xml-exc-c14n#" PrefixList="env"/></ds:CanonicalizationMethod><ds:SignatureMethod Algorithm="http://www.w3.org/2001/04/xmldsig-more#rsa-sha256"/><ds:Reference URI="#_2c603a66-5162-4fc8-9732-9a69ad336bee"><ds:Transforms><ds:Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#"/></ds:Transforms><ds:DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256"/><ds:DigestValue>JbMXeSo58wtyRInZzrwvm/67/cf1GkFCjVjPfqlF6ho=</ds:DigestValue></ds:Reference><ds:Reference URI="#_1246da6e-edc4-443a-bfd8-8899bfe119dd"><ds:Transforms><ds:Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#"/></ds:Transforms><ds:DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256"/><ds:DigestValue>rjbIu+R1XQ/ecnsqxE1vYc6fIcwYqkKCBfDtXUFkaQA=</ds:DigestValue></ds:Reference></ds:SignedInfo><ds:SignatureValue>H9NWgJa2D6yLaUeM5Mj8n9ZKZyAttGQl+DqXmCQjk8yHmPeqwjoTxLKps0XLgaFj96e1viDEV5f3MkJA2u8LpSbLNqosgYY3fTl0zPQxn+aLZNPSNOfhVqA0QZCp3WXsRxgcn0MptEb6/xbbfsLTUhRF1q07arYGrb9wMxVPouWaHJyTXqNIlvavrq3fp7pjHdw9XmYtN/40n/qDAKnh07iUR5IqrLXMwF6djLb3XnnhO+OmrS1Z4YfdPbo9UqfTlILlJcf2mrjeN7nlAt07tx0pGdHIr2igP14pkLYKDfck2ou3779UMnffAvhP1Zs3WBh1BOgdLvK4wl0doCdXyw==</ds:SignatureValue><ds:KeyInfo Id="KI-c7b4dc07-d80f-4342-bc02-f4197c7d726e"><wsse:SecurityTokenReference wsu:Id="STR-85f74fbe-fc2e-412c-a37f-973f83d89ac6"><wsse:Reference URI="#X509-e30e33a3-bc3e-45c9-878e-2e2258d04757" ValueType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-x509-token-profile-1.0#X509v3"/></wsse:SecurityTokenReference></ds:KeyInfo></ds:Signature></wsse:Security></env:Header><env:Body xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" wsu:Id="_2c603a66-5162-4fc8-9732-9a69ad336bee"/></env:Envelope>
2025-07-02 11:40:39.195 [pool-2-thread-1] DEBUG c.h.p.servlet.AS4IncomingHandler - Without any incoming attachments
2025-07-02 11:40:39.195 [pool-2-thread-1] DEBUG c.h.p.servlet.AS4IncomingHandler - Processing SOAP header element {http://docs.oasis-open.org/ebxml-msg/ebms/v3.0/ns/core/200704/}Messaging with processor com.helger.phase4.servlet.soap.SOAPHeaderElementProcessorExtractEbms3Messaging@1fe2aec6
2025-07-02 11:40:39.222 [pool-2-thread-1] DEBUG c.h.p.servlet.AS4IncomingHandler - Processing SOAP header element {http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd}Security with processor com.helger.phase4.servlet.soap.SOAPHeaderElementProcessorWSS4J@3ae1e1bb
2025-07-02 11:40:39.223 [pool-2-thread-1] DEBUG c.h.p.s.s.SOAPHeaderElementProcessorWSS4J - Using signature algorithm RSA_SHA_256
2025-07-02 11:40:39.223 [pool-2-thread-1] DEBUG c.h.p.s.s.SOAPHeaderElementProcessorWSS4J - Using signature digest algorithm DIGEST_SHA_256
2025-07-02 11:40:39.263 [pool-2-thread-1] DEBUG c.h.p.s.s.SOAPHeaderElementProcessorWSS4J - WSSecurityEngineResult: {signature-value=[B@5bdac4ec, subject=null, canonicalization-method=http://www.w3.org/2001/10/xml-exc-c14n#, public-key=null, x509-certificates=[Ljava.security.cert.X509Certificate;@50adf372, signature-method=http://www.w3.org/2001/04/xmldsig-more#rsa-sha256, secret=null, principal=CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE, data-ref-uris=[org.apache.wss4j.dom.WSDataRef@3630c96c, org.apache.wss4j.dom.WSDataRef@2d84f8ba], x509-reference-type=DIRECT_REF, x509-certificate=[
[
  Version: V3
  Subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE
  Signature Algorithm: SHA256withRSA, OID = 1.2.840.113549.1.1.11

  Key:  Sun RSA public key, 2048 bits
  params: null
  modulus: 21470153719305158626303552905005207152603605085913778941492168201002130174642884995372195549283201411428777146409037348532374969351342072972078523157757385500688645209491010737311038646758631839607940957248039046297983634871428903954685662119707376479694559746997573652530378040752895324417135618276092473900092401593040623448453502917482120180275130727806976475533595494905290614422801920280639805100130747981626750767187194125840771161192927382727034999647747577608846403802133479374933747729458864416020154057313844444387408554562130897723119878409312187158825108668803636017688353983707066458325445047651948775697
  public exponent: 65537
  Validity: [From: Wed Jul 10 05:30:00 IST 2024,
               To: Wed Jul 01 05:29:59 IST 2026]
  Issuer: CN=PEPPOL ACCESS POINT TEST CA - G2, OU=FOR TEST ONLY, O=OpenPEPPOL AISBL, C=BE
  SerialNumber: [    320cefcf 2799a7ab 78d0f3b3 6f77af78]

Certificate Extensions: 9
[1]: ObjectId: 2.16.840.1.113733.1.16.3 Criticality=false
Extension unknown: DER encoded OCTET string =
0000: 04 1F 30 1D 06 13 60 86   48 01 86 F8 45 01 10 01  ..0...`.H...E...
0010: 02 03 01 01 81 A9 90 E1   03 16 06 39 35 37 36 30  ...........95760
0020: 38                                                 8


[2]: ObjectId: 2.16.840.1.113733.1.16.5 Criticality=false
Extension unknown: DER encoded OCTET string =
0000: 04 2B 30 29 02 01 00 16   24 61 48 52 30 63 48 4D  .+0)....$aHR0cHM
0010: 36 4C 79 39 77 61 32 6B   74 63 6D 45 75 63 33 6C  6Ly9wa2ktcmEuc3l
0020: 74 59 58 56 30 61 43 35   6A 62 32 30 3D           tYXV0aC5jb20=


[3]: ObjectId: 1.3.6.1.5.5.7.1.1 Criticality=false
AuthorityInfoAccess [
  [
   accessMethod: ocsp
   accessLocation: URIName: http://pki-ocsp.symauth.com
]
]

[4]: ObjectId: 2.5.29.35 Criticality=false
AuthorityKeyIdentifier [
KeyIdentifier [
0000: 6B 6F 4B B6 F1 37 BA 2B   3C 7F 18 CD BA 2B B2 B9  koK..7.+<....+..
0010: 7C 2A 37 EB                                        .*7.
]
]

[5]: ObjectId: ********* Criticality=true
BasicConstraints:[
  CA:false
  PathLen: undefined
]

[6]: ObjectId: ********* Criticality=false
CRLDistributionPoints [
  [DistributionPoint:
     [URIName: http://pki-crl.symauth.com/ca_6a937734a393a0805bf33cda8b331093/LatestCRL.crl]
]]

[7]: ObjectId: ********* Criticality=true
ExtendedKeyUsages [
  clientAuth
]

[8]: ObjectId: ********* Criticality=true
KeyUsage [
  DigitalSignature
  Key_Encipherment
  Key_Agreement
]

[9]: ObjectId: ********* Criticality=false
SubjectKeyIdentifier [
KeyIdentifier [
0000: 95 77 2F 8C 02 9C 21 37   D8 7C 77 A3 A2 1F 88 8C  .w/...!7..w.....
0010: B9 13 38 95                                        ..8.
]
]

]
  Algorithm: [SHA256withRSA]
  Signature:
0000: 53 95 BF 01 36 89 5E A2   4D F6 5B 18 96 74 68 85  S...6.^.M.[..th.
0010: 15 A3 FD CC 4C 55 83 F2   3B D4 5A 62 57 38 AB 3A  ....LU..;.ZbW8.:
0020: C2 21 63 98 23 E4 27 91   63 84 6B 24 3F A5 0F 7D  .!c.#.'.c.k$?...
0030: 9D F4 B6 96 87 9F 07 DD   51 29 54 43 1F 40 A2 4C  ........Q)TC.@.L
0040: EE 0F 3B 1B 24 97 DF 73   9D 1D D7 00 53 6F 23 F3  ..;.$..s....So#.
0050: 38 1D 0F 61 E3 09 DD C8   E0 A7 06 1F B7 86 6C 58  8..a..........lX
0060: 56 9F 64 A3 05 E7 6A C1   7D 54 D3 36 67 F0 AF 52  V.d...j..T.6g..R
0070: 62 77 D2 B3 0B 02 89 09   87 65 5F 81 26 A0 AA D2  bw.......e_.&...
0080: 88 9E D9 2F 89 3D 11 2F   16 C9 17 C9 D4 D4 BF 5E  .../.=./.......^
0090: 05 A1 C0 2E EB 5C F2 9F   22 B0 B9 73 FA 47 D7 8C  .....\.."..s.G..
00A0: 39 3C 5B 8A F4 4B 19 4E   8A 59 99 C2 73 CC 3E 10  9<[..K.N.Y..s.>.
00B0: A8 E5 8F F1 18 09 8B E4   0A 1E 79 89 33 CA F5 3D  ..........y.3..=
00C0: 4D 92 DE 99 51 C1 4C C8   F3 EE 19 F7 5C 8F 0A CE  M...Q.L.....\...
00D0: D8 39 78 0C 28 F6 0D BE   FF C0 76 BF 6C 5F 6F 79  .9x.(.....v.l_oy
00E0: 95 65 0F 22 66 21 44 6F   E8 98 C6 EC 91 6E 31 12  .e."f!Do.....n1.
00F0: 5C 0B 8B 42 FF 76 14 E9   DD 95 C8 FF 48 F6 2D 60  \..B.v......H.-`
0100: 62 5E D0 45 2D 0C 0E DE   DC 28 B3 3B AB 34 12 67  b^.E-....(.;.4.g
0110: F8 FD 10 95 85 5D 65 C8   E6 57 0C A7 A9 EE 77 4D  .....]e..W....wM
0120: 19 9D B6 E4 EE 4C 12 DD   22 EA A1 01 4E 24 09 B7  .....L.."...N$..
0130: 18 A7 CE 68 58 A9 4B 50   6F 2C 5C D2 E5 B9 75 80  ...hX.KPo,\...u.
0140: 6B 65 25 55 EA 85 CB 2F   D8 69 45 14 66 F1 9B DF  ke%U.../.iE.f...
0150: 65 DC 05 A3 32 EB 05 E5   8A 45 35 56 D6 7D BE 4D  e...2....E5V...M
0160: 11 0F 45 0B 68 2B 7E E0   8B 73 FA 54 03 74 98 61  ..E.h+...s.T.t.a
0170: 48 BB 1F E8 F3 3F FC DE   2E EC 16 24 C2 ED 65 20  H....?.....$..e 
0180: 29 7F 30 26 1F 82 75 4F   B4 B8 D3 B1 40 26 55 FC  ).0&..uO....@&U.
0190: EC 4B 17 4A 14 B9 B0 16   45 C4 F8 AF CC 29 57 E1  .K.J....E....)W.
01A0: 28 06 4C D5 10 FB 50 EC   B9 61 5D 93 85 C1 77 60  (.L...P..a]...w`
01B0: F2 F1 81 FD AB 6D 9D 07   08 5F 33 03 28 3B 6B 62  .....m..._3.(;kb
01C0: BA 8D 02 28 02 22 EC CD   E8 D2 F9 6E 0D 59 F2 BA  ...(.".....n.Y..
01D0: 72 27 81 96 4B E1 54 46   AA 11 01 4B 14 C7 A7 6C  r'..K.TF...K...l
01E0: 95 78 50 D6 45 4C 2A DE   E7 3E 6C E9 49 F9 6F 66  .xP.EL*..>l.I.of
01F0: 9D 8C DB 49 11 F5 61 65   D8 D9 7D 84 19 07 81 FE  ...I..ae........

], validated-token=true, action=2, id=SIG-3bff8d4a-3d57-4794-a937-1f4ec482bd50, token-element=[ds:Signature: null]}
2025-07-02 11:40:39.270 [pool-2-thread-1] DEBUG c.h.p.s.s.SOAPHeaderElementProcessorWSS4J - WSSecurityEngineResult: {binary-security-token=<wsse:BinarySecurityToken EncodingType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary" ValueType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-x509-token-profile-1.0#X509v3" wsu:Id="X509-e30e33a3-bc3e-45c9-878e-2e2258d04757">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</wsse:BinarySecurityToken>, x509-certificate=[
[
  Version: V3
  Subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE
  Signature Algorithm: SHA256withRSA, OID = 1.2.840.113549.1.1.11

  Key:  Sun RSA public key, 2048 bits
  params: null
  modulus: 21470153719305158626303552905005207152603605085913778941492168201002130174642884995372195549283201411428777146409037348532374969351342072972078523157757385500688645209491010737311038646758631839607940957248039046297983634871428903954685662119707376479694559746997573652530378040752895324417135618276092473900092401593040623448453502917482120180275130727806976475533595494905290614422801920280639805100130747981626750767187194125840771161192927382727034999647747577608846403802133479374933747729458864416020154057313844444387408554562130897723119878409312187158825108668803636017688353983707066458325445047651948775697
  public exponent: 65537
  Validity: [From: Wed Jul 10 05:30:00 IST 2024,
               To: Wed Jul 01 05:29:59 IST 2026]
  Issuer: CN=PEPPOL ACCESS POINT TEST CA - G2, OU=FOR TEST ONLY, O=OpenPEPPOL AISBL, C=BE
  SerialNumber: [    320cefcf 2799a7ab 78d0f3b3 6f77af78]

Certificate Extensions: 9
[1]: ObjectId: 2.16.840.1.113733.1.16.3 Criticality=false
Extension unknown: DER encoded OCTET string =
0000: 04 1F 30 1D 06 13 60 86   48 01 86 F8 45 01 10 01  ..0...`.H...E...
0010: 02 03 01 01 81 A9 90 E1   03 16 06 39 35 37 36 30  ...........95760
0020: 38                                                 8


[2]: ObjectId: 2.16.840.1.113733.1.16.5 Criticality=false
Extension unknown: DER encoded OCTET string =
0000: 04 2B 30 29 02 01 00 16   24 61 48 52 30 63 48 4D  .+0)....$aHR0cHM
0010: 36 4C 79 39 77 61 32 6B   74 63 6D 45 75 63 33 6C  6Ly9wa2ktcmEuc3l
0020: 74 59 58 56 30 61 43 35   6A 62 32 30 3D           tYXV0aC5jb20=


[3]: ObjectId: 1.3.6.1.5.5.7.1.1 Criticality=false
AuthorityInfoAccess [
  [
   accessMethod: ocsp
   accessLocation: URIName: http://pki-ocsp.symauth.com
]
]

[4]: ObjectId: 2.5.29.35 Criticality=false
AuthorityKeyIdentifier [
KeyIdentifier [
0000: 6B 6F 4B B6 F1 37 BA 2B   3C 7F 18 CD BA 2B B2 B9  koK..7.+<....+..
0010: 7C 2A 37 EB                                        .*7.
]
]

[5]: ObjectId: ********* Criticality=true
BasicConstraints:[
  CA:false
  PathLen: undefined
]

[6]: ObjectId: ********* Criticality=false
CRLDistributionPoints [
  [DistributionPoint:
     [URIName: http://pki-crl.symauth.com/ca_6a937734a393a0805bf33cda8b331093/LatestCRL.crl]
]]

[7]: ObjectId: ********* Criticality=true
ExtendedKeyUsages [
  clientAuth
]

[8]: ObjectId: ********* Criticality=true
KeyUsage [
  DigitalSignature
  Key_Encipherment
  Key_Agreement
]

[9]: ObjectId: ********* Criticality=false
SubjectKeyIdentifier [
KeyIdentifier [
0000: 95 77 2F 8C 02 9C 21 37   D8 7C 77 A3 A2 1F 88 8C  .w/...!7..w.....
0010: B9 13 38 95                                        ..8.
]
]

]
  Algorithm: [SHA256withRSA]
  Signature:
0000: 53 95 BF 01 36 89 5E A2   4D F6 5B 18 96 74 68 85  S...6.^.M.[..th.
0010: 15 A3 FD CC 4C 55 83 F2   3B D4 5A 62 57 38 AB 3A  ....LU..;.ZbW8.:
0020: C2 21 63 98 23 E4 27 91   63 84 6B 24 3F A5 0F 7D  .!c.#.'.c.k$?...
0030: 9D F4 B6 96 87 9F 07 DD   51 29 54 43 1F 40 A2 4C  ........Q)TC.@.L
0040: EE 0F 3B 1B 24 97 DF 73   9D 1D D7 00 53 6F 23 F3  ..;.$..s....So#.
0050: 38 1D 0F 61 E3 09 DD C8   E0 A7 06 1F B7 86 6C 58  8..a..........lX
0060: 56 9F 64 A3 05 E7 6A C1   7D 54 D3 36 67 F0 AF 52  V.d...j..T.6g..R
0070: 62 77 D2 B3 0B 02 89 09   87 65 5F 81 26 A0 AA D2  bw.......e_.&...
0080: 88 9E D9 2F 89 3D 11 2F   16 C9 17 C9 D4 D4 BF 5E  .../.=./.......^
0090: 05 A1 C0 2E EB 5C F2 9F   22 B0 B9 73 FA 47 D7 8C  .....\.."..s.G..
00A0: 39 3C 5B 8A F4 4B 19 4E   8A 59 99 C2 73 CC 3E 10  9<[..K.N.Y..s.>.
00B0: A8 E5 8F F1 18 09 8B E4   0A 1E 79 89 33 CA F5 3D  ..........y.3..=
00C0: 4D 92 DE 99 51 C1 4C C8   F3 EE 19 F7 5C 8F 0A CE  M...Q.L.....\...
00D0: D8 39 78 0C 28 F6 0D BE   FF C0 76 BF 6C 5F 6F 79  .9x.(.....v.l_oy
00E0: 95 65 0F 22 66 21 44 6F   E8 98 C6 EC 91 6E 31 12  .e."f!Do.....n1.
00F0: 5C 0B 8B 42 FF 76 14 E9   DD 95 C8 FF 48 F6 2D 60  \..B.v......H.-`
0100: 62 5E D0 45 2D 0C 0E DE   DC 28 B3 3B AB 34 12 67  b^.E-....(.;.4.g
0110: F8 FD 10 95 85 5D 65 C8   E6 57 0C A7 A9 EE 77 4D  .....]e..W....wM
0120: 19 9D B6 E4 EE 4C 12 DD   22 EA A1 01 4E 24 09 B7  .....L.."...N$..
0130: 18 A7 CE 68 58 A9 4B 50   6F 2C 5C D2 E5 B9 75 80  ...hX.KPo,\...u.
0140: 6B 65 25 55 EA 85 CB 2F   D8 69 45 14 66 F1 9B DF  ke%U.../.iE.f...
0150: 65 DC 05 A3 32 EB 05 E5   8A 45 35 56 D6 7D BE 4D  e...2....E5V...M
0160: 11 0F 45 0B 68 2B 7E E0   8B 73 FA 54 03 74 98 61  ..E.h+...s.T.t.a
0170: 48 BB 1F E8 F3 3F FC DE   2E EC 16 24 C2 ED 65 20  H....?.....$..e 
0180: 29 7F 30 26 1F 82 75 4F   B4 B8 D3 B1 40 26 55 FC  ).0&..uO....@&U.
0190: EC 4B 17 4A 14 B9 B0 16   45 C4 F8 AF CC 29 57 E1  .K.J....E....)W.
01A0: 28 06 4C D5 10 FB 50 EC   B9 61 5D 93 85 C1 77 60  (.L...P..a]...w`
01B0: F2 F1 81 FD AB 6D 9D 07   08 5F 33 03 28 3B 6B 62  .....m..._3.(;kb
01C0: BA 8D 02 28 02 22 EC CD   E8 D2 F9 6E 0D 59 F2 BA  ...(.".....n.Y..
01D0: 72 27 81 96 4B E1 54 46   AA 11 01 4B 14 C7 A7 6C  r'..K.TF...K...l
01E0: 95 78 50 D6 45 4C 2A DE   E7 3E 6C E9 49 F9 6F 66  .xP.EL*..>l.I.of
01F0: 9D 8C DB 49 11 F5 61 65   D8 D9 7D 84 19 07 81 FE  ...I..ae........

], validated-token=false, action=4096, x509-certificates=[Ljava.security.cert.X509Certificate;@50adf372, id=X509-e30e33a3-bc3e-45c9-878e-2e2258d04757, token-element=[wsse:BinarySecurityToken: null]}
2025-07-02 11:40:39.281 [pool-2-thread-1] DEBUG c.h.p.s.s.SOAPHeaderElementProcessorWSS4J - WSSecurityEngineResult: {binary-security-token=<wsse:BinarySecurityToken EncodingType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary" ValueType="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-x509-token-profile-1.0#X509v3" wsu:Id="G4fcbdeea-5879-4419-8705-ffb58587dfb3">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</wsse:BinarySecurityToken>, x509-certificate=[
[
  Version: V3
  Subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE
  Signature Algorithm: SHA256withRSA, OID = 1.2.840.113549.1.1.11

  Key:  Sun RSA public key, 2048 bits
  params: null
  modulus: 21470153719305158626303552905005207152603605085913778941492168201002130174642884995372195549283201411428777146409037348532374969351342072972078523157757385500688645209491010737311038646758631839607940957248039046297983634871428903954685662119707376479694559746997573652530378040752895324417135618276092473900092401593040623448453502917482120180275130727806976475533595494905290614422801920280639805100130747981626750767187194125840771161192927382727034999647747577608846403802133479374933747729458864416020154057313844444387408554562130897723119878409312187158825108668803636017688353983707066458325445047651948775697
  public exponent: 65537
  Validity: [From: Wed Jul 10 05:30:00 IST 2024,
               To: Wed Jul 01 05:29:59 IST 2026]
  Issuer: CN=PEPPOL ACCESS POINT TEST CA - G2, OU=FOR TEST ONLY, O=OpenPEPPOL AISBL, C=BE
  SerialNumber: [    320cefcf 2799a7ab 78d0f3b3 6f77af78]

Certificate Extensions: 9
[1]: ObjectId: 2.16.840.1.113733.1.16.3 Criticality=false
Extension unknown: DER encoded OCTET string =
0000: 04 1F 30 1D 06 13 60 86   48 01 86 F8 45 01 10 01  ..0...`.H...E...
0010: 02 03 01 01 81 A9 90 E1   03 16 06 39 35 37 36 30  ...........95760
0020: 38                                                 8


[2]: ObjectId: 2.16.840.1.113733.1.16.5 Criticality=false
Extension unknown: DER encoded OCTET string =
0000: 04 2B 30 29 02 01 00 16   24 61 48 52 30 63 48 4D  .+0)....$aHR0cHM
0010: 36 4C 79 39 77 61 32 6B   74 63 6D 45 75 63 33 6C  6Ly9wa2ktcmEuc3l
0020: 74 59 58 56 30 61 43 35   6A 62 32 30 3D           tYXV0aC5jb20=


[3]: ObjectId: 1.3.6.1.5.5.7.1.1 Criticality=false
AuthorityInfoAccess [
  [
   accessMethod: ocsp
   accessLocation: URIName: http://pki-ocsp.symauth.com
]
]

[4]: ObjectId: 2.5.29.35 Criticality=false
AuthorityKeyIdentifier [
KeyIdentifier [
0000: 6B 6F 4B B6 F1 37 BA 2B   3C 7F 18 CD BA 2B B2 B9  koK..7.+<....+..
0010: 7C 2A 37 EB                                        .*7.
]
]

[5]: ObjectId: ********* Criticality=true
BasicConstraints:[
  CA:false
  PathLen: undefined
]

[6]: ObjectId: ********* Criticality=false
CRLDistributionPoints [
  [DistributionPoint:
     [URIName: http://pki-crl.symauth.com/ca_6a937734a393a0805bf33cda8b331093/LatestCRL.crl]
]]

[7]: ObjectId: ********* Criticality=true
ExtendedKeyUsages [
  clientAuth
]

[8]: ObjectId: ********* Criticality=true
KeyUsage [
  DigitalSignature
  Key_Encipherment
  Key_Agreement
]

[9]: ObjectId: ********* Criticality=false
SubjectKeyIdentifier [
KeyIdentifier [
0000: 95 77 2F 8C 02 9C 21 37   D8 7C 77 A3 A2 1F 88 8C  .w/...!7..w.....
0010: B9 13 38 95                                        ..8.
]
]

]
  Algorithm: [SHA256withRSA]
  Signature:
0000: 53 95 BF 01 36 89 5E A2   4D F6 5B 18 96 74 68 85  S...6.^.M.[..th.
0010: 15 A3 FD CC 4C 55 83 F2   3B D4 5A 62 57 38 AB 3A  ....LU..;.ZbW8.:
0020: C2 21 63 98 23 E4 27 91   63 84 6B 24 3F A5 0F 7D  .!c.#.'.c.k$?...
0030: 9D F4 B6 96 87 9F 07 DD   51 29 54 43 1F 40 A2 4C  ........Q)TC.@.L
0040: EE 0F 3B 1B 24 97 DF 73   9D 1D D7 00 53 6F 23 F3  ..;.$..s....So#.
0050: 38 1D 0F 61 E3 09 DD C8   E0 A7 06 1F B7 86 6C 58  8..a..........lX
0060: 56 9F 64 A3 05 E7 6A C1   7D 54 D3 36 67 F0 AF 52  V.d...j..T.6g..R
0070: 62 77 D2 B3 0B 02 89 09   87 65 5F 81 26 A0 AA D2  bw.......e_.&...
0080: 88 9E D9 2F 89 3D 11 2F   16 C9 17 C9 D4 D4 BF 5E  .../.=./.......^
0090: 05 A1 C0 2E EB 5C F2 9F   22 B0 B9 73 FA 47 D7 8C  .....\.."..s.G..
00A0: 39 3C 5B 8A F4 4B 19 4E   8A 59 99 C2 73 CC 3E 10  9<[..K.N.Y..s.>.
00B0: A8 E5 8F F1 18 09 8B E4   0A 1E 79 89 33 CA F5 3D  ..........y.3..=
00C0: 4D 92 DE 99 51 C1 4C C8   F3 EE 19 F7 5C 8F 0A CE  M...Q.L.....\...
00D0: D8 39 78 0C 28 F6 0D BE   FF C0 76 BF 6C 5F 6F 79  .9x.(.....v.l_oy
00E0: 95 65 0F 22 66 21 44 6F   E8 98 C6 EC 91 6E 31 12  .e."f!Do.....n1.
00F0: 5C 0B 8B 42 FF 76 14 E9   DD 95 C8 FF 48 F6 2D 60  \..B.v......H.-`
0100: 62 5E D0 45 2D 0C 0E DE   DC 28 B3 3B AB 34 12 67  b^.E-....(.;.4.g
0110: F8 FD 10 95 85 5D 65 C8   E6 57 0C A7 A9 EE 77 4D  .....]e..W....wM
0120: 19 9D B6 E4 EE 4C 12 DD   22 EA A1 01 4E 24 09 B7  .....L.."...N$..
0130: 18 A7 CE 68 58 A9 4B 50   6F 2C 5C D2 E5 B9 75 80  ...hX.KPo,\...u.
0140: 6B 65 25 55 EA 85 CB 2F   D8 69 45 14 66 F1 9B DF  ke%U.../.iE.f...
0150: 65 DC 05 A3 32 EB 05 E5   8A 45 35 56 D6 7D BE 4D  e...2....E5V...M
0160: 11 0F 45 0B 68 2B 7E E0   8B 73 FA 54 03 74 98 61  ..E.h+...s.T.t.a
0170: 48 BB 1F E8 F3 3F FC DE   2E EC 16 24 C2 ED 65 20  H....?.....$..e 
0180: 29 7F 30 26 1F 82 75 4F   B4 B8 D3 B1 40 26 55 FC  ).0&..uO....@&U.
0190: EC 4B 17 4A 14 B9 B0 16   45 C4 F8 AF CC 29 57 E1  .K.J....E....)W.
01A0: 28 06 4C D5 10 FB 50 EC   B9 61 5D 93 85 C1 77 60  (.L...P..a]...w`
01B0: F2 F1 81 FD AB 6D 9D 07   08 5F 33 03 28 3B 6B 62  .....m..._3.(;kb
01C0: BA 8D 02 28 02 22 EC CD   E8 D2 F9 6E 0D 59 F2 BA  ...(.".....n.Y..
01D0: 72 27 81 96 4B E1 54 46   AA 11 01 4B 14 C7 A7 6C  r'..K.TF...K...l
01E0: 95 78 50 D6 45 4C 2A DE   E7 3E 6C E9 49 F9 6F 66  .xP.EL*..>l.I.of
01F0: 9D 8C DB 49 11 F5 61 65   D8 D9 7D 84 19 07 81 FE  ...I..ae........

], validated-token=false, action=4096, x509-certificates=[Ljava.security.cert.X509Certificate;@16af71b7, id=G4fcbdeea-5879-4419-8705-ffb58587dfb3, token-element=[wsse:BinarySecurityToken: null]}
2025-07-02 11:40:39.281 [pool-2-thread-1] DEBUG c.h.p.servlet.AS4IncomingHandler - Determined AS4 profile ID 'peppol' for current message
2025-07-02 11:41:28.408 [SpringApplicationShutdownHook] INFO  c.helger.phase4.wss.WSSConfigManager - Cleaning up WSSConfig. Security Providers will also be removed.
2025-07-02 12:15:44.038 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - MetaAS4Manager is initialized with in-memory data structures
2025-07-02 12:15:44.054 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - Creating AS4 managers using factory class com.helger.phase4.mgr.AS4ManagerFactoryInMemory
2025-07-02 12:15:44.101 [main] INFO  c.h.phase4.profile.AS4ProfileManager - 1 AS4 profile is registered 
2025-07-02 12:15:44.101 [main] INFO  c.helger.phase4.mgr.MetaAS4Manager - MetaAS4Manager was initialized
2025-07-02 12:16:05.028 [http-nio-8081-exec-2] INFO  c.h.p.s.AS4BidirectionalClientHelper - Sending AS4 UserMessage to 'http://localhost:8081/reverse-flow/receive-as4-message' with max. 1 retries
2025-07-02 12:16:05.680 [http-nio-8081-exec-2] INFO  c.helger.phase4.wss.WSSConfigManager - None of the WSSConfig Security Providers is already installed - doing it now
2025-07-02 12:16:05.770 [http-nio-8081-exec-2] INFO  c.h.p.messaging.crypto.AS4Signer - Now signing AS4 message [SOAP_12]. KeyIdentifierType=BST_DIRECT_REFERENCE; KeyAlias=cert; SignAlgo=http://www.w3.org/2001/04/xmldsig-more#rsa-sha256; DigestAlgo=http://www.w3.org/2001/04/xmlenc#sha256; C14NAlgo=http://www.w3.org/2001/10/xml-exc-c14n#
2025-07-02 12:16:05.872 [http-nio-8081-exec-2] INFO  c.h.p.messaging.crypto.AS4Encryptor - Now encrypting AS4 MIME message. KeyIdentifierType=BST_DIRECT_REFERENCE; EncAlgo=http://www.w3.org/2009/xmlenc11#aes128-gcm; KeyEncAlgo=RSA_OAEP_XENC11; MgfAlgo=http://www.w3.org/2009/xmlenc11#mgf1sha256; DigestAlgo=http://www.w3.org/2001/04/xmlenc#sha256; CertificateSubjectCN=CN=POP000688,OU=PEPPOL TEST AP,O=SunTec Business Solutions DMCC,C=AE
2025-07-02 12:16:06.059 [http-nio-8081-exec-2] INFO  c.h.phase4.util.AS4ResourceHelper - Converting [[Entity-Class: HttpMimeMessageEntity, Content-Type: multipart/related; 
	boundary="----=_Part_0_1751086285.1751438765981"; 
	type="application/soap+xml"; charset=UTF-8, Content-Encoding: null, chunked: false]; MimeMsg=[com.helger.phase4.messaging.mime.AS4MimeMessage@2d6c84bd; IsRepeatable=false]] to a repeatable HTTP entity using file C:\Users\<USER>\AppData\Local\Temp\phase4-res-400616220189574940.tmp
2025-07-02 12:16:06.111 [http-nio-8081-exec-2] INFO  c.helger.phase4.http.BasicHttpPoster - Starting to transmit AS4 Message to 'http://localhost:8081/reverse-flow/receive-as4-message'
2025-07-02 12:16:07.570 [http-nio-8081-exec-2] INFO  c.helger.phase4.http.BasicHttpPoster - Finished transmitting AS4 Message to 'http://localhost:8081/reverse-flow/receive-as4-message' after 1455 ms
2025-07-02 12:16:07.571 [http-nio-8081-exec-2] INFO  c.h.p.s.AS4BidirectionalClientHelper - Successfully transmitted AS4 UserMessage with message ID '5360244b-470c-4645-9797-542648b9db45@phase4' to 'http://localhost:8081/reverse-flow/receive-as4-message'
2025-07-02 12:16:07.578 [http-nio-8081-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Received Content-Type string: 'application/soap+xml;charset=UTF-8'
2025-07-02 12:16:07.583 [http-nio-8081-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Received Content-Type object: [MimeType@0x1040b8dc: contentType=APPLICATION; subType=soap+xml; parameters=[[MimeTypeParameter@0x6dd65fe3: Attribute=charset; Value=UTF-8; ValueRequiresQuoting=false]]]
2025-07-02 12:16:07.589 [http-nio-8081-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Received plain message
2025-07-02 12:16:07.622 [http-nio-8081-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Successfully parsed payload as XML
2025-07-02 12:16:07.625 [http-nio-8081-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Determined SOAP version SOAP_12 from XML root element namespace URI 'http://www.w3.org/2003/05/soap-envelope'
2025-07-02 12:16:07.628 [http-nio-8081-exec-2] DEBUG c.h.p.s.s.SOAPHeaderElementProcessorRegistry - Successfully registered SOAP header element processor for {http://docs.oasis-open.org/ebxml-msg/ebms/v3.0/ns/core/200704/}Messaging
2025-07-02 12:16:07.630 [http-nio-8081-exec-2] DEBUG c.h.p.s.s.SOAPHeaderElementProcessorRegistry - Successfully registered SOAP header element processor for {http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd}Security
2025-07-02 12:16:07.631 [http-nio-8081-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Received the following SOAP 1.2 document:
2025-07-02 12:16:07.653 [http-nio-8081-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - <?xml version="1.0" encoding="UTF-8"?><soap:Envelope xmlns:eb="http://docs.oasis-open.org/ebxml-msg/ebms/v3.0/ns/core/200704/" xmlns:soap="http://www.w3.org/2003/05/soap-envelope">
  <soap:Header>
    <eb:Messaging soap:mustUnderstand="true">
      <eb:SignalMessage>
        <eb:MessageInfo>
          <eb:Timestamp>2025-07-02T06:46:07.561090400Z</eb:Timestamp>
          <eb:MessageId>04e3df4f-ba51-498c-a6df-33a34a728209</eb:MessageId>
          <eb:RefToMessageId>5360244b-470c-4645-9797-542648b9db45@phase4</eb:RefToMessageId>
        </eb:MessageInfo>
        <eb:Receipt>
          <ebbp:NonRepudiationInformation xmlns:ebbp="http://docs.oasis-open.org/ebxml-bp/ebbp-signals-2.0">
            <ebbp:MessagePartNRInformation>
              <ds:Reference xmlns:ds="http://www.w3.org/2000/09/xmldsig#" URI="#_1">
                <ds:DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256"/>
                <ds:DigestValue>HFmQZrMy5W82JRGsz5p9HSwkKRgBee0YrlURAurCIe8=</ds:DigestValue>
              </ds:Reference>
            </ebbp:MessagePartNRInformation>
          </ebbp:NonRepudiationInformation>
        </eb:Receipt>
      </eb:SignalMessage>
    </eb:Messaging>
  </soap:Header>
  <soap:Body/>
</soap:Envelope>
2025-07-02 12:16:07.654 [http-nio-8081-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Without any incoming attachments
2025-07-02 12:16:07.662 [http-nio-8081-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Processing SOAP header element {http://docs.oasis-open.org/ebxml-msg/ebms/v3.0/ns/core/200704/}Messaging with processor com.helger.phase4.servlet.soap.SOAPHeaderElementProcessorExtractEbms3Messaging@1252c265
2025-07-02 12:16:07.685 [http-nio-8081-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Message contains no SOAP header element with QName {http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd}Security
2025-07-02 12:16:07.686 [http-nio-8081-exec-2] DEBUG c.h.p.servlet.AS4IncomingHandler - Determined AS4 profile ID 'peppol' for current message
2025-07-02 12:22:24.875 [SpringApplicationShutdownHook] INFO  c.helger.phase4.wss.WSSConfigManager - Cleaning up WSSConfig. Security Providers will also be removed.
