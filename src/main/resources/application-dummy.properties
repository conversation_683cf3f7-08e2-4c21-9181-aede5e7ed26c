# MoroHub AS4 Service - Development/Testing Configuration
# ========================================================
# For development and testing with direct endpoint (bypasses SMP lookup)

# AS4 Mode
as4.mode=dummy
as4.security.enabled=true
as4.validation.enabled=false
as4.certificate.validation.enabled=false

# Direct Endpoint (bypasses SMP lookup for testing)
as4.endpoint.url=http://localhost:8081/reverse-flow/receive-as4-message

# SMP Configuration for Dummy Mode
smp.discovery.mode=bypass
peppol.esml.dns.current=acc.edelivery.tech.ec.europa.eu.

# File Configuration Paths (inherits from main application.properties)
# app.config.country-config.path=country-config.json
# app.config.schematron.base-path=schematron/
# app.config.keystore.base-path=keystore/

# Use real certificate provided by receiver (but validation is bypassed)
as4.keystore.path=cert.p12
as4.keystore.password=TFd20lJQ4f8j
as4.keystore.key.alias=cert
as4.keystore.key.password=TFd20lJQ4f8j

as4.truststore.path=cert.p12
as4.truststore.password=TFd20lJQ4f8j


# Participant IDs for testing
peppol.sender.participant.id=9908:987654321
peppol.receiver.participant.id=9908:123456789

# Logging for Development/Testing
logging.level.com.morohub.apsp=DEBUG
logging.level.com.helger.phase4=INFO
logging.level.root=WARN

# Phase4 Settings for Testing
phase4.debug.http=true
phase4.debug.soap=true
phase4.debug.incoming=true
phase4.debug.outgoing=true
phase4.send.retry.count=1
phase4.send.retry.interval.ms=1000

# Enable detailed AS4 message logging
logging.level.com.helger.phase4.servlet=DEBUG
logging.level.com.helger.phase4.messaging=DEBUG
logging.level.com.helger.phase4.client=DEBUG

# Connection Settings
connection.timeout.ms=10000
connection.socket.timeout.ms=30000

# Validation (relaxed for dummy mode)
validation.strict.mode=false
validation.schema.validation=false
validation.business.rules.validation=false

# File Processing
file.processing.temp.dir=target/temp
file.processing.max.size.mb=10
