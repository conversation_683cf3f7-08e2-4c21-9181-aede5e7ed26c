package com.morohub.apsp.common.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.time.LocalDateTime;
import java.util.List;
import java.util.ArrayList;

/**
 * Response DTO for handling multiple errors in a single API response
 * Requirement 3: For multiple errors in the same API request, there should be a provision to provide all errors in the single response itself
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class MultipleErrorResponse {

    private String requestId;
    private String messageId;
    private LocalDateTime timestamp;
    private String path;
    private String method;
    private int totalErrors;
    private boolean hasRetryableErrors;
    private Long minRetryAfterMs;
    private List<ErrorResponse> errors;
    private String summary;

    // Default constructor
    public MultipleErrorResponse() {
        this.timestamp = LocalDateTime.now();
        this.errors = new ArrayList<>();
        this.totalErrors = 0;
        this.hasRetryableErrors = false;
    }

    // Constructor with basic info
    public MultipleErrorResponse(String requestId, String messageId) {
        this();
        this.requestId = requestId;
        this.messageId = messageId;
    }

    // Constructor with errors list
    public MultipleErrorResponse(String requestId, String messageId, List<ErrorResponse> errors) {
        this();
        this.requestId = requestId;
        this.messageId = messageId;
        if (errors != null) {
            this.errors = new ArrayList<>(errors);
            this.totalErrors = errors.size();
            calculateRetryInfo();
        }
    }

    // Add single error
    public void addError(ErrorResponse error) {
        if (error != null) {
            this.errors.add(error);
            this.totalErrors = this.errors.size();
            calculateRetryInfo();
        }
    }

    // Add multiple errors
    public void addErrors(List<ErrorResponse> errors) {
        if (errors != null) {
            this.errors.addAll(errors);
            this.totalErrors = this.errors.size();
            calculateRetryInfo();
        }
    }

    // Calculate retry information based on all errors
    private void calculateRetryInfo() {
        this.hasRetryableErrors = false;
        this.minRetryAfterMs = null;

        for (ErrorResponse error : errors) {
            if (error.isRetryable()) {
                this.hasRetryableErrors = true;
                if (error.getRetryAfterMs() != null) {
                    if (this.minRetryAfterMs == null || error.getRetryAfterMs() < this.minRetryAfterMs) {
                        this.minRetryAfterMs = error.getRetryAfterMs();
                    }
                }
            }
        }
    }

    // Generate summary of errors
    public void generateSummary() {
        if (errors.isEmpty()) {
            this.summary = "No errors";
            return;
        }

        StringBuilder sb = new StringBuilder();
        sb.append(String.format("Total errors: %d", totalErrors));

        // Count by error type
        long validationErrors = errors.stream()
                .filter(e -> e.getErrorType() != null && 
                           (e.getErrorType().contains("VALIDATION") || e.getErrorType().contains("SCHEMA")))
                .count();
        
        long connectivityErrors = errors.stream()
                .filter(e -> e.getErrorType() != null && e.getErrorType().contains("CONNECTIVITY"))
                .count();
        
        long authErrors = errors.stream()
                .filter(e -> e.getErrorType() != null && 
                           (e.getErrorType().contains("AUTHENTICATION") || e.getErrorType().contains("AUTHORIZATION")))
                .count();

        if (validationErrors > 0) {
            sb.append(String.format(", Validation: %d", validationErrors));
        }
        if (connectivityErrors > 0) {
            sb.append(String.format(", Connectivity: %d", connectivityErrors));
        }
        if (authErrors > 0) {
            sb.append(String.format(", Authentication: %d", authErrors));
        }

        if (hasRetryableErrors) {
            long retryableCount = errors.stream().filter(ErrorResponse::isRetryable).count();
            sb.append(String.format(", Retryable: %d", retryableCount));
        }

        this.summary = sb.toString();
    }

    // Check if all errors are validation errors
    public boolean isAllValidationErrors() {
        return errors.stream().allMatch(e -> 
            e.getErrorType() != null && 
            (e.getErrorType().contains("VALIDATION") || e.getErrorType().contains("SCHEMA"))
        );
    }

    // Check if any error is critical
    public boolean hasCriticalErrors() {
        return errors.stream().anyMatch(e -> 
            "CRITICAL".equals(e.getSeverity())
        );
    }

    // Get errors by type
    public List<ErrorResponse> getErrorsByType(String errorType) {
        return errors.stream()
                .filter(e -> errorType.equals(e.getErrorType()))
                .toList();
    }

    // Get retryable errors only
    public List<ErrorResponse> getRetryableErrors() {
        return errors.stream()
                .filter(ErrorResponse::isRetryable)
                .toList();
    }

    // Builder pattern
    public static MultipleErrorResponseBuilder builder() {
        return new MultipleErrorResponseBuilder();
    }

    public static class MultipleErrorResponseBuilder {
        private MultipleErrorResponse response = new MultipleErrorResponse();

        public MultipleErrorResponseBuilder requestId(String requestId) {
            response.requestId = requestId;
            return this;
        }

        public MultipleErrorResponseBuilder messageId(String messageId) {
            response.messageId = messageId;
            return this;
        }

        public MultipleErrorResponseBuilder path(String path) {
            response.path = path;
            return this;
        }

        public MultipleErrorResponseBuilder method(String method) {
            response.method = method;
            return this;
        }

        public MultipleErrorResponseBuilder addError(ErrorResponse error) {
            response.addError(error);
            return this;
        }

        public MultipleErrorResponseBuilder addErrors(List<ErrorResponse> errors) {
            response.addErrors(errors);
            return this;
        }

        public MultipleErrorResponse build() {
            response.generateSummary();
            return response;
        }
    }

    // Getters and Setters
    public String getRequestId() { return requestId; }
    public void setRequestId(String requestId) { this.requestId = requestId; }

    public String getMessageId() { return messageId; }
    public void setMessageId(String messageId) { this.messageId = messageId; }

    public LocalDateTime getTimestamp() { return timestamp; }
    public void setTimestamp(LocalDateTime timestamp) { this.timestamp = timestamp; }

    public String getPath() { return path; }
    public void setPath(String path) { this.path = path; }

    public String getMethod() { return method; }
    public void setMethod(String method) { this.method = method; }

    public int getTotalErrors() { return totalErrors; }
    public void setTotalErrors(int totalErrors) { this.totalErrors = totalErrors; }

    public boolean isHasRetryableErrors() { return hasRetryableErrors; }
    public void setHasRetryableErrors(boolean hasRetryableErrors) { this.hasRetryableErrors = hasRetryableErrors; }

    public Long getMinRetryAfterMs() { return minRetryAfterMs; }
    public void setMinRetryAfterMs(Long minRetryAfterMs) { this.minRetryAfterMs = minRetryAfterMs; }

    public List<ErrorResponse> getErrors() { return errors; }
    public void setErrors(List<ErrorResponse> errors) { this.errors = errors; }

    public String getSummary() { return summary; }
    public void setSummary(String summary) { this.summary = summary; }
}
