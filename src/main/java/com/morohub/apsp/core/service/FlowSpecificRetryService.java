package com.morohub.apsp.core.service;

import com.morohub.apsp.core.entity.MessageProcessingQueue;
import com.morohub.apsp.core.repository.MessageProcessingQueueRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service for handling flow-specific retry logic
 * Different flows have different retry mechanisms and processing logic
 */
@Service
@Transactional
public class FlowSpecificRetryService {

    private static final Logger logger = LoggerFactory.getLogger(FlowSpecificRetryService.class);

    @Autowired
    private MessageProcessingQueueRepository messageQueueRepository;

    @Autowired
    private AS4ConversionService as4ConversionService;

    @Autowired
    private SchematronValidationService schematronValidationService;

    @Autowired
    private InvoiceService invoiceService;

    @Autowired
    private MLSMessageSenderService mlsMessageSenderService;

    /**
     * Process retry for a specific message based on its flow type
     */
    public boolean processRetryForMessage(MessageProcessingQueue message) {
        try {
            logger.info("🔄 Processing retry for {} flow, message: {}", 
                       message.getFlowType(), message.getMessageId());

            switch (message.getFlowType()) {
                case FORWARD_FLOW:
                    return retryForwardFlow(message);
                
                case REVERSE_FLOW:
                    return retryReverseFlow(message);
                
                case XML_VALIDATION:
                    return retryXmlValidation(message);
                
                case PEPPOL_SBD_INVOICE:
                    return retryPeppolSbdInvoice(message);
                
                case MLS_MESSAGE:
                    return retryMlsMessage(message);
                
                default:
                    logger.warn("⚠️ Unknown flow type for retry: {}", message.getFlowType());
                    return false;
            }

        } catch (Exception e) {
            logger.error("❌ Error processing retry for message {}: {}", 
                        message.getMessageId(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * Retry forward flow (AS4 conversion and sending)
     */
    private boolean retryForwardFlow(MessageProcessingQueue message) {
        try {
            logger.info("🔄 Retrying forward flow for message: {}", message.getMessageId());
            
            // Retry AS4 conversion and sending
            AS4ConversionService.AS4ConversionResult result = as4ConversionService.convertAndSend(message.getUblXml());
            
            if (result.isSuccess()) {
                message.setStatus(MessageProcessingQueue.ProcessingStatus.COMPLETED);
                message.setProcessingNotes("Retry successful - AS4 message sent");
                messageQueueRepository.save(message);
                logger.info("✅ Forward flow retry successful for message: {}", message.getMessageId());
                return true;
            } else {
                message.setProcessingNotes("Retry failed: " + result.getErrorMessage());
                messageQueueRepository.save(message);
                logger.warn("❌ Forward flow retry failed for message: {}: {}", 
                           message.getMessageId(), result.getErrorMessage());
                return false;
            }

        } catch (Exception e) {
            logger.error("❌ Exception during forward flow retry for message {}: {}", 
                        message.getMessageId(), e.getMessage(), e);
            message.setProcessingNotes("Retry exception: " + e.getMessage());
            messageQueueRepository.save(message);
            return false;
        }
    }

    /**
     * Retry reverse flow (incoming message processing)
     */
    private boolean retryReverseFlow(MessageProcessingQueue message) {
        try {
            logger.info("🔄 Retrying reverse flow for message: {}", message.getMessageId());
            
            // Retry incoming message processing
            String result = invoiceService.processIncomingMessageWithCountryConfig(
                message.getUblXml(), message.getCountryCode(), message.getDocumentType());
            
            if (result != null) {
                message.setStatus(MessageProcessingQueue.ProcessingStatus.COMPLETED);
                message.setProcessingNotes("Retry successful - incoming message processed");
                messageQueueRepository.save(message);
                logger.info("✅ Reverse flow retry successful for message: {}", message.getMessageId());
                return true;
            } else {
                message.setProcessingNotes("Retry failed: processing returned null");
                messageQueueRepository.save(message);
                return false;
            }

        } catch (Exception e) {
            logger.error("❌ Exception during reverse flow retry for message {}: {}", 
                        message.getMessageId(), e.getMessage(), e);
            message.setProcessingNotes("Retry exception: " + e.getMessage());
            messageQueueRepository.save(message);
            return false;
        }
    }

    /**
     * Retry XML validation
     */
    private boolean retryXmlValidation(MessageProcessingQueue message) {
        try {
            logger.info("🔄 Retrying XML validation for message: {}", message.getMessageId());
            
            // Retry XML validation
            java.util.List<String> validationErrors = schematronValidationService.validateXml(
                message.getUblXml(), "PEPPOL-EN16931-UBL.sch");
            
            if (validationErrors.isEmpty()) {
                message.setStatus(MessageProcessingQueue.ProcessingStatus.COMPLETED);
                message.setProcessingNotes("Retry successful - XML validation passed");
                messageQueueRepository.save(message);
                logger.info("✅ XML validation retry successful for message: {}", message.getMessageId());
                return true;
            } else {
                String errors = String.join("; ", validationErrors);
                message.setValidationErrors(errors);
                message.setProcessingNotes("Retry failed: validation errors found");
                messageQueueRepository.save(message);
                logger.warn("❌ XML validation retry failed for message: {}: {}", 
                           message.getMessageId(), errors);
                return false;
            }

        } catch (Exception e) {
            logger.error("❌ Exception during XML validation retry for message {}: {}", 
                        message.getMessageId(), e.getMessage(), e);
            message.setProcessingNotes("Retry exception: " + e.getMessage());
            messageQueueRepository.save(message);
            return false;
        }
    }

    /**
     * Retry Peppol SBD Invoice generation
     */
    private boolean retryPeppolSbdInvoice(MessageProcessingQueue message) {
        try {
            logger.info("🔄 Retrying Peppol SBD Invoice for message: {}", message.getMessageId());
            
            // For Peppol SBD Invoice, we would need to reconstruct the request object
            // This is a simplified retry - in practice, you might need to store more context
            String result = invoiceService.generateInvoiceWithConfig(
                message.getUblXml(), message.getCountryCode(), message.getDocumentType());
            
            if (result != null && !result.isEmpty()) {
                message.setStatus(MessageProcessingQueue.ProcessingStatus.COMPLETED);
                message.setProcessingNotes("Retry successful - Peppol SBD Invoice generated");
                messageQueueRepository.save(message);
                logger.info("✅ Peppol SBD Invoice retry successful for message: {}", message.getMessageId());
                return true;
            } else {
                message.setProcessingNotes("Retry failed: invoice generation returned empty result");
                messageQueueRepository.save(message);
                return false;
            }

        } catch (Exception e) {
            logger.error("❌ Exception during Peppol SBD Invoice retry for message {}: {}", 
                        message.getMessageId(), e.getMessage(), e);
            message.setProcessingNotes("Retry exception: " + e.getMessage());
            messageQueueRepository.save(message);
            return false;
        }
    }

    /**
     * Retry MLS message sending
     */
    private boolean retryMlsMessage(MessageProcessingQueue message) {
        try {
            logger.info("🔄 Retrying MLS message for message: {}", message.getMessageId());
            
            // For MLS retry, we need to reconstruct the MLS sending context
            // This is the existing MLS retry logic
            MLSMessageSenderService.MLSMessageResult result = mlsMessageSenderService.sendMLSMessageWithSchematronErrors(message);
            
            if (result.isSuccess()) {
                message.setStatus(MessageProcessingQueue.ProcessingStatus.COMPLETED);
                message.setMlsMessageId(result.getMlsMessageId());
                message.setProcessingNotes("Retry successful - MLS message sent");
                messageQueueRepository.save(message);
                logger.info("✅ MLS message retry successful for message: {}", message.getMessageId());
                return true;
            } else {
                message.setProcessingNotes("Retry failed: " + result.getErrorMessage());
                messageQueueRepository.save(message);
                logger.warn("❌ MLS message retry failed for message: {}: {}", 
                           message.getMessageId(), result.getErrorMessage());
                return false;
            }

        } catch (Exception e) {
            logger.error("❌ Exception during MLS message retry for message {}: {}", 
                        message.getMessageId(), e.getMessage(), e);
            message.setProcessingNotes("Retry exception: " + e.getMessage());
            messageQueueRepository.save(message);
            return false;
        }
    }

    /**
     * Check if a message can be retried based on its flow type and current state
     */
    public boolean canRetry(MessageProcessingQueue message) {
        if (message.hasExceededMaxRetries()) {
            logger.warn("⚠️ Message {} has exceeded max retries", message.getMessageId());
            return false;
        }

        if (message.getStatus() == MessageProcessingQueue.ProcessingStatus.COMPLETED) {
            logger.debug("Message {} is already completed", message.getMessageId());
            return false;
        }

        return true;
    }
}
