package com.morohub.apsp.core.service;

import com.morohub.apsp.common.dto.ErrorResponse;
import com.morohub.apsp.common.dto.MultipleErrorResponse;
import com.morohub.apsp.common.exception.AS4Exception;
import com.morohub.apsp.core.entity.ErrorLog;
import com.morohub.apsp.core.entity.ErrorMasterData;
import com.morohub.apsp.core.repository.ErrorLogRepository;
import com.morohub.apsp.core.repository.ErrorMasterDataRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import jakarta.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

/**
 * Central error management service for the AS4 application
 * Handles error logging, retrieval, and response generation
 */
@Service
@Transactional
public class ErrorManagementService {

    private static final Logger logger = LoggerFactory.getLogger(ErrorManagementService.class);

    @Autowired
    private ErrorMasterDataRepository errorMasterDataRepository;

    @Autowired
    private ErrorLogRepository errorLogRepository;

    /**
     * Log an error occurrence
     */
    public ErrorLog logError(String errorCode, String messageId, String requestId, 
                           String errorMessage, String stackTrace, HttpServletRequest request) {
        try {
            Optional<ErrorMasterData> errorMasterOpt = errorMasterDataRepository.findByErrorCode(errorCode);
            
            if (errorMasterOpt.isEmpty()) {
                logger.warn("Error code {} not found in master data, creating default entry", errorCode);
                // Create a default error master data entry
                ErrorMasterData defaultError = createDefaultErrorMasterData(errorCode, errorMessage);
                errorMasterOpt = Optional.of(errorMasterDataRepository.save(defaultError));
            }

            ErrorMasterData errorMaster = errorMasterOpt.get();
            
            ErrorLog errorLog = new ErrorLog();
            errorLog.setErrorMasterData(errorMaster);
            errorLog.setMessageId(messageId);
            errorLog.setRequestId(requestId != null ? requestId : UUID.randomUUID().toString());
            errorLog.setErrorMessage(errorMessage);
            errorLog.setStackTrace(stackTrace);
            
            if (request != null) {
                errorLog.setApiEndpoint(request.getRequestURI());
                errorLog.setHttpMethod(request.getMethod());
                errorLog.setClientIp(getClientIpAddress(request));
                errorLog.setUserAgent(request.getHeader("User-Agent"));
            }

            errorLog.setOccurredAt(LocalDateTime.now());
            errorLog.setProcessingStatus(ErrorLog.ProcessingStatus.NEW);

            // Schedule retry if error is retryable
            if (Boolean.TRUE.equals(errorMaster.getRetryRequired())) {
                errorLog.scheduleNextRetry();
            }

            ErrorLog savedLog = errorLogRepository.save(errorLog);
            logger.info("Error logged: {} for message: {}", errorCode, messageId);
            
            return savedLog;

        } catch (Exception e) {
            logger.error("Failed to log error: {}", e.getMessage(), e);
            throw new RuntimeException("Error logging failed", e);
        }
    }

    /**
     * Log an AS4Exception
     */
    public ErrorLog logError(AS4Exception exception, HttpServletRequest request) {
        String stackTrace = getStackTraceAsString(exception);
        return logError(exception.getErrorCode(), exception.getMessageId(), 
                       exception.getRequestId(), exception.getMessage(), stackTrace, request);
    }

    /**
     * Generate error response from error code
     */
    public ErrorResponse generateErrorResponse(String errorCode, String messageId, String requestId) {
        Optional<ErrorMasterData> errorMasterOpt = errorMasterDataRepository.findByErrorCode(errorCode);
        
        if (errorMasterOpt.isPresent()) {
            return ErrorResponse.fromErrorMasterData(errorMasterOpt.get(), messageId, requestId);
        } else {
            logger.warn("Error code {} not found in master data", errorCode);
            return ErrorResponse.builder()
                    .errorCode(errorCode)
                    .errorDescription("Unknown error occurred")
                    .messageId(messageId)
                    .requestId(requestId)
                    .build();
        }
    }

    /**
     * Generate error response from AS4Exception
     */
    public ErrorResponse generateErrorResponse(AS4Exception exception) {
        ErrorResponse response = generateErrorResponse(exception.getErrorCode(), 
                                                     exception.getMessageId(), 
                                                     exception.getRequestId());
        
        // Override with exception details if available
        if (exception.getMessage() != null) {
            response.setErrorDescription(exception.getMessage());
        }
        response.setErrorType(exception.getErrorType().name());
        response.setSeverity(exception.getSeverity().name());
        response.setRetryable(exception.isRetryable());
        
        return response;
    }

    /**
     * Generate multiple error response
     */
    public MultipleErrorResponse generateMultipleErrorResponse(List<AS4Exception> exceptions, 
                                                             String requestId, String messageId) {
        MultipleErrorResponse.MultipleErrorResponseBuilder builder = MultipleErrorResponse.builder()
                .requestId(requestId)
                .messageId(messageId);

        for (AS4Exception exception : exceptions) {
            ErrorResponse errorResponse = generateErrorResponse(exception);
            builder.addError(errorResponse);
        }

        return builder.build();
    }

    /**
     * Get error master data by error code
     */
    public Optional<ErrorMasterData> getErrorMasterData(String errorCode) {
        return errorMasterDataRepository.findByErrorCode(errorCode);
    }

    /**
     * Get error logs for a message
     */
    public List<ErrorLog> getErrorLogsForMessage(String messageId) {
        return errorLogRepository.findByMessageId(messageId);
    }

    /**
     * Get unresolved errors
     */
    public List<ErrorLog> getUnresolvedErrors() {
        return errorLogRepository.findByResolvedFalse();
    }

    /**
     * Mark error as resolved
     */
    public void markErrorAsResolved(Long errorLogId, String resolvedBy, String resolutionNotes) {
        Optional<ErrorLog> errorLogOpt = errorLogRepository.findById(errorLogId);
        if (errorLogOpt.isPresent()) {
            ErrorLog errorLog = errorLogOpt.get();
            errorLog.markAsResolved(resolvedBy, resolutionNotes);
            errorLogRepository.save(errorLog);
            logger.info("Error {} marked as resolved by {}", errorLogId, resolvedBy);
        }
    }

    /**
     * Get errors ready for retry
     */
    public List<ErrorLog> getErrorsReadyForRetry() {
        return errorLogRepository.findErrorsReadyForRetry(LocalDateTime.now());
    }

    /**
     * Create default error master data for unknown error codes
     */
    private ErrorMasterData createDefaultErrorMasterData(String errorCode, String errorMessage) {
        ErrorMasterData errorMaster = new ErrorMasterData();
        errorMaster.setErrorCode(errorCode);
        errorMaster.setHttpStatusCode(500);
        errorMaster.setErrorType(ErrorMasterData.ErrorType.SYSTEM_ERROR);
        errorMaster.setErrorDescription(errorMessage != null ? errorMessage : "Unknown system error");
        errorMaster.setRetryRequired(false);
        errorMaster.setSeverity(ErrorMasterData.ErrorSeverity.MEDIUM);
        errorMaster.setCategory("SYSTEM");
        errorMaster.setActive(true);
        errorMaster.setCreatedBy("SYSTEM");
        
        return errorMaster;
    }

    /**
     * Get client IP address from request
     */
    private String getClientIpAddress(HttpServletRequest request) {
        String xForwardedFor = request.getHeader("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            return xForwardedFor.split(",")[0].trim();
        }
        
        String xRealIp = request.getHeader("X-Real-IP");
        if (xRealIp != null && !xRealIp.isEmpty()) {
            return xRealIp;
        }
        
        return request.getRemoteAddr();
    }

    /**
     * Convert stack trace to string
     */
    private String getStackTraceAsString(Throwable throwable) {
        java.io.StringWriter sw = new java.io.StringWriter();
        java.io.PrintWriter pw = new java.io.PrintWriter(sw);
        throwable.printStackTrace(pw);
        return sw.toString();
    }
}
