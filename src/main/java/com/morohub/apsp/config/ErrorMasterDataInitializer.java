package com.morohub.apsp.config;

import com.morohub.apsp.core.entity.ErrorMasterData;
import com.morohub.apsp.core.repository.ErrorMasterDataRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * Initializes error master data with common error codes
 * Requirement 1: Master data for all errors should be maintained in Access Point database
 */
@Component
public class ErrorMasterDataInitializer implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(ErrorMasterDataInitializer.class);

    @Autowired
    private ErrorMasterDataRepository errorMasterDataRepository;

    @Override
    public void run(String... args) throws Exception {
        try {
            logger.info("🔧 Initializing error master data...");
            initializeErrorMasterData();
            logger.info("✅ Error master data initialization completed");
        } catch (Exception e) {
            logger.error("❌ Failed to initialize error master data: {}", e.getMessage(), e);
        }
    }

    private void initializeErrorMasterData() {
        List<ErrorMasterData> defaultErrors = Arrays.asList(
            // Authentication Errors
            createError("AUTH_001", 401, ErrorMasterData.ErrorType.AUTHENTICATION_FAILURE,
                       "Invalid authentication credentials", false, null, null,
                       ErrorMasterData.ErrorSeverity.HIGH, "AUTHENTICATION"),

            createError("AUTH_002", 401, ErrorMasterData.ErrorType.AUTHENTICATION_FAILURE,
                       "Authentication token expired", false, null, null,
                       ErrorMasterData.ErrorSeverity.HIGH, "AUTHENTICATION"),

            createError("AUTH_003", 403, ErrorMasterData.ErrorType.AUTHORIZATION_FAILURE,
                       "Insufficient permissions", false, null, null,
                       ErrorMasterData.ErrorSeverity.HIGH, "AUTHORIZATION"),

            createError("AUTH_004", 401, ErrorMasterData.ErrorType.CERTIFICATE_ERROR,
                       "Invalid or expired certificate", false, null, null,
                       ErrorMasterData.ErrorSeverity.CRITICAL, "CERTIFICATE"),

            // Validation Errors
            createError("VAL_001", 400, ErrorMasterData.ErrorType.SCHEMA_VALIDATION_FAILURE,
                       "XML schema validation failed", false, null, null,
                       ErrorMasterData.ErrorSeverity.HIGH, "VALIDATION"),

            createError("VAL_002", 400, ErrorMasterData.ErrorType.SCHEMATRON_VALIDATION_FAILURE,
                       "Schematron validation failed", false, null, null,
                       ErrorMasterData.ErrorSeverity.HIGH, "VALIDATION"),

            createError("VAL_003", 400, ErrorMasterData.ErrorType.BUSINESS_RULE_VIOLATION,
                       "Business rule validation failed", false, null, null,
                       ErrorMasterData.ErrorSeverity.MEDIUM, "VALIDATION"),

            createError("VAL_004", 400, ErrorMasterData.ErrorType.DOCUMENT_CAPABILITY_MISMATCH,
                       "Document type not supported", false, null, null,
                       ErrorMasterData.ErrorSeverity.HIGH, "VALIDATION"),

            // Connectivity Errors (Retryable)
            createError("CONN_001", 503, ErrorMasterData.ErrorType.TECHNICAL_CONNECTIVITY_ERROR,
                       "Service temporarily unavailable", true, 60000L, 3,
                       ErrorMasterData.ErrorSeverity.HIGH, "CONNECTIVITY"),

            createError("CONN_002", 408, ErrorMasterData.ErrorType.TIMEOUT_ERROR,
                       "Request timeout", true, 30000L, 3,
                       ErrorMasterData.ErrorSeverity.MEDIUM, "CONNECTIVITY"),

            createError("CONN_003", 502, ErrorMasterData.ErrorType.TECHNICAL_CONNECTIVITY_ERROR,
                       "Bad gateway", true, 60000L, 3,
                       ErrorMasterData.ErrorSeverity.HIGH, "CONNECTIVITY"),

            createError("CONN_004", 504, ErrorMasterData.ErrorType.TECHNICAL_CONNECTIVITY_ERROR,
                       "Gateway timeout", true, 60000L, 3,
                       ErrorMasterData.ErrorSeverity.HIGH, "CONNECTIVITY"),

            createError("CONN_005", 500, ErrorMasterData.ErrorType.TECHNICAL_CONNECTIVITY_ERROR,
                       "Connection refused", true, 120000L, 5,
                       ErrorMasterData.ErrorSeverity.HIGH, "CONNECTIVITY"),

            // System Errors
            createError("SYS_001", 500, ErrorMasterData.ErrorType.SYSTEM_ERROR,
                       "Internal server error", true, 60000L, 2,
                       ErrorMasterData.ErrorSeverity.CRITICAL, "SYSTEM"),

            createError("SYS_002", 500, ErrorMasterData.ErrorType.CONFIGURATION_ERROR,
                       "System configuration error", false, null, null,
                       ErrorMasterData.ErrorSeverity.CRITICAL, "SYSTEM"),

            createError("SYS_003", 507, ErrorMasterData.ErrorType.SYSTEM_ERROR,
                       "Insufficient storage space", true, 300000L, 2,
                       ErrorMasterData.ErrorSeverity.HIGH, "SYSTEM"),

            // AS4 Specific Errors
            createError("AS4_001", 500, ErrorMasterData.ErrorType.TECHNICAL_CONNECTIVITY_ERROR,
                       "AS4 message processing failed", true, 60000L, 3,
                       ErrorMasterData.ErrorSeverity.HIGH, "AS4"),

            createError("AS4_002", 400, ErrorMasterData.ErrorType.SCHEMA_VALIDATION_FAILURE,
                       "Invalid AS4 message format", false, null, null,
                       ErrorMasterData.ErrorSeverity.HIGH, "AS4"),

            createError("AS4_003", 500, ErrorMasterData.ErrorType.ENCRYPTION_ERROR,
                       "AS4 message encryption failed", false, null, null,
                       ErrorMasterData.ErrorSeverity.CRITICAL, "AS4"),

            createError("AS4_004", 500, ErrorMasterData.ErrorType.SIGNATURE_ERROR,
                       "AS4 message signature verification failed", false, null, null,
                       ErrorMasterData.ErrorSeverity.CRITICAL, "AS4"),

            // Peppol Specific Errors
            createError("PEPPOL_001", 404, ErrorMasterData.ErrorType.TECHNICAL_CONNECTIVITY_ERROR,
                       "Participant not found in SMP", true, 300000L, 2,
                       ErrorMasterData.ErrorSeverity.HIGH, "PEPPOL"),

            createError("PEPPOL_002", 400, ErrorMasterData.ErrorType.DOCUMENT_CAPABILITY_MISMATCH,
                       "Document type not supported by participant", false, null, null,
                       ErrorMasterData.ErrorSeverity.HIGH, "PEPPOL"),

            createError("PEPPOL_003", 500, ErrorMasterData.ErrorType.TECHNICAL_CONNECTIVITY_ERROR,
                       "SMP lookup failed", true, 120000L, 3,
                       ErrorMasterData.ErrorSeverity.HIGH, "PEPPOL"),

            // MLS Specific Errors
            createError("MLS_001", 500, ErrorMasterData.ErrorType.TECHNICAL_CONNECTIVITY_ERROR,
                       "MLS message sending failed", true, 60000L, 3,
                       ErrorMasterData.ErrorSeverity.HIGH, "MLS"),

            createError("MLS_002", 400, ErrorMasterData.ErrorType.SCHEMA_VALIDATION_FAILURE,
                       "Invalid MLS message format", false, null, null,
                       ErrorMasterData.ErrorSeverity.HIGH, "MLS"),

            // Rate Limiting
            createError("RATE_001", 429, ErrorMasterData.ErrorType.TECHNICAL_CONNECTIVITY_ERROR,
                       "Rate limit exceeded", true, 60000L, 5,
                       ErrorMasterData.ErrorSeverity.MEDIUM, "RATE_LIMITING")
        );

        for (ErrorMasterData error : defaultErrors) {
            if (!errorMasterDataRepository.existsByErrorCode(error.getErrorCode())) {
                errorMasterDataRepository.save(error);
                logger.debug("Created error master data: {}", error.getErrorCode());
            } else {
                logger.debug("Error master data already exists: {}", error.getErrorCode());
            }
        }

        logger.info("Initialized {} error master data entries", defaultErrors.size());
    }

    private ErrorMasterData createError(String errorCode, Integer httpStatusCode, 
                                      ErrorMasterData.ErrorType errorType, String description,
                                      Boolean retryRequired, Long retryIntervalMs, Integer maxRetryAttempts,
                                      ErrorMasterData.ErrorSeverity severity, String category) {
        ErrorMasterData error = new ErrorMasterData();
        error.setErrorCode(errorCode);
        error.setHttpStatusCode(httpStatusCode);
        error.setErrorType(errorType);
        error.setErrorDescription(description);
        error.setRetryRequired(retryRequired);
        error.setRetryIntervalMs(retryIntervalMs);
        error.setMaxRetryAttempts(maxRetryAttempts);
        error.setSeverity(severity);
        error.setCategory(category);
        error.setActive(true);
        error.setCreatedBy("SYSTEM");
        error.setUpdatedBy("SYSTEM");
        return error;
    }
}
