package com.morohub.apsp.core.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.time.LocalDateTime;

/**
 * Entity representing messages in the processing queue
 */
@Entity
@Table(name = "message_processing_queue")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class MessageProcessingQueue {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "message_id", unique = true, nullable = false)
    private String messageId;

    @Column(name = "ubl_xml", columnDefinition = "TEXT", nullable = false)
    private String ublXml;

    @Column(name = "country_code", nullable = false)
    private String countryCode;

    @Column(name = "document_type", nullable = false)
    private String documentType;

    @Enumerated(EnumType.STRING)
    @Column(name = "flow_type", nullable = false)
    private FlowType flowType;

    @Enumerated(EnumType.STRING)
    @Column(name = "status", nullable = false)
    private ProcessingStatus status = ProcessingStatus.READY_TO_SEND;

    @Column(name = "retry_count")
    private Integer retryCount = 0;

    @Column(name = "max_retries")
    private Integer maxRetries = 3;

    @Column(name = "created_date")
    private LocalDateTime createdDate;

    @Column(name = "last_updated")
    private LocalDateTime lastUpdated;

    @Column(name = "last_processed")
    private LocalDateTime lastProcessed;

    @Column(name = "error_message", columnDefinition = "TEXT")
    private String errorMessage;

    @Column(name = "receiver_participant_id")
    private String receiverParticipantId;

    @Column(name = "sender_participant_id")
    private String senderParticipantId;

    @Column(name = "endpoint_url")
    private String endpointUrl;

    @Column(name = "mls_message_id")
    private String mlsMessageId;

    @Column(name = "original_sbdh_business_scope", columnDefinition = "TEXT")
    private String originalSbdhBusinessScope;

    @Column(name = "original_document_instance_id")
    private String originalDocumentInstanceId;

    @Column(name = "request_id")
    private String requestId;


    // Removed validation_errors and processing_notes - handled by error_log table

    /**
     * Flow type enumeration for different processing flows
     */
    public enum FlowType {
        FORWARD_FLOW,           // Normal AS4 message sending
        REVERSE_FLOW,           // Incoming AS4 message processing
        XML_VALIDATION,         // XML validation only
        PEPPOL_SBD_INVOICE,     // Peppol SBD Invoice generation
        MLS_MESSAGE             // MLS message sending
    }

    public enum ProcessingStatus {
        READY_TO_SEND,      // For MLS messages ready to be sent
        PROCESSING,         // Currently being processed
        COMPLETED,          // Successfully completed
        RETRY,              // Needs retry (for reprocessing)
        ERROR               // Failed with error
    }

    @PrePersist
    public void prePersist() {
        this.createdDate = LocalDateTime.now();
        this.lastUpdated = LocalDateTime.now();
    }

    @PreUpdate
    public void preUpdate() {
        this.lastUpdated = LocalDateTime.now();
    }

    public void incrementRetryCount() {
        this.retryCount = (this.retryCount == null) ? 1 : this.retryCount + 1;
    }

    public boolean hasExceededMaxRetries() {
        return this.retryCount != null && this.retryCount >= this.maxRetries;
    }
}
