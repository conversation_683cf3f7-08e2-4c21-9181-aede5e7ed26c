package com.morohub.apsp.config;

import com.helger.smpclient.peppol.ISMPServiceMetadataProvider;
import com.helger.smpclient.peppol.SMPClientReadOnly;
import com.helger.phase4.peppol.Phase4PeppolSender;
import com.helger.phase4.dynamicdiscovery.AS4EndpointDetailProviderPeppol;
import com.helger.phase4.dynamicdiscovery.IAS4EndpointDetailProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Bean;

import java.net.URI;

/**
 * Configuration for SMP (Service Metadata Publisher) and ESML (Service Metadata Locator)
 * Handles different modes: auto (Phase4 discovery), manual (custom lookup), bypass (direct endpoint)
 */
@Configuration
public class SMPConfiguration {

    private static final Logger LOGGER = LoggerFactory.getLogger(SMPConfiguration.class);

    @Value("${as4.mode:dummy}")
    private String as4Mode;

    @Value("${smp.discovery.mode:auto}")
    private String smpDiscoveryMode;

    @Value("${peppol.esml.dns.production:edelivery.tech.ec.europa.eu.}")
    private String esmlDnsProduction;

    @Value("${peppol.esml.dns.test:acc.edelivery.tech.ec.europa.eu.}")
    private String esmlDnsTest;

    @Value("${peppol.esml.dns.local:smj.peppolcentral.org.}")
    private String esmlDnsLocal;

    @Value("${peppol.esml.dns.current:}")
    private String esmlDnsCurrent;

    @Value("${peppol.smp.url:}")
    private String customSmpUrl;

    @Value("${smp.lookup.bypass:false}")
    private boolean smpLookupBypass;

    @Value("${smp.lookup.bypass.url:}")
    private String smpBypassUrl;

    /**
     * Get the appropriate ESML DNS value based on current mode
     */
    public String getESMLDnsValue() {
        // If current DNS is explicitly set, use it
        if (esmlDnsCurrent != null && !esmlDnsCurrent.trim().isEmpty()) {
            LOGGER.info("🌐 Using explicitly configured ESML DNS: {}", esmlDnsCurrent);
            return esmlDnsCurrent;
        }

        // Determine based on AS4 mode
        String dnsValue;
        switch (as4Mode.toLowerCase()) {
            case "production":
                dnsValue = esmlDnsProduction;
                LOGGER.info("🏭 Production mode - using ESML DNS: {}", dnsValue);
                break;
            case "test":
                dnsValue = esmlDnsTest;
                LOGGER.info("🧪 Test mode - using ESML DNS: {}", dnsValue);
                break;
            case "dummy":
            case "local":
            default:
                dnsValue = esmlDnsLocal;
                LOGGER.info("🔧 Dummy/Local mode - using ESML DNS: {}", dnsValue);
                break;
        }

        return dnsValue;
    }

    /**
     * Get SMP discovery mode
     */
    public SMPDiscoveryMode getDiscoveryMode() {
        try {
            return SMPDiscoveryMode.valueOf(smpDiscoveryMode.toUpperCase());
        } catch (IllegalArgumentException e) {
            LOGGER.warn("⚠️ Invalid SMP discovery mode '{}', defaulting to AUTO", smpDiscoveryMode);
            return SMPDiscoveryMode.AUTO;
        }
    }

    /**
     * Check if SMP lookup should be bypassed
     */
    public boolean shouldBypassSMPLookup() {
        return smpLookupBypass || getDiscoveryMode() == SMPDiscoveryMode.BYPASS;
    }

    /**
     * Get bypass URL for testing
     */
    public String getBypassUrl() {
        return smpBypassUrl;
    }

    /**
     * Create SMP client for manual lookup (when needed)
     */
    public ISMPServiceMetadataProvider createSMPClient() {
        try {
            if (customSmpUrl != null && !customSmpUrl.trim().isEmpty()) {
                LOGGER.info("🌐 Creating SMP client with custom URL: {}", customSmpUrl);
                return new SMPClientReadOnly(new URI(customSmpUrl));
            } else {
                String esmlDns = getESMLDnsValue();
                // For Phase4, we don't need to create the client manually
                // Phase4 will handle SMP discovery automatically
                LOGGER.info("🌐 SMP client will use ESML DNS: {}", esmlDns);
                return null; // Let Phase4 handle it
            }
        } catch (Exception e) {
            LOGGER.error("❌ Error creating SMP client: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to create SMP client", e);
        }
    }

    /**
     * Configure Phase4 SMP settings
     */
    public void configurePhase4SMP(Phase4PeppolSender.SBDHBuilder builder) {
        SMPDiscoveryMode mode = getDiscoveryMode();
        
        LOGGER.info("🔧 Configuring Phase4 SMP with mode: {}", mode);
        
        switch (mode) {
            case AUTO:
                // Let Phase4 handle SMP discovery automatically
                // Phase4 will use the default ESML configuration
                LOGGER.info("🌐 Using Phase4 automatic SMP discovery");
                break;
                
            case MANUAL:
                // Use custom SMP URL if provided
                if (customSmpUrl != null && !customSmpUrl.trim().isEmpty()) {
                    LOGGER.info("🌐 Using custom SMP URL for manual lookup: {}", customSmpUrl);
                    // Phase4 will use the configured SMP client
                }
                break;
                
            case BYPASS:
                // Direct endpoint - no SMP lookup needed
                LOGGER.info("🌐 Bypassing SMP lookup - using direct endpoint");
                break;
        }
    }

    /**
     * Get configuration info for diagnostics
     */
    public String getConfigurationInfo() {
        return String.format(
            "SMP Configuration: mode=%s, discoveryMode=%s, esmlDns=%s, customSmp=%s, bypass=%s",
            as4Mode, smpDiscoveryMode, getESMLDnsValue(), 
            customSmpUrl != null ? customSmpUrl : "none",
            shouldBypassSMPLookup()
        );
    }

    /**
     * SMP Discovery Mode enumeration
     */
    public enum SMPDiscoveryMode {
        AUTO,    // Use Phase4 automatic SMP discovery with ESML
        MANUAL,  // Use manual SMP lookup service
        BYPASS   // Use direct endpoint (for testing)
    }
}
