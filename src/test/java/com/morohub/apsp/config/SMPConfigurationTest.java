package com.morohub.apsp.config;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;


import static org.junit.jupiter.api.Assertions.*;

/**
 * Test for SMP Configuration to verify ESML DNS configuration works correctly
 */
@SpringBootTest
class SMPConfigurationTest {

    @Autowired
    private SMPConfiguration smpConfiguration;

    @Test
    void testDummyModeConfiguration() {
        // Test dummy mode uses test ESML DNS (default mode is dummy)
        String esmlDns = smpConfiguration.getESMLDnsValue();
        assertNotNull(esmlDns);
        assertTrue(smpConfiguration.isDummyMode());
        assertFalse(smpConfiguration.isProductionMode());
    }

    @Test
    void testConfigurationInfo() {
        String configInfo = smpConfiguration.getConfigurationInfo();
        assertNotNull(configInfo);
        assertTrue(configInfo.contains("SMP Configuration"));
        assertTrue(configInfo.contains("mode="));
        assertTrue(configInfo.contains("esmlDns="));
    }

    @Test
    void testBypassLogic() {
        // Test bypass logic for dummy mode
        boolean shouldBypass = smpConfiguration.shouldBypassSMPLookup();
        // In dummy mode, it should typically bypass or use direct endpoint
        assertNotNull(shouldBypass); // Just verify the method works
    }
}
