package com.morohub.apsp.core.service;

import com.helger.peppolid.factory.PeppolIdentifierFactory;
import com.helger.peppolid.IParticipantIdentifier;
import com.helger.peppolid.IDocumentTypeIdentifier;
import com.helger.peppolid.IProcessIdentifier;
import com.morohub.apsp.core.entity.MessageProcessingQueue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.UUID;

import com.morohub.apsp.common.exception.AS4Exception;
import com.morohub.apsp.common.exception.ConnectivityException;
import com.morohub.apsp.core.entity.ErrorMasterData;

/**
 * Service for sending Peppol MLS (Message Level Status) messages
 * Implements the Peppol MLS Specification v1.0.0
 */
@Service
public class MLSMessageSenderService {

    private static final Logger logger = LoggerFactory.getLogger(MLSMessageSenderService.class);

    @Autowired
    private AS4ConversionService as4ConversionService;

    @Value("${as4.mode:dummy}")
    private String as4Mode;

    @Value("${peppol.sender.participant.id:9908:test-sender}")
    private String defaultSenderParticipantId;

    // Peppol MLS Document Type and Process Identifiers (updated to match specification)
    private static final String MLS_DOCUMENT_TYPE_ID = "urn:oasis:names:specification:ubl:schema:xsd:ApplicationResponse-2::ApplicationResponse##urn:peppol:edec:mls:1.0::2.1";
    private static final String MLS_PROCESS_ID = "urn:peppol:edec:mls";

    /**
     * Send Peppol MLS message for the given processing result
     */
    public MLSMessageResult sendMLSMessage(MessageProcessingQueue message, String endpointUrl, MLSStatus status, String statusReason) {
        try {
            logger.info("🚀 Sending Peppol MLS message for: {} with status: {}", message.getMessageId(), status);

            // Create MLS ApplicationResponse document
            String mlsDocument = createMLSApplicationResponse(message, status, statusReason);

            // Create SBDH wrapper for the MLS document
            String sbdhWrappedMls = createSBDHWrapper(mlsDocument, message);

            // Send via Phase4PeppolSender
            MLSMessageResult result = sendViaPeppol(sbdhWrappedMls, message,endpointUrl);

            if (result.isSuccess()) {
                logger.info("✅ Peppol MLS message sent successfully: {}", result.getMlsMessageId());
            } else {
                logger.error("❌ Peppol MLS message send failed: {}", result.getErrorMessage());
            }

            return result;

        } catch (Exception e) {
            logger.error("❌ Error sending Peppol MLS message: {}", e.getMessage(), e);
            return MLSMessageResult.failure("Exception: " + e.getMessage());
        }
    }

    /**
     * Send MLS message with default success status
     */
    public MLSMessageResult sendMLSMessage(MessageProcessingQueue message, String endpointUrl) {
        return sendMLSMessage(message, endpointUrl, MLSStatus.AB, "Message delivered successfully without confirmation");
    }

    /**
     * Send MLS message with Schematron validation errors
     */
    public MLSMessageResult sendMLSMessageWithSchematronErrors(MessageProcessingQueue message, String endpointUrl,
                                                              MLSStatus status, String statusReason, List<String> validationErrors) {
        try {
            logger.info("🚀 Sending Peppol MLS message with Schematron errors for: {} with status: {}", message.getMessageId(), status);

            // Create MLS ApplicationResponse document with Schematron errors
            String mlsDocument = createMLSApplicationResponseWithSchematronErrors(message, status, statusReason, validationErrors);

            // Create SBDH wrapper for the MLS document
            String sbdhWrappedMls = createSBDHWrapper(mlsDocument, message);

            // Send via Phase4PeppolSender
            MLSMessageResult result = sendViaPeppol(sbdhWrappedMls, message,endpointUrl);

            if (result.isSuccess()) {
                logger.info("✅ Peppol MLS message with Schematron errors sent successfully: {}", result.getMlsMessageId());
            } else {
                logger.error("❌ Peppol MLS message with Schematron errors send failed: {}", result.getErrorMessage());
            }

            return result;

        } catch (Exception e) {
            logger.error("❌ Error sending Peppol MLS message with Schematron errors: {}", e.getMessage(), e);

            // Throw ConnectivityException for MLS sending failures
            throw ConnectivityException.connectionRefused("MLS_001",
                "Failed to send Peppol MLS message: " + e.getMessage(),
                "MLS_ENDPOINT", message.getMessageId(), null, e);
        }
    }

    /**
     * Create Peppol MLS ApplicationResponse document
     */
    private String createMLSApplicationResponse(MessageProcessingQueue message, MLSStatus status, String statusReason) {
        try {
            String mlsId = "MLS-" + UUID.randomUUID().toString();
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE);
            // Fix time format to match: 15:06:29.8631395Z
            String timeNow = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss.SSSSSSS")) + "Z";

            // Extract participant IDs and scheme IDs
            String[] senderParts = extractParticipantIdParts(message.getSenderParticipantId());
            String[] receiverParts = extractParticipantIdParts(message.getReceiverParticipantId());

            // Get original document instance ID
            String originalDocumentId = message.getOriginalDocumentInstanceId() != null ?
                message.getOriginalDocumentInstanceId() : extractOriginalMessageId(message.getUblXml());

            String mlsDocument = String.format(MLS_APPLICATION_RESPONSE_TEMPLATE,
                mlsId,                          // {0} - MLS ID
                timestamp,                      // {1} - Issue Date
                timeNow,                        // {2} - Issue Time
                senderParts[0],                 // {3} - Sender Scheme ID
                senderParts[1],                 // {4} - Sender ID
                receiverParts[0],               // {5} - Receiver Scheme ID
                receiverParts[1],               // {6} - Receiver ID
                status.getCode(),               // {7} - Status Response Code
                statusReason != null ? statusReason : status.getDescription(), // {8} - Status Description
                originalDocumentId,             // {9} - Original Document ID
                ""                              // {10} - Line Responses (empty for basic MLS)
            );

            logger.debug("📄 Created MLS ApplicationResponse with ID: {} and status: {}", mlsId, status);
            return mlsDocument;

        } catch (Exception e) {
            logger.error("❌ Error creating MLS ApplicationResponse: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to create MLS ApplicationResponse: " + e.getMessage(), e);
        }
    }

    /**
     * Create Peppol MLS ApplicationResponse document with Schematron validation errors
     */
    private String createMLSApplicationResponseWithSchematronErrors(MessageProcessingQueue message, MLSStatus status,
                                                                   String statusReason, List<String> validationErrors) {
        try {
            String mlsId = "MLS-" + UUID.randomUUID().toString();
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE);
            // Fix time format to match: 15:06:29.8631395Z
            String timeNow = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HH:mm:ss.SSSSSSS")) + "Z";

            // Extract participant IDs and scheme IDs
            String[] senderParts = extractParticipantIdParts(message.getSenderParticipantId());
            String[] receiverParts = extractParticipantIdParts(message.getReceiverParticipantId());

            // Get original document instance ID
            String originalDocumentId = message.getOriginalDocumentInstanceId() != null ?
                message.getOriginalDocumentInstanceId() : extractOriginalMessageId(message.getUblXml());

            // Build LineResponse elements for validation errors
            StringBuilder lineResponses = new StringBuilder();
            for (String error : validationErrors) {
                String lineId = extractLineIdFromError(error);
                String errorDescription = extractErrorDescription(error);
                String statusReasonCode = determineStatusReasonCode(error);

                lineResponses.append(String.format(LINE_RESPONSE_TEMPLATE,
                    lineId,                     // Line ID
                    errorDescription,           // Error Description
                    statusReasonCode           // Status Reason Code
                ));
            }

            String mlsDocument = String.format(MLS_APPLICATION_RESPONSE_TEMPLATE,
                mlsId,                          // {0} - MLS ID
                timestamp,                      // {1} - Issue Date
                timeNow,                        // {2} - Issue Time
                senderParts[0],                 // {3} - Sender Scheme ID
                senderParts[1],                 // {4} - Sender ID
                receiverParts[0],               // {5} - Receiver Scheme ID
                receiverParts[1],               // {6} - Receiver ID
                status.getCode(),               // {7} - Status Response Code
                statusReason != null ? statusReason : status.getDescription(), // {8} - Status Description
                originalDocumentId,             // {9} - Original Document ID
                lineResponses.toString()        // {10} - Line Responses
            );

            logger.debug("📄 Created MLS ApplicationResponse with Schematron errors, ID: {} and status: {}", mlsId, status);
            return mlsDocument;

        } catch (Exception e) {
            logger.error("❌ Error creating MLS ApplicationResponse with Schematron errors: {}", e.getMessage(), e);
            throw new AS4Exception("MLS_002", "Failed to create MLS ApplicationResponse: " + e.getMessage(),
                ErrorMasterData.ErrorType.SCHEMA_VALIDATION_FAILURE,
                ErrorMasterData.ErrorSeverity.HIGH, false, message.getMessageId(), null, e);
        }
    }

    /**
     * Create SBDH wrapper for MLS document
     */
    private String createSBDHWrapper(String mlsDocument, MessageProcessingQueue message) {
        try {
            String instanceId = UUID.randomUUID().toString();
            // Fix timestamp format to match the expected format: 2025-06-30T15:06:30.5288424
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss.SSSSSSS"));

            // Note: According to MLS spec, Sender should be receiver from original document (C3)
            // and Receiver should be sender from original document (C2)
            String senderParticipantId = message.getSenderParticipantId(); // C3 (current receiver)
            String receiverParticipantId = message.getReceiverParticipantId(); // C2 (original sender)

            // IMPORTANT: Use original BusinessScope from the original message ENTIRELY
            // Do NOT replace with MLS-specific values - preserve the original BusinessScope completely
            String businessScope;
            if (message.getOriginalSbdhBusinessScope() != null && !message.getOriginalSbdhBusinessScope().trim().isEmpty()) {
                businessScope = validateAndCleanBusinessScope(message.getOriginalSbdhBusinessScope());
                logger.info("✅ Using original BusinessScope from source message for MLS");
                logger.debug("📋 Original BusinessScope: {}", businessScope);
            } else {
                businessScope = createDefaultBusinessScope();
                logger.warn("⚠️ Original BusinessScope not available, using default - this should be investigated");
            }

            return String.format(SBDH_WRAPPER_TEMPLATE,
                senderParticipantId,            // {0} - Sender ID (C3)
                receiverParticipantId,          // {1} - Receiver ID (C2)
                instanceId,                     // {2} - Instance Identifier
                timestamp,                      // {3} - Creation Date Time
                businessScope,                  // {4} - Business Scope from original message (COMPLETE SECTION)
                mlsDocument                     // {5} - MLS Document
            );

        } catch (Exception e) {
            logger.error("❌ Error creating SBDH wrapper: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to create SBDH wrapper: " + e.getMessage(), e);
        }
    }

    /**
     * Validate and clean BusinessScope XML to ensure it's properly formatted
     */
    private String validateAndCleanBusinessScope(String businessScope) {
        try {
            // Remove any XML declarations that might be embedded
            String cleaned = businessScope.replaceAll("<\\?xml[^>]*\\?>", "");

            // Remove namespace prefixes that might cause conflicts
            cleaned = cleaned.replaceAll("sh:", "");
            cleaned = cleaned.replaceAll("xmlns:sh=\"[^\"]*\"", "");

            // Ensure proper formatting
            cleaned = cleaned.trim();

            // Validate that it starts with <BusinessScope> and ends with </BusinessScope>
            if (!cleaned.startsWith("<BusinessScope")) {
                logger.warn("⚠️ BusinessScope doesn't start with <BusinessScope>, adding wrapper");
                cleaned = "<BusinessScope>" + cleaned + "</BusinessScope>";
            }

            logger.debug("📋 Validated and cleaned BusinessScope: {}", cleaned);
            return cleaned;

        } catch (Exception e) {
            logger.error("❌ Error validating BusinessScope, using default: {}", e.getMessage());
            return createDefaultBusinessScope();
        }
    }

    /**
     * Create default BusinessScope if original is not available
     * NOTE: This should rarely be used - the original BusinessScope should always be preserved
     */
    private String createDefaultBusinessScope() {
        logger.warn("⚠️ Using default BusinessScope - original BusinessScope should be preserved from the original message");
        return """
                <BusinessScope>
                    <Scope>
                        <Type>DOCUMENTID</Type>
                        <InstanceIdentifier>urn:oasis:names:specification:ubl:schema:xsd:Invoice-2::Invoice##urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1</InstanceIdentifier>
                        <Identifier>busdox-docid-qns</Identifier>
                    </Scope>
                    <Scope>
                        <Type>PROCESSID</Type>
                        <InstanceIdentifier>urn:fdc:peppol.eu:2017:poacc:billing:01:1.0</InstanceIdentifier>
                        <Identifier>cenbii-procid-ubl</Identifier>
                    </Scope>
                </BusinessScope>""";
    }

    /**
     * Send MLS message via AS4ConversionService (reusing existing AS4 infrastructure)
     */
    private MLSMessageResult sendViaPeppol(String sbdhWrappedMls, MessageProcessingQueue message,String endpointUrl) {
        try {
            logger.info("📋 Sending MLS via AS4ConversionService");

            // Create AS4MessageMetadata for MLS
            AS4MessageMetadata metadata = createMLSMetadata(message);

            logger.debug("📋 MLS Metadata:");
            logger.debug("  - From: {}", metadata.getFormattedSenderParticipantId());
            logger.debug("  - To: {}", metadata.getFormattedReceiverParticipantId());
            logger.debug("  - Document Type: {}", metadata.getDocumentTypeId().getURIEncoded());
            logger.debug("  - Process: {}", metadata.getProcessId().getURIEncoded());

            // Use reflection to call the private sendAS4Message method from AS4ConversionService
            AS4ConversionResult as4Result = invokeSendAS4Message(sbdhWrappedMls, metadata,endpointUrl);

            if (as4Result.isSuccess()) {
                String mlsMessageId = as4Result.getAs4MessageId();
                logger.info("✅ MLS sent successfully via AS4ConversionService: {}", mlsMessageId);
                return MLSMessageResult.success(mlsMessageId, "MLS sent successfully via AS4");
            } else {
                logger.error("❌ MLS send failed via AS4ConversionService: {}", as4Result.getErrorMessage());
                return MLSMessageResult.failure("AS4 send failed: " + as4Result.getErrorMessage());
            }

        } catch (Exception e) {
            logger.error("❌ Error sending MLS via AS4ConversionService: {}", e.getMessage(), e);
            return MLSMessageResult.failure("AS4 send error: " + e.getMessage());
        }
    }

    /**
     * Create AS4MessageMetadata for MLS message
     */
    private AS4MessageMetadata createMLSMetadata(MessageProcessingQueue message) {
        AS4MessageMetadata metadata = new AS4MessageMetadata();

        // Set participant IDs (note: for MLS, sender is C3 and receiver is C2)
        IParticipantIdentifier senderParticipantId = createParticipantIdentifier(message.getSenderParticipantId());
        IParticipantIdentifier receiverParticipantId = createParticipantIdentifier(message.getReceiverParticipantId());

        metadata.setSenderParticipantId(senderParticipantId);
        metadata.setReceiverParticipantId(receiverParticipantId);

        // IMPORTANT: Set MLS-specific document type and process for AS4 transport
        // This is different from the BusinessScope which preserves original document identifiers
        metadata.setDocumentTypeId(createMLSDocumentTypeIdentifier());
        metadata.setProcessId(createMLSProcessIdentifier());

        // Set other metadata
        metadata.setMessageId("MLS-" + UUID.randomUUID().toString());
        metadata.setConversationId("MLS-CONV-" + UUID.randomUUID().toString());
        metadata.setCountryCode(message.getCountryCode());
        metadata.setInvoiceId("MLS-" + message.getMessageId());

        // Extract scheme IDs from participant IDs
        String[] senderParts = extractParticipantIdParts(message.getSenderParticipantId());
        String[] receiverParts = extractParticipantIdParts(message.getReceiverParticipantId());
        metadata.setSenderSchemeId(senderParts[0]);
        metadata.setReceiverSchemeId(receiverParts[0]);

        logger.debug("📋 Created MLS metadata with document type: {}", metadata.getDocumentTypeId().getURIEncoded());
        logger.debug("📋 Created MLS metadata with process: {}", metadata.getProcessId().getURIEncoded());

        return metadata;
    }

    /**
     * Invoke the public sendAS4Message method from AS4ConversionService
     */
    private AS4ConversionResult invokeSendAS4Message(String xml, AS4MessageMetadata metadata,String endpointUrl) throws Exception {
        // Create a new AS4ConversionResult
        AS4ConversionResult result = new AS4ConversionResult();
        result.setStartTime(System.currentTimeMillis());
        result.setAs4MessageId(metadata.getMessageId());

        try {
            // Call the public sendAS4Message method directly
            AS4ConversionResult sendResult = as4ConversionService.processAs4message(metadata, result,xml,endpointUrl);

            sendResult.setEndTime(System.currentTimeMillis());
            return sendResult;

        } catch (Exception e) {
            logger.error("❌ Error calling sendAS4Message: {}", e.getMessage(), e);
            result.setSuccess(false);
            result.setErrorMessage("AS4 send error: " + e.getMessage());
            result.setEndTime(System.currentTimeMillis());
            return result;
        }
    }

    /**
     * Helper methods for creating Peppol identifiers
     */
    private IParticipantIdentifier createParticipantIdentifier(String participantIdString) {
        try {
            if (participantIdString == null || participantIdString.trim().isEmpty()) {
                participantIdString = defaultSenderParticipantId;
            }

            // Handle different formats: "scheme::value" or "scheme:value" or just "value"
            if (participantIdString.contains("::")) {
                String[] parts = participantIdString.split("::", 2);
                return PeppolIdentifierFactory.INSTANCE.createParticipantIdentifierWithDefaultScheme(parts[1]);
            } else if (participantIdString.contains(":")) {
                String[] parts = participantIdString.split(":", 2);
                return PeppolIdentifierFactory.INSTANCE.createParticipantIdentifierWithDefaultScheme(parts[1]);
            } else {
                return PeppolIdentifierFactory.INSTANCE.createParticipantIdentifierWithDefaultScheme(participantIdString);
            }
        } catch (Exception e) {
            logger.warn("⚠️ Error parsing participant ID '{}', using default: {}", participantIdString, e.getMessage());
            return PeppolIdentifierFactory.INSTANCE.createParticipantIdentifierWithDefaultScheme("default");
        }
    }

    private IDocumentTypeIdentifier createMLSDocumentTypeIdentifier() {
        return PeppolIdentifierFactory.INSTANCE.createDocumentTypeIdentifierWithDefaultScheme(MLS_DOCUMENT_TYPE_ID);
    }

    private IProcessIdentifier createMLSProcessIdentifier() {
        return PeppolIdentifierFactory.INSTANCE.createProcessIdentifierWithDefaultScheme(MLS_PROCESS_ID);
    }

    /**
     * Extract original message ID from business document
     */
    private String extractOriginalMessageId(String ublXml) {
        try {
            // Simple regex to extract ID from UBL document
            java.util.regex.Pattern pattern = java.util.regex.Pattern.compile("<(?:cbc:)?ID[^>]*>([^<]+)</(?:cbc:)?ID>");
            java.util.regex.Matcher matcher = pattern.matcher(ublXml);
            if (matcher.find()) {
                return matcher.group(1);
            }
        } catch (Exception e) {
            logger.debug("Could not extract original message ID: {}", e.getMessage());
        }
        return "UNKNOWN-ID";
    }

    /**
     * Extract participant ID parts (scheme and ID)
     */
    private String[] extractParticipantIdParts(String participantId) {
        if (participantId == null || participantId.trim().isEmpty()) {
            return new String[]{"0235", "default"};
        }

        // Handle format like "0235:1345678901"
        if (participantId.contains(":")) {
            String[] parts = participantId.split(":", 2);
            return new String[]{parts[0], parts[1]};
        }

        // Default scheme if no scheme provided
        return new String[]{"0235", participantId};
    }

    /**
     * Extract line ID from Schematron error message
     */
    private String extractLineIdFromError(String error) {
        // Try to extract XPath-like line ID from error message
        // This is a simplified implementation - you may need to enhance based on actual error format
        if (error.contains("[") && error.contains("]")) {
            // Extract something like "/Invoice/cac:InvoiceLine[1]/cbc:ID[1]"
            int start = error.indexOf("/");
            int end = error.indexOf(" ", start);
            if (start != -1 && end != -1) {
                return error.substring(start, end);
            }
        }
        return "NA"; // Not applicable if no line ID can be extracted
    }

    /**
     * Extract error description from Schematron error message
     */
    private String extractErrorDescription(String error) {
        // Clean up the error message for display
        if (error.length() > 200) {
            return error.substring(0, 197) + "...";
        }
        return error;
    }

    /**
     * Determine status reason code from error type
     */
    private String determineStatusReasonCode(String error) {
        String errorLower = error.toLowerCase();
        if (errorLower.contains("schema") || errorLower.contains("syntax")) {
            return "SV"; // Syntax Violation
        } else if (errorLower.contains("warning")) {
            return "BW"; // Business Warning
        } else if (errorLower.contains("delivery") || errorLower.contains("failure")) {
            return "FD"; // Failure of Delivery
        } else {
            return "BV"; // Business Validation (default)
        }
    }

    /**
     * Get status reason code based on status and reason
     */
    private String getStatusReasonCode(MLSStatus status, String statusReason) {
        // Map status and reason to appropriate reason codes as per MLS spec
        switch (status) {
            case RE: // Rejected
                if (statusReason != null) {
                    if (statusReason.toLowerCase().contains("schema")) return "SV";
                    if (statusReason.toLowerCase().contains("validation")) return "BV";
                    if (statusReason.toLowerCase().contains("warning")) return "BW";
                    if (statusReason.toLowerCase().contains("delivery")) return "FD";
                }
                return "BV"; // Default to Business Validation error
            case AP: // Accepted with confirmation
            case AB: // Accepted without confirmation
            default:
                return ""; // No reason code for successful statuses
        }
    }

    /**
     * Peppol MLS Status enumeration
     */
    public enum MLSStatus {
        RE("RE", "Message Rejected"),
        AP("AP", "Message Delivery succeeded (with confirmation)"),
        AB("AB", "Message Delivery performed (without confirmation)");

        private final String code;
        private final String description;

        MLSStatus(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() { return code; }
        public String getDescription() { return description; }
    }

    /**
     * Result class for MLS message sending
     */
    public static class MLSMessageResult {
        private boolean success;
        private String mlsMessageId;
        private String response;
        private String errorMessage;

        public static MLSMessageResult success(String mlsMessageId, String response) {
            MLSMessageResult result = new MLSMessageResult();
            result.success = true;
            result.mlsMessageId = mlsMessageId;
            result.response = response;
            return result;
        }

        public static MLSMessageResult failure(String errorMessage) {
            MLSMessageResult result = new MLSMessageResult();
            result.success = false;
            result.errorMessage = errorMessage;
            return result;
        }

        // Getters
        public boolean isSuccess() { return success; }
        public String getMlsMessageId() { return mlsMessageId; }
        public String getResponse() { return response; }
        public String getErrorMessage() { return errorMessage; }
    }

    /**
     * Peppol MLS ApplicationResponse template (updated to match specification)
     */
    private static final String MLS_APPLICATION_RESPONSE_TEMPLATE = """
        <ApplicationResponse xmlns="urn:oasis:names:specification:ubl:schema:xsd:ApplicationResponse-2"
                           xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
                           xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2">
            <cbc:CustomizationID>urn:peppol:edec:mls:1.0</cbc:CustomizationID>
            <cbc:ProfileID>urn:peppol:edec:mls</cbc:ProfileID>
            <cbc:ID>%s</cbc:ID>
            <cbc:IssueDate>%s</cbc:IssueDate>
            <cbc:IssueTime>%s</cbc:IssueTime>
            <cac:SenderParty>
                <cbc:EndpointID schemeID="%s">%s</cbc:EndpointID>
            </cac:SenderParty>
            <cac:ReceiverParty>
                <cbc:EndpointID schemeID="%s">%s</cbc:EndpointID>
            </cac:ReceiverParty>
            <cac:DocumentResponse>
                <cac:Response>
                    <cbc:ResponseCode>%s</cbc:ResponseCode>
                    <cbc:Description>%s</cbc:Description>
                </cac:Response>
                <cac:DocumentReference>
                    <cbc:ID>%s</cbc:ID>
                </cac:DocumentReference>%s
            </cac:DocumentResponse>
        </ApplicationResponse>
        """;

    /**
     * Template for LineResponse elements in case of validation errors
     */
    private static final String LINE_RESPONSE_TEMPLATE = """

                <cac:LineResponse>
                    <cac:LineReference>
                        <cbc:LineID>%s</cbc:LineID>
                    </cac:LineReference>
                    <cac:Response>
                        <cbc:Description>%s</cbc:Description>
                        <cac:Status>
                            <cbc:StatusReasonCode>%s</cbc:StatusReasonCode>
                        </cac:Status>
                    </cac:Response>
                </cac:LineResponse>""";

    /**
     * SBDH wrapper template for MLS (updated to match specification)
     */
    private static final String SBDH_WRAPPER_TEMPLATE = """
        <?xml version="1.0" encoding="UTF-8"?>
        <StandardBusinessDocument xmlns="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader">
            <StandardBusinessDocumentHeader>
                <HeaderVersion>1.0</HeaderVersion>
                <Sender>
                    <Identifier Authority="iso6523-actorid-upis">%s</Identifier>
                </Sender>
                <Receiver>
                    <Identifier Authority="iso6523-actorid-upis">%s</Identifier>
                </Receiver>
                <DocumentIdentification>
                    <Standard>urn:oasis:names:specification:ubl:schema:xsd:ApplicationResponse-2</Standard>
                    <TypeVersion>2.1</TypeVersion>
                    <InstanceIdentifier>%s</InstanceIdentifier>
                    <Type>ApplicationResponse</Type>
                    <CreationDateAndTime>%s</CreationDateAndTime>
                </DocumentIdentification>
                %s
            </StandardBusinessDocumentHeader>
            %s
        </StandardBusinessDocument>
        """;
}
