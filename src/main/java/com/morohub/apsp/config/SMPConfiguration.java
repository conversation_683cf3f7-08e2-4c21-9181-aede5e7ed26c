package com.morohub.apsp.config;

import com.helger.smpclient.peppol.ISMPServiceMetadataProvider;
import com.helger.smpclient.peppol.SMPClientReadOnly;
import com.helger.phase4.peppol.Phase4PeppolSender;
import com.helger.phase4.dynamicdiscovery.AS4EndpointDetailProviderPeppol;
import com.helger.phase4.dynamicdiscovery.IAS4EndpointDetailProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Bean;

import java.net.URI;

/**
 * Configuration for SMP (Service Metadata Publisher) and ESML (Service Metadata Locator)
 * Handles different modes: auto (Phase4 discovery), manual (custom lookup), bypass (direct endpoint)
 */
@Configuration
public class SMPConfiguration {

    private static final Logger LOGGER = LoggerFactory.getLogger(SMPConfiguration.class);

    @Value("${as4.mode:dummy}")
    private String as4Mode;

    @Value("${peppol.esml.dns.production:edelivery.tech.ec.europa.eu.}")
    private String esmlDnsProduction;

    @Value("${peppol.esml.dns.test:acc.edelivery.tech.ec.europa.eu.}")
    private String esmlDnsTest;

    @Value("${peppol.smp.url:}")
    private String customSmpUrl;

    @Value("${smp.lookup.bypass:false}")
    private boolean smpLookupBypass;

    @Value("${smp.lookup.bypass.url:}")
    private String smpBypassUrl;

    /**
     * Get the appropriate ESML DNS value based on current mode
     */
    public String getESMLDnsValue() {
        String dnsValue;
        if ("production".equals(as4Mode)) {
            dnsValue = esmlDnsProduction;
            LOGGER.info("🏭 Production mode - using ESML DNS: {}", dnsValue);
        } else {
            // dummy mode (test)
            dnsValue = esmlDnsTest;
            LOGGER.info("🧪 Dummy mode (test) - using ESML DNS: {}", dnsValue);
        }
        return dnsValue;
    }

    /**
     * Check if we're in production mode
     */
    public boolean isProductionMode() {
        return "production".equals(as4Mode);
    }

    /**
     * Check if we're in dummy (test) mode
     */
    public boolean isDummyMode() {
        return !isProductionMode();
    }

    /**
     * Check if SMP lookup should be bypassed (for dummy mode with direct endpoint)
     */
    public boolean shouldBypassSMPLookup() {
        return smpLookupBypass ;
    }

    /**
     * Get bypass URL for testing
     */
    public String getBypassUrl() {
        return smpBypassUrl;
    }

    /**
     * Create SMP client with ESML DNS configuration
     */
    public ISMPServiceMetadataProvider createSMPClient() {
        try {
            if (customSmpUrl != null && !customSmpUrl.trim().isEmpty()) {
                LOGGER.info("🌐 Creating SMP client with custom URL: {}", customSmpUrl);
                return new SMPClientReadOnly(new URI(customSmpUrl));
            } else {
                String esmlDns = getESMLDnsValue();
                LOGGER.info("🌐 Creating SMP client with ESML DNS: {}", esmlDns);
                // Phase4 will handle SMP discovery automatically using the ESML DNS
                return null; // Let Phase4 handle it automatically
            }
        } catch (Exception e) {
            LOGGER.error("❌ Error creating SMP client: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to create SMP client", e);
        }
    }

    /**
     * Configure Phase4 SMP settings based on mode (dummy/production)
     */
    public void configurePhase4SMP(Phase4PeppolSender.SBDHBuilder builder) {
        String esmlDns = getESMLDnsValue();

        if (isProductionMode()) {
            LOGGER.info("🏭 Production mode - Phase4 will use automatic SMP discovery with ESML DNS: {}", esmlDns);
            // Phase4 handles SMP discovery automatically when no endpoint is provided
            // It will use the configured ESML DNS value for production
        } else {
            LOGGER.info("🧪 Dummy mode - Phase4 will use automatic SMP discovery with test ESML DNS: {}", esmlDns);
            // Phase4 handles SMP discovery automatically for test environment
        }

        // If custom SMP URL is provided, configure it
        if (customSmpUrl != null && !customSmpUrl.trim().isEmpty()) {
            LOGGER.info("🌐 Using custom SMP URL: {}", customSmpUrl);
            try {
                ISMPServiceMetadataProvider smpClient = new SMPClientReadOnly(new URI(customSmpUrl));
                IAS4EndpointDetailProvider endpointProvider = new AS4EndpointDetailProviderPeppol(smpClient);
                builder.endpointDetailProvider(endpointProvider);
                LOGGER.info("✅ Configured Phase4 with custom SMP client");
            } catch (Exception e) {
                LOGGER.error("❌ Failed to configure custom SMP client: {}", e.getMessage(), e);
                throw new RuntimeException("Failed to configure custom SMP client", e);
            }
        }
    }

    /**
     * Get configuration info for diagnostics
     */
    public String getConfigurationInfo() {
        return String.format(
            "SMP Configuration: mode=%s, esmlDns=%s, customSmp=%s, bypass=%s",
            as4Mode, getESMLDnsValue(),
            customSmpUrl != null ? customSmpUrl : "none",
            shouldBypassSMPLookup()
        );
    }
}
