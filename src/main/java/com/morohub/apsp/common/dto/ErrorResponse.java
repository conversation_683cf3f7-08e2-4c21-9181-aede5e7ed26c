package com.morohub.apsp.common.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.morohub.apsp.core.entity.ErrorMasterData;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * Standardized error response DTO for API responses
 * Requirement 2: Access point API response should provide appropriate error codes and descriptions
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ErrorResponse {

    private String errorCode;
    private String errorType;
    private String errorDescription;
    private String severity;
    private boolean retryable;
    private Long retryAfterMs;
    private String messageId;
    private String requestId;
    private LocalDateTime timestamp;
    private String path;
    private String method;
    private Map<String, Object> additionalInfo;

    // Default constructor
    public ErrorResponse() {
        this.timestamp = LocalDateTime.now();
    }

    // Basic constructor
    public ErrorResponse(String errorCode, String errorDescription) {
        this();
        this.errorCode = errorCode;
        this.errorDescription = errorDescription;
    }

    // Constructor with error type
    public ErrorResponse(String errorCode, String errorType, String errorDescription) {
        this();
        this.errorCode = errorCode;
        this.errorType = errorType;
        this.errorDescription = errorDescription;
    }

    // Full constructor
    public ErrorResponse(String errorCode, String errorType, String errorDescription, 
                        String severity, boolean retryable, Long retryAfterMs, 
                        String messageId, String requestId) {
        this();
        this.errorCode = errorCode;
        this.errorType = errorType;
        this.errorDescription = errorDescription;
        this.severity = severity;
        this.retryable = retryable;
        this.retryAfterMs = retryAfterMs;
        this.messageId = messageId;
        this.requestId = requestId;
    }

    // Constructor from ErrorMasterData
    public static ErrorResponse fromErrorMasterData(ErrorMasterData errorMasterData, 
                                                   String messageId, String requestId) {
        ErrorResponse response = new ErrorResponse();
        response.errorCode = errorMasterData.getErrorCode();
        response.errorType = errorMasterData.getErrorType().name();
        response.errorDescription = errorMasterData.getErrorDescription();
        response.severity = errorMasterData.getSeverity() != null ? errorMasterData.getSeverity().name() : null;
        response.retryable = Boolean.TRUE.equals(errorMasterData.getRetryRequired());
        response.retryAfterMs = errorMasterData.getRetryIntervalMs();
        response.messageId = messageId;
        response.requestId = requestId;
        return response;
    }

    // Builder pattern
    public static ErrorResponseBuilder builder() {
        return new ErrorResponseBuilder();
    }

    public static class ErrorResponseBuilder {
        private ErrorResponse response = new ErrorResponse();

        public ErrorResponseBuilder errorCode(String errorCode) {
            response.errorCode = errorCode;
            return this;
        }

        public ErrorResponseBuilder errorType(String errorType) {
            response.errorType = errorType;
            return this;
        }

        public ErrorResponseBuilder errorDescription(String errorDescription) {
            response.errorDescription = errorDescription;
            return this;
        }

        public ErrorResponseBuilder severity(String severity) {
            response.severity = severity;
            return this;
        }

        public ErrorResponseBuilder retryable(boolean retryable) {
            response.retryable = retryable;
            return this;
        }

        public ErrorResponseBuilder retryAfterMs(Long retryAfterMs) {
            response.retryAfterMs = retryAfterMs;
            return this;
        }

        public ErrorResponseBuilder messageId(String messageId) {
            response.messageId = messageId;
            return this;
        }

        public ErrorResponseBuilder requestId(String requestId) {
            response.requestId = requestId;
            return this;
        }

        public ErrorResponseBuilder path(String path) {
            response.path = path;
            return this;
        }

        public ErrorResponseBuilder method(String method) {
            response.method = method;
            return this;
        }

        public ErrorResponseBuilder additionalInfo(Map<String, Object> additionalInfo) {
            response.additionalInfo = additionalInfo;
            return this;
        }

        public ErrorResponse build() {
            return response;
        }
    }

    // Getters and Setters
    public String getErrorCode() { return errorCode; }
    public void setErrorCode(String errorCode) { this.errorCode = errorCode; }

    public String getErrorType() { return errorType; }
    public void setErrorType(String errorType) { this.errorType = errorType; }

    public String getErrorDescription() { return errorDescription; }
    public void setErrorDescription(String errorDescription) { this.errorDescription = errorDescription; }

    public String getSeverity() { return severity; }
    public void setSeverity(String severity) { this.severity = severity; }

    public boolean isRetryable() { return retryable; }
    public void setRetryable(boolean retryable) { this.retryable = retryable; }

    public Long getRetryAfterMs() { return retryAfterMs; }
    public void setRetryAfterMs(Long retryAfterMs) { this.retryAfterMs = retryAfterMs; }

    public String getMessageId() { return messageId; }
    public void setMessageId(String messageId) { this.messageId = messageId; }

    public String getRequestId() { return requestId; }
    public void setRequestId(String requestId) { this.requestId = requestId; }

    public LocalDateTime getTimestamp() { return timestamp; }
    public void setTimestamp(LocalDateTime timestamp) { this.timestamp = timestamp; }

    public String getPath() { return path; }
    public void setPath(String path) { this.path = path; }

    public String getMethod() { return method; }
    public void setMethod(String method) { this.method = method; }

    public Map<String, Object> getAdditionalInfo() { return additionalInfo; }
    public void setAdditionalInfo(Map<String, Object> additionalInfo) { this.additionalInfo = additionalInfo; }
}
