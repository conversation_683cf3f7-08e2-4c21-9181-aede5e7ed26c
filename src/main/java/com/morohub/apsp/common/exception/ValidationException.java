package com.morohub.apsp.common.exception;

import com.morohub.apsp.core.entity.ErrorMasterData;
import java.util.List;
import java.util.ArrayList;

/**
 * Exception for validation errors (Schema, Schematron, Business Rules)
 * Supports multiple validation errors in a single exception
 */
public class ValidationException extends AS4Exception {

    private final List<ValidationError> validationErrors;
    private final String validationType;

    public ValidationException(String errorCode, String message, List<ValidationError> validationErrors) {
        super(errorCode, message, ErrorMasterData.ErrorType.SCHEMA_VALIDATION_FAILURE, 
              ErrorMasterData.ErrorSeverity.HIGH, false);
        this.validationErrors = validationErrors != null ? validationErrors : new ArrayList<>();
        this.validationType = "GENERAL";
    }

    public ValidationException(String errorCode, String message, List<ValidationError> validationErrors, 
                             String validationType) {
        super(errorCode, message, getErrorTypeForValidationType(validationType), 
              ErrorMasterData.ErrorSeverity.HIGH, false);
        this.validationErrors = validationErrors != null ? validationErrors : new ArrayList<>();
        this.validationType = validationType;
    }

    public ValidationException(String errorCode, String message, List<ValidationError> validationErrors, 
                             String validationType, String messageId, String requestId) {
        super(errorCode, message, getErrorTypeForValidationType(validationType), 
              ErrorMasterData.ErrorSeverity.HIGH, false, messageId, requestId);
        this.validationErrors = validationErrors != null ? validationErrors : new ArrayList<>();
        this.validationType = validationType;
    }

    // Single validation error constructor
    public ValidationException(String errorCode, String message, String field, String errorMessage) {
        super(errorCode, message, ErrorMasterData.ErrorType.SCHEMA_VALIDATION_FAILURE, 
              ErrorMasterData.ErrorSeverity.HIGH, false);
        this.validationErrors = new ArrayList<>();
        this.validationErrors.add(new ValidationError(field, errorMessage));
        this.validationType = "GENERAL";
    }

    // Schematron validation error constructor
    public static ValidationException schematronValidation(String errorCode, String message, 
                                                          List<ValidationError> validationErrors, 
                                                          String messageId, String requestId) {
        return new ValidationException(errorCode, message, validationErrors, "SCHEMATRON", messageId, requestId);
    }

    // Schema validation error constructor
    public static ValidationException schemaValidation(String errorCode, String message, 
                                                      List<ValidationError> validationErrors, 
                                                      String messageId, String requestId) {
        return new ValidationException(errorCode, message, validationErrors, "SCHEMA", messageId, requestId);
    }

    // Business rule validation error constructor
    public static ValidationException businessRuleValidation(String errorCode, String message, 
                                                            List<ValidationError> validationErrors, 
                                                            String messageId, String requestId) {
        return new ValidationException(errorCode, message, validationErrors, "BUSINESS_RULE", messageId, requestId);
    }

    private static ErrorMasterData.ErrorType getErrorTypeForValidationType(String validationType) {
        switch (validationType.toUpperCase()) {
            case "SCHEMATRON":
                return ErrorMasterData.ErrorType.SCHEMATRON_VALIDATION_FAILURE;
            case "SCHEMA":
                return ErrorMasterData.ErrorType.SCHEMA_VALIDATION_FAILURE;
            case "BUSINESS_RULE":
                return ErrorMasterData.ErrorType.BUSINESS_RULE_VIOLATION;
            default:
                return ErrorMasterData.ErrorType.SCHEMA_VALIDATION_FAILURE;
        }
    }

    public List<ValidationError> getValidationErrors() {
        return validationErrors;
    }

    public String getValidationType() {
        return validationType;
    }

    public boolean hasValidationErrors() {
        return validationErrors != null && !validationErrors.isEmpty();
    }

    public int getErrorCount() {
        return validationErrors != null ? validationErrors.size() : 0;
    }

    /**
     * Get formatted error message with all validation errors
     */
    public String getFormattedErrorMessage() {
        StringBuilder sb = new StringBuilder(getMessage());
        if (hasValidationErrors()) {
            sb.append("\nValidation Errors:");
            for (int i = 0; i < validationErrors.size(); i++) {
                ValidationError error = validationErrors.get(i);
                sb.append(String.format("\n%d. Field: %s, Error: %s", i + 1, error.getField(), error.getMessage()));
            }
        }
        return sb.toString();
    }

    /**
     * Validation Error class
     */
    public static class ValidationError {
        private final String field;
        private final String message;
        private final String code;
        private final String value;

        public ValidationError(String field, String message) {
            this.field = field;
            this.message = message;
            this.code = null;
            this.value = null;
        }

        public ValidationError(String field, String message, String code) {
            this.field = field;
            this.message = message;
            this.code = code;
            this.value = null;
        }

        public ValidationError(String field, String message, String code, String value) {
            this.field = field;
            this.message = message;
            this.code = code;
            this.value = value;
        }

        public String getField() { return field; }
        public String getMessage() { return message; }
        public String getCode() { return code; }
        public String getValue() { return value; }

        @Override
        public String toString() {
            return String.format("ValidationError{field='%s', message='%s', code='%s', value='%s'}", 
                               field, message, code, value);
        }
    }
}
