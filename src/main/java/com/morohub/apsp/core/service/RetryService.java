package com.morohub.apsp.core.service;

import com.morohub.apsp.core.entity.ErrorLog;
import com.morohub.apsp.core.entity.MessageProcessingQueue;
import com.morohub.apsp.core.repository.ErrorLogRepository;
import com.morohub.apsp.core.repository.MessageProcessingQueueRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * Service for handling automatic retry mechanism
 * Requirement 4: For technical connectivity errors - Facility for automatic retry should be provided
 */
@Service
@Transactional
public class RetryService {

    private static final Logger logger = LoggerFactory.getLogger(RetryService.class);

    @Autowired
    private ErrorLogRepository errorLogRepository;

    @Autowired
    private MessageProcessingQueueRepository messageQueueRepository;

    @Autowired
    private MessageProcessingService messageProcessingService;

    @Autowired
    private ErrorManagementService errorManagementService;

    @Autowired
    private ReprocessingService reprocessingService;

    @Value("${error.retry.enabled:true}")
    private boolean retryEnabled;

    @Value("${error.retry.batch.size:10}")
    private int retryBatchSize;

    @Value("${error.retry.max.concurrent:3}")
    private int maxConcurrentRetries;

    /**
     * Scheduled method to process retry queue
     * Runs every 30 seconds by default
     */
    @Scheduled(fixedDelayString = "${error.retry.poll.interval.ms:30000}")
    public void processRetryQueue() {
        if (!retryEnabled) {
            logger.debug("Retry processing is disabled");
            return;
        }

        try {
            logger.debug("🔄 Processing retry queue...");

            // Get errors ready for retry
            List<ErrorLog> errorsToRetry = errorLogRepository.findErrorsReadyForRetry(LocalDateTime.now());
            
            if (errorsToRetry.isEmpty()) {
                logger.debug("No errors ready for retry");
                return;
            }

            logger.info("Found {} errors ready for retry", errorsToRetry.size());

            // Process in batches
            int processed = 0;
            for (ErrorLog errorLog : errorsToRetry) {
                if (processed >= retryBatchSize) {
                    logger.info("Reached batch size limit ({}), stopping retry processing", retryBatchSize);
                    break;
                }

                try {
                    processRetryForError(errorLog);
                    processed++;
                } catch (Exception e) {
                    logger.error("Failed to process retry for error log {}: {}", errorLog.getId(), e.getMessage(), e);
                }
            }

            logger.info("✅ Processed {} retry attempts", processed);

        } catch (Exception e) {
            logger.error("❌ Error in retry queue processing: {}", e.getMessage(), e);
        }
    }

    /**
     * Process retry for a specific error
     */
    public void processRetryForError(ErrorLog errorLog) {
        try {
            logger.info("🔄 Processing retry for error log: {} (attempt {})", 
                       errorLog.getId(), errorLog.getRetryAttempt() + 1);

            // Mark as retry in progress
            errorLog.setProcessingStatus(ErrorLog.ProcessingStatus.RETRY_IN_PROGRESS);
            errorLog.incrementRetryAttempt();
            errorLogRepository.save(errorLog);

            // Check if max retries exceeded
            if (errorLog.hasExceededMaxRetries()) {
                logger.warn("⚠️ Error log {} exceeded max retries, marking as failed", errorLog.getId());
                errorLog.setProcessingStatus(ErrorLog.ProcessingStatus.FAILED_MAX_RETRIES);
                errorLogRepository.save(errorLog);
                return;
            }

            // Attempt to retry based on error type
            boolean retrySuccess = attemptRetry(errorLog);

            if (retrySuccess) {
                logger.info("✅ Retry successful for error log: {}", errorLog.getId());
                errorLog.markAsResolved("SYSTEM", "Resolved through automatic retry");
                errorLogRepository.save(errorLog);
            } else {
                logger.warn("❌ Retry failed for error log: {}, scheduling next retry", errorLog.getId());
                errorLog.scheduleNextRetry();
                errorLogRepository.save(errorLog);
            }

        } catch (Exception e) {
            logger.error("❌ Exception during retry processing for error log {}: {}", 
                        errorLog.getId(), e.getMessage(), e);
            
            // Schedule next retry on exception
            errorLog.scheduleNextRetry();
            errorLogRepository.save(errorLog);
        }
    }

    /**
     * Attempt to retry based on the error type and flow type
     */
    private boolean attemptRetry(ErrorLog errorLog) {
        String messageId = errorLog.getMessageId();

        if (messageId == null) {
            logger.warn("No message ID found for error log {}, cannot retry", errorLog.getId());
            return false;
        }

        // Find the message in processing queue
        Optional<MessageProcessingQueue> messageOpt = messageQueueRepository.findByMessageId(messageId);

        if (messageOpt.isEmpty()) {
            logger.warn("Message {} not found in processing queue, cannot retry", messageId);
            return false;
        }

        MessageProcessingQueue message = messageOpt.get();

        // Check message status
        if (message.getStatus() == MessageProcessingQueue.ProcessingStatus.COMPLETED) {
            logger.info("Message {} already completed, marking error as resolved", messageId);
            return true;
        }

        try {
            // For MLS messages, use the existing MLS processing logic
            if (message.getFlowType() == MessageProcessingQueue.FlowType.MLS_MESSAGE) {
                // Reset message status for retry
                message.setStatus(MessageProcessingQueue.ProcessingStatus.RETRY);
                message.setErrorMessage(null);
                messageQueueRepository.save(message);

                // Use existing MLS processing
                messageProcessingService.processMessage(message);

                // Check if processing was successful
                MessageProcessingQueue updatedMessage = messageQueueRepository.findByMessageId(messageId).orElse(message);
                return updatedMessage.getStatus() == MessageProcessingQueue.ProcessingStatus.COMPLETED;
            } else {
                // For non-MLS messages, use reprocessing service with same controller logic
                message.setStatus(MessageProcessingQueue.ProcessingStatus.RETRY);
                messageQueueRepository.save(message);

                boolean retrySuccess = reprocessingService.reprocessMessage(message);

                if (retrySuccess) {
                    logger.info("✅ Reprocessing successful for message: {}", messageId);
                    return true;
                } else {
                    logger.warn("❌ Reprocessing failed for message: {}", messageId);
                    return false;
                }
            }

        } catch (Exception e) {
            logger.error("Failed to retry message processing for {}: {}", messageId, e.getMessage(), e);
            message.setStatus(MessageProcessingQueue.ProcessingStatus.ERROR);
          //  message.setProcessingNotes("Retry exception: " + e.getMessage());
            messageQueueRepository.save(message);
            return false;
        }
    }

    /**
     * Manually trigger retry for a specific error log
     */
    public boolean manualRetry(Long errorLogId, String triggeredBy) {
        try {
            Optional<ErrorLog> errorLogOpt = errorLogRepository.findById(errorLogId);
            
            if (errorLogOpt.isEmpty()) {
                logger.warn("Error log {} not found for manual retry", errorLogId);
                return false;
            }

            ErrorLog errorLog = errorLogOpt.get();

            // Check if error is retryable
            if (!Boolean.TRUE.equals(errorLog.getErrorMasterData().getRetryRequired())) {
                logger.warn("Error log {} is not retryable", errorLogId);
                return false;
            }

            // Check if already resolved
            if (Boolean.TRUE.equals(errorLog.getResolved())) {
                logger.warn("Error log {} is already resolved", errorLogId);
                return false;
            }

            logger.info("🔄 Manual retry triggered for error log {} by {}", errorLogId, triggeredBy);

            // Reset retry count for manual retry
            errorLog.setRetryAttempt(0);
            errorLog.setProcessingStatus(ErrorLog.ProcessingStatus.RETRY_SCHEDULED);
            errorLog.setNextRetryAt(LocalDateTime.now());
            errorLogRepository.save(errorLog);

            // Process immediately
            processRetryForError(errorLog);

            return true;

        } catch (Exception e) {
            logger.error("Failed to trigger manual retry for error log {}: {}", errorLogId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * Discard error (mark as discarded, no more retries)
     */
    public boolean discardError(Long errorLogId, String discardedBy, String reason) {
        try {
            Optional<ErrorLog> errorLogOpt = errorLogRepository.findById(errorLogId);
            
            if (errorLogOpt.isEmpty()) {
                logger.warn("Error log {} not found for discard", errorLogId);
                return false;
            }

            ErrorLog errorLog = errorLogOpt.get();
            errorLog.setProcessingStatus(ErrorLog.ProcessingStatus.DISCARDED);
            errorLog.setResolutionNotes(String.format("Discarded by %s. Reason: %s", discardedBy, reason));
            errorLog.setResolvedBy(discardedBy);
            errorLog.setResolvedDate(LocalDateTime.now());
            
            errorLogRepository.save(errorLog);

            logger.info("Error log {} discarded by {}: {}", errorLogId, discardedBy, reason);
            return true;

        } catch (Exception e) {
            logger.error("Failed to discard error log {}: {}", errorLogId, e.getMessage(), e);
            return false;
        }
    }

    /**
     * Get retry statistics
     */
    public RetryStatistics getRetryStatistics() {
        try {
            LocalDateTime last24Hours = LocalDateTime.now().minusHours(24);
            
            long totalErrors = errorLogRepository.countErrorsInDateRange(last24Hours, LocalDateTime.now());
            long unresolvedErrors = errorLogRepository.countUnresolvedErrors();
            long errorsReadyForRetry = errorLogRepository.findErrorsReadyForRetry(LocalDateTime.now()).size();
            long errorsExceededMaxRetries = errorLogRepository.findErrorsExceededMaxRetries().size();

            return new RetryStatistics(totalErrors, unresolvedErrors, errorsReadyForRetry, errorsExceededMaxRetries);

        } catch (Exception e) {
            logger.error("Failed to get retry statistics: {}", e.getMessage(), e);
            return new RetryStatistics(0, 0, 0, 0);
        }
    }

    /**
     * Retry statistics class
     */
    public static class RetryStatistics {
        private final long totalErrorsLast24Hours;
        private final long unresolvedErrors;
        private final long errorsReadyForRetry;
        private final long errorsExceededMaxRetries;

        public RetryStatistics(long totalErrorsLast24Hours, long unresolvedErrors, 
                             long errorsReadyForRetry, long errorsExceededMaxRetries) {
            this.totalErrorsLast24Hours = totalErrorsLast24Hours;
            this.unresolvedErrors = unresolvedErrors;
            this.errorsReadyForRetry = errorsReadyForRetry;
            this.errorsExceededMaxRetries = errorsExceededMaxRetries;
        }

        public long getTotalErrorsLast24Hours() { return totalErrorsLast24Hours; }
        public long getUnresolvedErrors() { return unresolvedErrors; }
        public long getErrorsReadyForRetry() { return errorsReadyForRetry; }
        public long getErrorsExceededMaxRetries() { return errorsExceededMaxRetries; }
    }
}
