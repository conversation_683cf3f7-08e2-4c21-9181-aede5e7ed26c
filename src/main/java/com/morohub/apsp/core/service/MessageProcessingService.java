package com.morohub.apsp.core.service;

import com.morohub.apsp.config.CountryConfigurationService;
import com.morohub.apsp.core.entity.MessageProcessingQueue;
import com.morohub.apsp.core.entity.MessageProcessingQueue.ProcessingStatus;
import com.morohub.apsp.core.service.SchematronValidationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import com.morohub.apsp.common.exception.AS4Exception;
import com.morohub.apsp.common.exception.ConnectivityException;
import com.morohub.apsp.common.exception.ValidationException;
import com.morohub.apsp.core.entity.ErrorMasterData;

/**
 * Service for processing messages from the queue
 * Handles polling, SMP lookup, and MLS message sending
 */
@Service
public class MessageProcessingService {

    private static final Logger logger = LoggerFactory.getLogger(MessageProcessingService.class);

    @Autowired
    private MessageQueueService messageQueueService;

    @Autowired
    private SMPLookupService smpLookupService;

    @Autowired
    private MLSMessageSenderService mlsMessageSenderService;

    @Autowired
    private MLSValidationService mlsValidationService;

    @Autowired
    private CountryConfigurationService countryConfigurationService;

    @Autowired
    private SchematronValidationService schematronValidationService;

    @Autowired
    private MLSConfigurationService mlsConfigurationService;

    @Autowired
    private FlowSpecificRetryService flowSpecificRetryService;

    @Value("${message.processing.enabled:true}")
    private boolean processingEnabled;

    @Value("${message.processing.batch.size:10}")
    private int batchSize;

    @Value("${message.processing.parallel.threads:3}")
    private int parallelThreads;

    private final ExecutorService executorService;

    public MessageProcessingService() {
        this.executorService = Executors.newFixedThreadPool(5);
    }

    /**
     * Scheduled method to poll and process messages
     * Runs every 30 seconds by default
     */
    @Scheduled(fixedDelayString = "${message.processing.poll.interval.ms:30000}")
    public void processMessages() {
        if (!processingEnabled) {
            logger.debug("📋 Message processing is disabled");
            return;
        }

        try {
            logger.debug("🔄 Starting MLS message processing cycle");

            // Get only MLS messages ready for processing
            List<MessageProcessingQueue> messages = messageQueueService.getMlsMessagesReadyForProcessing();

            if (messages.isEmpty()) {
                logger.debug("📋 No messages ready for processing");
                return;
            }

            logger.info("📦 Found {} messages ready for processing", messages.size());

            // Process messages in batches
            int processed = 0;
            for (int i = 0; i < messages.size() && i < batchSize; i++) {
                MessageProcessingQueue message = messages.get(i);
                
                // Process message asynchronously
                CompletableFuture.runAsync(() -> processMessage(message), executorService);
                processed++;
            }

            logger.info("🚀 Started processing {} messages", processed);

        } catch (Exception e) {
            logger.error("❌ Error in message processing cycle: {}", e.getMessage(), e);
        }
    }

    /**
     * Process a single message based on flow type
     */
    public void processMessage(MessageProcessingQueue message) {
        try {
            logger.info("🔄 Processing {} message: {}", message.getFlowType(), message.getMessageId());

            // Mark message as processing
            messageQueueService.markMessageAsProcessing(message.getId());

            // Route to appropriate processing method based on flow type
            if (message.getFlowType() == null) {
                // Default to MLS for backward compatibility
                message.setFlowType(MessageProcessingQueue.FlowType.MLS_MESSAGE);
            }

            switch (message.getFlowType()) {
                case MLS_MESSAGE:
                    processMlsMessage(message);
                    break;
                case FORWARD_FLOW:
                case REVERSE_FLOW:
                case XML_VALIDATION:
                case PEPPOL_SBD_INVOICE:
                    // For non-MLS flows, use flow-specific retry service
                    boolean success = flowSpecificRetryService.processRetryForMessage(message);
                    if (success) {
                        messageQueueService.markMessageAsCompleted(message.getId(),message.getMlsMessageId());
                    } else {
                        messageQueueService.markMessageForRetry(message.getId(), "Flow-specific processing failed");
                    }
                    break;
                default:
                    logger.warn("⚠️ Unknown flow type: {}", message.getFlowType());
                    messageQueueService.markMessageForRetry(message.getId(), "Unknown flow type: " + message.getFlowType());
            }

        }
        catch (ConnectivityException e) {
            logger.error("❌ Connectivity error processing message {}: {}", message.getMessageId(), e.getMessage(), e);
            messageQueueService.markMessageForRetry(message.getId(), "Connectivity error: " + e.getMessage());
            throw e; // Re-throw for error management system
        } catch (ValidationException e) {
            logger.error("❌ Validation error processing message {}: {}", message.getMessageId(), e.getMessage(), e);
            messageQueueService.markMessageForRetry(message.getId(), "Validation error: " + e.getMessage());
            throw e; // Re-throw for error management system
        }catch (AS4Exception e) {
            logger.error("❌ AS4 error processing message {}: {}", message.getMessageId(), e.getMessage(), e);
            messageQueueService.markMessageForRetry(message.getId(), "AS4 error: " + e.getMessage());
            throw e; // Re-throw for error management system
        }  catch (Exception e) {
            logger.error("❌ Unexpected error processing message {}: {}", message.getMessageId(), e.getMessage(), e);
            messageQueueService.markMessageForRetry(message.getId(), "Processing error: " + e.getMessage());

            // Convert to AS4Exception for error management
            throw new AS4Exception("SYS_001", "Message processing failed: " + e.getMessage(),
                ErrorMasterData.ErrorType.SYSTEM_ERROR,
                ErrorMasterData.ErrorSeverity.HIGH, true, message.getMessageId(), null, e);
        }
    }

    /**
     * Process MLS message (existing logic)
     */
    private void processMlsMessage(MessageProcessingQueue message) throws Exception {
        try {

            // Step 1: Perform country-specific Schematron validation
            List<String> schematronFiles = countryConfigurationService.getSchematronFiles(message.getCountryCode(), message.getDocumentType());
            List<String> validationErrors = schematronValidationService.validateXmlWithMultipleSchematrons(message.getUblXml(), schematronFiles);

            if (!validationErrors.isEmpty()) {
                logger.warn("⚠️ Message {} failed Schematron validation, sending rejection MLS", message.getMessageId());

                // Send rejection MLS message with Schematron errors
                sendRejectionMLSWithSchematronErrors(message, validationErrors);
                return;
            }

            // Step 2: Validate the message according to Peppol MLS rules
            MLSValidationService.ValidationResult validationResult = mlsValidationService.validateMessage(message);

            if (validationResult.hasErrors()) {
                logger.warn("⚠️ Message {} failed basic validation, sending rejection MLS", message.getMessageId());

                // Send rejection MLS message
                sendRejectionMLS(message, validationResult);
                return;
            }

            // Step 3: Perform SMP lookup to find endpoint for C2 (original sender)
            SMPLookupService.SMPLookupResult smpResult = smpLookupService.performMLSSMPLookup(message.getReceiverParticipantId(),message.getMessageId());

            if (!smpResult.isSuccess()) {
                logger.error("❌ MLS SMP lookup failed for message {}: {}", message.getMessageId(), smpResult.getErrorMessage());
                messageQueueService.markMessageForRetry(message.getId(), "MLS SMP lookup failed: " + smpResult.getErrorMessage());
                return;
            }

            String endpointUrl = smpResult.getEndpointUrl();
            logger.info("🌐 MLS SMP lookup successful - Endpoint: {}", endpointUrl);

            // Update message with endpoint URL
            message.setEndpointUrl(endpointUrl);

            // Step 4: Determine MLS status based on delivery result (default to AB for now)
            MLSMessageSenderService.MLSStatus mlsStatus = MLSMessageSenderService.MLSStatus.AB;
            String statusReason = determineMlsStatusReason(message, mlsStatus);

            // Step 5: Check if MLS should be sent based on configuration and MLS_TYPE
            boolean shouldSendMLS = mlsConfigurationService.shouldSendMLS(
                message.getOriginalSbdhBusinessScope(), mlsStatus);

            if (!shouldSendMLS) {
                logger.info("📋 MLS not sent for message {} - disabled by configuration or MLS_TYPE",
                           message.getMessageId());
                messageQueueService.markMessageAsCompleted(message.getId(), "Processing completed - MLS not sent per configuration");
                return;
            }

            // Step 6: Send MLS message with appropriate status
            MLSMessageSenderService.MLSMessageResult mlsResult = mlsMessageSenderService.sendMLSMessage(
                message, endpointUrl, mlsStatus, statusReason);

            if (mlsResult.isSuccess()) {
                // Mark as completed
                messageQueueService.markMessageAsCompleted(message.getId(), mlsResult.getMlsMessageId());
                logger.info("✅ Message {} processed successfully with MLS ID: {} and status: {}",
                           message.getMessageId(), mlsResult.getMlsMessageId(), mlsStatus);
            } else {
                // Mark for retry
                messageQueueService.markMessageForRetry(message.getId(), "MLS send failed: " + mlsResult.getErrorMessage());
                logger.error("❌ MLS send failed for message {}: {}", message.getMessageId(), mlsResult.getErrorMessage());
            }

        } catch (Exception e) {
            logger.error("❌ Error in MLS message processing for {}: {}", message.getMessageId(), e.getMessage(), e);
            messageQueueService.markMessageForRetry(message.getId(), "MLS processing error: " + e.getMessage());
            throw e; // Re-throw to be handled by main processMessage method
        }
    }

    /**
     * Process a specific message by ID (for manual processing)
     */
    public ProcessingResult processMessageById(Long messageId) {
        try {
            logger.info("🔄 Manual processing requested for message ID: {}", messageId);

            // Get message from queue
            List<MessageProcessingQueue> messages = messageQueueService.getMessagesReadyForProcessing();
            MessageProcessingQueue message = messages.stream()
                    .filter(m -> m.getId().equals(messageId))
                    .findFirst()
                    .orElse(null);

            if (message == null) {
                logger.warn("⚠️ Message not found or not ready for processing: {}", messageId);
                return ProcessingResult.failure("Message not found or not ready for processing");
            }

            // Process the message
            processMessage(message);

            return ProcessingResult.success("Message processing started");

        } catch (Exception e) {
            logger.error("❌ Error in manual message processing: {}", e.getMessage(), e);
            return ProcessingResult.failure("Error: " + e.getMessage());
        }
    }

    /**
     * Get processing statistics
     */
    public ProcessingStatistics getProcessingStatistics() {
        try {
            MessageQueueService.QueueStatistics queueStats = messageQueueService.getQueueStatistics();
            
            ProcessingStatistics stats = new ProcessingStatistics();
            stats.setTotalMessages(queueStats.getTotal());
            stats.setReadyToSend(queueStats.getReadyToSend());
            stats.setProcessing(queueStats.getProcessing());
            stats.setRetry(queueStats.getRetry());
            stats.setCompleted(queueStats.getCompleted());
            stats.setError(queueStats.getError());
            stats.setProcessingEnabled(processingEnabled);
            
            return stats;
            
        } catch (Exception e) {
            logger.error("❌ Error getting processing statistics: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to get processing statistics: " + e.getMessage(), e);
        }
    }

    /**
     * Enable/disable message processing
     */
    public void setProcessingEnabled(boolean enabled) {
        this.processingEnabled = enabled;
        logger.info("📋 Message processing {}", enabled ? "enabled" : "disabled");
    }

    /**
     * Send rejection MLS message with Schematron validation errors
     */
    private void sendRejectionMLSWithSchematronErrors(MessageProcessingQueue message, List<String> validationErrors) {
        try {
            logger.info("📤 Checking if rejection MLS should be sent for Schematron validation errors: {}", message.getMessageId());

            // Check if MLS should be sent based on configuration and MLS_TYPE
            boolean shouldSendMLS = mlsConfigurationService.shouldSendMLS(
                message.getOriginalSbdhBusinessScope(), MLSMessageSenderService.MLSStatus.RE);

            if (!shouldSendMLS) {
                logger.info("📋 Rejection MLS not sent for message {} - disabled by configuration or MLS_TYPE",
                           message.getMessageId());
                messageQueueService.markMessageAsCompleted(message.getId(), "Validation failed - MLS not sent per configuration");
                return;
            }

            logger.info("📤 Sending rejection MLS for Schematron validation errors: {}", message.getMessageId());

            // Perform SMP lookup for C2 (original sender)
            SMPLookupService.SMPLookupResult smpResult = smpLookupService.performMLSSMPLookup(message.getReceiverParticipantId(),message.getMessageId());

            if (!smpResult.isSuccess()) {
                logger.error("❌ Cannot send rejection MLS - SMP lookup failed: {}", smpResult.getErrorMessage());
                messageQueueService.markMessageForRetry(message.getId(), "Rejection MLS SMP lookup failed: " + smpResult.getErrorMessage());
                return;
            }

            // Build rejection reason from Schematron errors
            String rejectionReason = "Schematron validation failed: " + String.join("; ", validationErrors);

            // Send rejection MLS with Schematron errors
            MLSMessageSenderService.MLSMessageResult mlsResult = mlsMessageSenderService.sendMLSMessageWithSchematronErrors(
                message, smpResult.getEndpointUrl(), MLSMessageSenderService.MLSStatus.RE, rejectionReason, validationErrors);

            if (mlsResult.isSuccess()) {
                messageQueueService.markMessageAsCompleted(message.getId(), mlsResult.getMlsMessageId());
                logger.info("✅ Rejection MLS sent successfully for message: {}", message.getMessageId());
            } else {
                messageQueueService.markMessageForRetry(message.getId(), "Rejection MLS send failed: " + mlsResult.getErrorMessage());
                logger.error("❌ Rejection MLS send failed: {}", mlsResult.getErrorMessage());
            }

        } catch (Exception e) {
            logger.error("❌ Error sending rejection MLS: {}", e.getMessage(), e);
            messageQueueService.markMessageForRetry(message.getId(), "Rejection MLS error: " + e.getMessage());
        }
    }

    /**
     * Send rejection MLS message
     */
    private void sendRejectionMLS(MessageProcessingQueue message, MLSValidationService.ValidationResult validationResult) {
        try {
            logger.info("📤 Checking if rejection MLS should be sent for message: {}", message.getMessageId());

            // Check if MLS should be sent based on configuration and MLS_TYPE
            boolean shouldSendMLS = mlsConfigurationService.shouldSendMLS(
                message.getOriginalSbdhBusinessScope(), validationResult.getStatus());

            if (!shouldSendMLS) {
                logger.info("📋 Rejection MLS not sent for message {} - disabled by configuration or MLS_TYPE",
                           message.getMessageId());
                messageQueueService.markMessageAsCompleted(message.getId(), "Validation failed - MLS not sent per configuration");
                return;
            }

            logger.info("📤 Sending rejection MLS for message: {}", message.getMessageId());

            // Perform SMP lookup for C2 (original sender)
            SMPLookupService.SMPLookupResult smpResult = smpLookupService.performMLSSMPLookup(message.getReceiverParticipantId(),message.getMessageId());

            if (!smpResult.isSuccess()) {
                logger.error("❌ Cannot send rejection MLS - SMP lookup failed: {}", smpResult.getErrorMessage());
                messageQueueService.markMessageForRetry(message.getId(), "Rejection MLS SMP lookup failed: " + smpResult.getErrorMessage());
                return;
            }

            // Send rejection MLS
            MLSMessageSenderService.MLSMessageResult mlsResult = mlsMessageSenderService.sendMLSMessage(
                message, smpResult.getEndpointUrl(), validationResult.getStatus(), validationResult.getStatusReason());

            if (mlsResult.isSuccess()) {
                messageQueueService.markMessageAsCompleted(message.getId(), mlsResult.getMlsMessageId());
                logger.info("✅ Rejection MLS sent successfully for message: {}", message.getMessageId());
            } else {
                messageQueueService.markMessageForRetry(message.getId(), "Rejection MLS send failed: " + mlsResult.getErrorMessage());
                logger.error("❌ Rejection MLS send failed: {}", mlsResult.getErrorMessage());
            }

        } catch (Exception e) {
            logger.error("❌ Error sending rejection MLS: {}", e.getMessage(), e);
            messageQueueService.markMessageForRetry(message.getId(), "Rejection MLS error: " + e.getMessage());
        }
    }

    /**
     * Simulate delivery to C4 and determine MLS status
     */
    private MLSMessageSenderService.MLSStatus simulateDeliveryToC4(MessageProcessingQueue message) {
        try {
            logger.debug("📦 Simulating delivery to C4 for message: {}", message.getMessageId());

            // In a real implementation, this would:
            // 1. Deliver the message to C4 via their preferred method (API, file transfer, etc.)
            // 2. Wait for confirmation if available
            // 3. Return appropriate status based on delivery result

            // For simulation purposes:
            // - 80% chance of successful delivery with confirmation (AP)
            // - 15% chance of successful delivery without confirmation (AB)
            // - 5% chance of delivery failure (would trigger retry, not MLS)

            double random = Math.random();
            if (random < 0.05) {
                // Simulate delivery failure - this would not send MLS but retry
                throw new RuntimeException("Simulated delivery failure to C4");
            } else if (random < 0.20) {
                // Delivery without confirmation
                logger.info("📦 Simulated delivery to C4 without confirmation");
                return MLSMessageSenderService.MLSStatus.AB;
            } else {
                // Delivery with confirmation
                logger.info("📦 Simulated delivery to C4 with confirmation");
                return MLSMessageSenderService.MLSStatus.AP;
            }

        } catch (Exception e) {
            logger.error("❌ Delivery to C4 failed: {}", e.getMessage());
            throw e; // Re-throw to trigger retry logic
        }
    }

    /**
     * Determine MLS status reason based on message and status
     */
    private String determineMlsStatusReason(MessageProcessingQueue message, MLSMessageSenderService.MLSStatus status) {
        switch (status) {
            case AP:
                return "Message delivered successfully with confirmation";
            case AB:
                return "Message delivered successfully without confirmation";
            case RE:
                return "Message rejected due to validation errors";
            default:
                return status.getDescription();
        }
    }

    /**
     * Result class for processing operations
     */
    public static class ProcessingResult {
        private boolean success;
        private String message;

        public static ProcessingResult success(String message) {
            ProcessingResult result = new ProcessingResult();
            result.success = true;
            result.message = message;
            return result;
        }

        public static ProcessingResult failure(String message) {
            ProcessingResult result = new ProcessingResult();
            result.success = false;
            result.message = message;
            return result;
        }

        // Getters
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
    }

    /**
     * Statistics class for processing information
     */
    public static class ProcessingStatistics {
        private long totalMessages;
        private long readyToSend;
        private long processing;
        private long retry;
        private long completed;
        private long error;
        private boolean processingEnabled;

        // Getters and setters
        public long getTotalMessages() { return totalMessages; }
        public void setTotalMessages(long totalMessages) { this.totalMessages = totalMessages; }
        
        public long getReadyToSend() { return readyToSend; }
        public void setReadyToSend(long readyToSend) { this.readyToSend = readyToSend; }
        
        public long getProcessing() { return processing; }
        public void setProcessing(long processing) { this.processing = processing; }
        
        public long getRetry() { return retry; }
        public void setRetry(long retry) { this.retry = retry; }
        
        public long getCompleted() { return completed; }
        public void setCompleted(long completed) { this.completed = completed; }
        
        public long getError() { return error; }
        public void setError(long error) { this.error = error; }
        
        public boolean isProcessingEnabled() { return processingEnabled; }
        public void setProcessingEnabled(boolean processingEnabled) { this.processingEnabled = processingEnabled; }
    }
}
