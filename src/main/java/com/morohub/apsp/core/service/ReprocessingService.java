package com.morohub.apsp.core.service;

import com.morohub.apsp.core.entity.MessageProcessingQueue;
import com.morohub.apsp.core.repository.MessageProcessingQueueRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Service for reprocessing messages using the same logic as original controllers
 * This service calls the exact same methods that the controllers call
 */
@Service
@Transactional
public class ReprocessingService {

    private static final Logger logger = LoggerFactory.getLogger(ReprocessingService.class);

    @Autowired
    private MessageProcessingQueueRepository messageQueueRepository;

    @Autowired
    private MessageQueueService messageQueueService;

    // Original service dependencies - same as controllers use
    @Autowired
    private AS4ConversionService as4ConversionService;

    @Autowired
    private SchematronValidationService schematronValidationService;

    @Autowired
    private InvoiceService invoiceService;

    @Autowired
    private CountryConfigurationService countryConfigurationService;

    /**
     * Reprocess a message using the exact same logic as the original controller
     */
    public boolean reprocessMessage(MessageProcessingQueue message) {
        try {
            logger.info("🔄 Reprocessing {} message: {}", message.getFlowType(), message.getMessageId());

            // Mark as processing
            message.setStatus(MessageProcessingQueue.ProcessingStatus.PROCESSING);
            messageQueueRepository.save(message);

            boolean success = false;

            switch (message.getFlowType()) {
                case FORWARD_FLOW:
                    success = reprocessForwardFlow(message);
                    break;
                
                case REVERSE_FLOW:
                    success = reprocessReverseFlow(message);
                    break;
                
                case XML_VALIDATION:
                    success = reprocessXmlValidation(message);
                    break;
                
                case PEPPOL_SBD_INVOICE:
                    success = reprocessPeppolSbdInvoice(message);
                    break;
                
                case MLS_MESSAGE:
                    logger.warn("⚠️ MLS messages should be processed by MessageProcessingService, not ReprocessingService");
                    return false;
                
                default:
                    logger.warn("⚠️ Unknown flow type for reprocessing: {}", message.getFlowType());
                    return false;
            }

            // Update status based on result
            if (success) {
                message.setStatus(MessageProcessingQueue.ProcessingStatus.COMPLETED);
                logger.info("✅ Reprocessing successful for message: {}", message.getMessageId());
            } else {
                message.setStatus(MessageProcessingQueue.ProcessingStatus.RETRY);
                logger.warn("❌ Reprocessing failed for message: {}", message.getMessageId());
            }
            
            messageQueueRepository.save(message);
            return success;

        } catch (Exception e) {
            logger.error("❌ Exception during reprocessing for message {}: {}", 
                        message.getMessageId(), e.getMessage(), e);
            
            message.setStatus(MessageProcessingQueue.ProcessingStatus.ERROR);
            messageQueueRepository.save(message);
            return false;
        }
    }

    /**
     * Reprocess forward flow - same logic as XmlValidationController.validateAndConvertToAs4
     */
    private boolean reprocessForwardFlow(MessageProcessingQueue message) {
        try {
            logger.info("🔄 Reprocessing forward flow for message: {}", message.getMessageId());
            
            String xml = message.getUblXml();
            String countryCode = message.getCountryCode();
            String documentType = message.getDocumentType();

            // Step 1: Perform country-specific Schematron validation (same as controller)
            List<String> schematronFiles = countryConfigurationService.getSchematronFiles(countryCode, documentType);
            List<String> validationErrors = schematronValidationService.validateXmlWithMultipleSchematrons(xml, schematronFiles);

            if (!validationErrors.isEmpty()) {
                logger.warn("❌ Schematron validation failed during reprocessing for message: {}", message.getMessageId());
                return false;
            }

            // Step 2: Convert to AS4 message (same as controller)
            AS4ConversionService.AS4ConversionResult conversionResult = as4ConversionService.convertAndSend(xml);

            if (conversionResult.isSuccess()) {
                logger.info("✅ Forward flow reprocessing successful for message: {}", message.getMessageId());
                return true;
            } else {
                logger.error("❌ AS4 conversion failed during reprocessing for message: {}: {}", 
                           message.getMessageId(), conversionResult.getErrorMessage());
                return false;
            }

        } catch (Exception e) {
            logger.error("❌ Exception during forward flow reprocessing for message {}: {}", 
                        message.getMessageId(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * Reprocess reverse flow - same logic as ReverseFlowController
     */
    private boolean reprocessReverseFlow(MessageProcessingQueue message) {
        try {
            logger.info("🔄 Reprocessing reverse flow for message: {}", message.getMessageId());
            
            // Use the same logic as ReverseFlowController
            String result = invoiceService.processIncomingMessageWithCountryConfig(
                message.getUblXml(), message.getCountryCode(), message.getDocumentType());
            
            if (result != null && !result.isEmpty()) {
                logger.info("✅ Reverse flow reprocessing successful for message: {}", message.getMessageId());
                return true;
            } else {
                logger.warn("❌ Reverse flow reprocessing returned empty result for message: {}", message.getMessageId());
                return false;
            }

        } catch (Exception e) {
            logger.error("❌ Exception during reverse flow reprocessing for message {}: {}", 
                        message.getMessageId(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * Reprocess XML validation - same logic as XmlValidationController.validateXml
     */
    private boolean reprocessXmlValidation(MessageProcessingQueue message) {
        try {
            logger.info("🔄 Reprocessing XML validation for message: {}", message.getMessageId());
            
            String xml = message.getUblXml();

            // Use the same validation logic as XmlValidationController
            List<String> validationErrors = schematronValidationService.validateXml(xml, "PEPPOL-EN16931-UBL.sch");

            if (validationErrors.isEmpty()) {
                logger.info("✅ XML validation reprocessing successful for message: {}", message.getMessageId());
                return true;
            } else {
                logger.warn("❌ XML validation failed during reprocessing for message: {} with {} errors", 
                           message.getMessageId(), validationErrors.size());
                return false;
            }

        } catch (Exception e) {
            logger.error("❌ Exception during XML validation reprocessing for message {}: {}", 
                        message.getMessageId(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * Reprocess Peppol SBD Invoice - same logic as PeppolSbdInvoiceController.generateInvoice
     */
    private boolean reprocessPeppolSbdInvoice(MessageProcessingQueue message) {
        try {
            logger.info("🔄 Reprocessing Peppol SBD Invoice for message: {}", message.getMessageId());
            
            String countryCode = message.getCountryCode();
            String documentType = message.getDocumentType();

            // Check if country/document type is supported (same as controller)
            if (!countryConfigurationService.isSupported(countryCode, documentType)) {
                logger.warn("❌ Unsupported country/document type during reprocessing: {}/{}", countryCode, documentType);
                return false;
            }

            // Generate invoice using the same logic as controller
            String result = invoiceService.generateInvoiceWithConfig(
                message.getUblXml(), countryCode, documentType);
            
            if (result != null && !result.isEmpty()) {
                logger.info("✅ Peppol SBD Invoice reprocessing successful for message: {}", message.getMessageId());
                return true;
            } else {
                logger.warn("❌ Peppol SBD Invoice generation returned empty result for message: {}", message.getMessageId());
                return false;
            }

        } catch (Exception e) {
            logger.error("❌ Exception during Peppol SBD Invoice reprocessing for message {}: {}", 
                        message.getMessageId(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * Get messages that can be reprocessed (status = RETRY)
     */
    public List<MessageProcessingQueue> getMessagesForReprocessing() {
        return messageQueueRepository.findByStatusOrderByCreatedDateAsc(
            MessageProcessingQueue.ProcessingStatus.RETRY);
    }

    /**
     * Get messages by flow type for reprocessing
     */
    public List<MessageProcessingQueue> getMessagesForReprocessing(MessageProcessingQueue.FlowType flowType) {
        return messageQueueRepository.findByFlowTypeAndStatusOrderByCreatedDateAsc(
            flowType, MessageProcessingQueue.ProcessingStatus.RETRY);
    }
}
