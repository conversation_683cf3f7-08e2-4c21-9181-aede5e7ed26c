package com.morohub.apsp.common.exception;

import com.morohub.apsp.core.entity.ErrorMasterData;

/**
 * Exception for technical connectivity errors
 * These errors are typically retryable
 */
public class ConnectivityException extends AS4Exception {

    private final String endpoint;
    private final int httpStatusCode;
    private final long responseTime;

    public ConnectivityException(String errorCode, String message, String endpoint) {
        super(errorCode, message, ErrorMasterData.ErrorType.TECHNICAL_CONNECTIVITY_ERROR, 
              ErrorMasterData.ErrorSeverity.HIGH, true);
        this.endpoint = endpoint;
        this.httpStatusCode = 0;
        this.responseTime = 0;
    }

    public ConnectivityException(String errorCode, String message, String endpoint, Throwable cause) {
        super(errorCode, message, ErrorMasterData.ErrorType.TECHNICAL_CONNECTIVITY_ERROR, 
              ErrorMasterData.ErrorSeverity.HIGH, true, null, null, cause);
        this.endpoint = endpoint;
        this.httpStatusCode = 0;
        this.responseTime = 0;
    }

    public ConnectivityException(String errorCode, String message, String endpoint, 
                               int httpStatusCode, String messageId, String requestId) {
        super(errorCode, message, ErrorMasterData.ErrorType.TECHNICAL_CONNECTIVITY_ERROR, 
              ErrorMasterData.ErrorSeverity.HIGH, true, messageId, requestId);
        this.endpoint = endpoint;
        this.httpStatusCode = httpStatusCode;
        this.responseTime = 0;
    }

    public ConnectivityException(String errorCode, String message, String endpoint, 
                               int httpStatusCode, long responseTime, String messageId, String requestId) {
        super(errorCode, message, ErrorMasterData.ErrorType.TECHNICAL_CONNECTIVITY_ERROR, 
              ErrorMasterData.ErrorSeverity.HIGH, true, messageId, requestId);
        this.endpoint = endpoint;
        this.httpStatusCode = httpStatusCode;
        this.responseTime = responseTime;
    }

    public ConnectivityException(String errorCode, String message, String endpoint, 
                               int httpStatusCode, long responseTime, String messageId, 
                               String requestId, Throwable cause) {
        super(errorCode, message, ErrorMasterData.ErrorType.TECHNICAL_CONNECTIVITY_ERROR, 
              ErrorMasterData.ErrorSeverity.HIGH, true, messageId, requestId, cause);
        this.endpoint = endpoint;
        this.httpStatusCode = httpStatusCode;
        this.responseTime = responseTime;
    }

    // Timeout specific constructor
    public static ConnectivityException timeout(String errorCode, String message, String endpoint, 
                                              long responseTime, String messageId, String requestId) {
        return new ConnectivityException(errorCode, message, endpoint, 408, responseTime, messageId, requestId);
    }

    // Connection refused constructor
    public static ConnectivityException connectionRefused(String errorCode, String message, String endpoint, 
                                                        String messageId, String requestId, Throwable cause) {
        return new ConnectivityException(errorCode, message, endpoint, 0, 0, messageId, requestId, cause);
    }

    // DNS resolution failure constructor
    public static ConnectivityException dnsFailure(String errorCode, String message, String endpoint, 
                                                  String messageId, String requestId, Throwable cause) {
        return new ConnectivityException(errorCode, message, endpoint, 0, 0, messageId, requestId, cause);
    }

    // SSL/TLS error constructor
    public static ConnectivityException sslError(String errorCode, String message, String endpoint, 
                                                String messageId, String requestId, Throwable cause) {
        ConnectivityException ex = new ConnectivityException(errorCode, message, endpoint, 0, 0, messageId, requestId, cause);
        // SSL errors might not be retryable depending on the specific error
        return ex;
    }

    // HTTP error constructor
    public static ConnectivityException httpError(String errorCode, String message, String endpoint, 
                                                 int httpStatusCode, String messageId, String requestId) {
        return new ConnectivityException(errorCode, message, endpoint, httpStatusCode, 0, messageId, requestId);
    }

    public String getEndpoint() {
        return endpoint;
    }

    public int getHttpStatusCode() {
        return httpStatusCode;
    }

    public long getResponseTime() {
        return responseTime;
    }

    /**
     * Determine if this connectivity error is retryable based on the error type
     */
    @Override
    public boolean isRetryable() {
        // Generally connectivity errors are retryable, but some HTTP status codes are not
        if (httpStatusCode > 0) {
            // 4xx client errors are generally not retryable (except 408, 429)
            if (httpStatusCode >= 400 && httpStatusCode < 500) {
                return httpStatusCode == 408 || httpStatusCode == 429;
            }
            // 5xx server errors are generally retryable
            if (httpStatusCode >= 500) {
                return true;
            }
        }
        // Network-level errors (connection refused, timeout, DNS) are retryable
        return super.isRetryable();
    }

    @Override
    public String toString() {
        return String.format("ConnectivityException{errorCode='%s', endpoint='%s', httpStatusCode=%d, responseTime=%d, message='%s'}", 
                           getErrorCode(), endpoint, httpStatusCode, responseTime, getMessage());
    }
}
