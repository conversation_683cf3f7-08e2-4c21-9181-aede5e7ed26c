package com.morohub.apsp.core.service;

import com.morohub.apsp.common.dto.ErrorResponse;
import com.morohub.apsp.common.exception.AS4Exception;
import com.morohub.apsp.common.exception.ValidationException;
import com.morohub.apsp.core.entity.ErrorLog;
import com.morohub.apsp.core.entity.ErrorMasterData;
import com.morohub.apsp.core.repository.ErrorLogRepository;
import com.morohub.apsp.core.repository.ErrorMasterDataRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Unit tests for ErrorManagementService
 */
@ExtendWith(MockitoExtension.class)
class ErrorManagementServiceTest {

    @Mock
    private ErrorMasterDataRepository errorMasterDataRepository;

    @Mock
    private ErrorLogRepository errorLogRepository;

    @InjectMocks
    private ErrorManagementService errorManagementService;

    private ErrorMasterData testErrorMasterData;
    private AS4Exception testAS4Exception;

    @BeforeEach
    void setUp() {
        // Create test error master data
        testErrorMasterData = new ErrorMasterData();
        testErrorMasterData.setId(1L);
        testErrorMasterData.setErrorCode("TEST_001");
        testErrorMasterData.setHttpStatusCode(400);
        testErrorMasterData.setErrorType(ErrorMasterData.ErrorType.SCHEMA_VALIDATION_FAILURE);
        testErrorMasterData.setErrorDescription("Test validation error");
        testErrorMasterData.setRetryRequired(false);
        testErrorMasterData.setSeverity(ErrorMasterData.ErrorSeverity.HIGH);
        testErrorMasterData.setActive(true);

        // Create test AS4Exception
        testAS4Exception = new AS4Exception("TEST_001", "Test error message",
                ErrorMasterData.ErrorType.SCHEMA_VALIDATION_FAILURE,
                ErrorMasterData.ErrorSeverity.HIGH, false,
                "MSG_123", "REQ_456");
    }

    @Test
    void testLogError_WithExistingErrorCode() {
        // Arrange
        when(errorMasterDataRepository.findByErrorCode("TEST_001"))
                .thenReturn(Optional.of(testErrorMasterData));
        when(errorLogRepository.save(any(ErrorLog.class)))
                .thenAnswer(invocation -> {
                    ErrorLog errorLog = invocation.getArgument(0);
                    errorLog.setId(1L);
                    return errorLog;
                });

        // Act
        ErrorLog result = errorManagementService.logError("TEST_001", "MSG_123", "REQ_456",
                "Test error message", "Stack trace", null);

        // Assert
        assertNotNull(result);
        assertEquals("MSG_123", result.getMessageId());
        assertEquals("REQ_456", result.getRequestId());
        assertEquals("Test error message", result.getErrorMessage());
        assertEquals(testErrorMasterData, result.getErrorMasterData());

        verify(errorMasterDataRepository).findByErrorCode("TEST_001");
        verify(errorLogRepository).save(any(ErrorLog.class));
    }

    @Test
    void testLogError_WithUnknownErrorCode() {
        // Arrange
        when(errorMasterDataRepository.findByErrorCode("UNKNOWN_001"))
                .thenReturn(Optional.empty());
        when(errorMasterDataRepository.save(any(ErrorMasterData.class)))
                .thenAnswer(invocation -> {
                    ErrorMasterData errorMaster = invocation.getArgument(0);
                    errorMaster.setId(2L);
                    return errorMaster;
                });
        when(errorLogRepository.save(any(ErrorLog.class)))
                .thenAnswer(invocation -> {
                    ErrorLog errorLog = invocation.getArgument(0);
                    errorLog.setId(2L);
                    return errorLog;
                });

        // Act
        ErrorLog result = errorManagementService.logError("UNKNOWN_001", "MSG_123", "REQ_456",
                "Unknown error message", "Stack trace", null);

        // Assert
        assertNotNull(result);
        assertEquals("MSG_123", result.getMessageId());
        assertEquals("REQ_456", result.getRequestId());
        assertEquals("Unknown error message", result.getErrorMessage());

        verify(errorMasterDataRepository).findByErrorCode("UNKNOWN_001");
        verify(errorMasterDataRepository).save(any(ErrorMasterData.class));
        verify(errorLogRepository).save(any(ErrorLog.class));
    }

    @Test
    void testLogError_WithAS4Exception() {
        // Arrange
        when(errorMasterDataRepository.findByErrorCode("TEST_001"))
                .thenReturn(Optional.of(testErrorMasterData));
        when(errorLogRepository.save(any(ErrorLog.class)))
                .thenAnswer(invocation -> {
                    ErrorLog errorLog = invocation.getArgument(0);
                    errorLog.setId(1L);
                    return errorLog;
                });

        // Act
        ErrorLog result = errorManagementService.logError(testAS4Exception, null);

        // Assert
        assertNotNull(result);
        assertEquals("MSG_123", result.getMessageId());
        assertEquals("REQ_456", result.getRequestId());
        assertEquals("Test error message", result.getErrorMessage());

        verify(errorMasterDataRepository).findByErrorCode("TEST_001");
        verify(errorLogRepository).save(any(ErrorLog.class));
    }

    @Test
    void testGenerateErrorResponse_WithExistingErrorCode() {
        // Arrange
        when(errorMasterDataRepository.findByErrorCode("TEST_001"))
                .thenReturn(Optional.of(testErrorMasterData));

        // Act
        ErrorResponse result = errorManagementService.generateErrorResponse("TEST_001", "MSG_123", "REQ_456");

        // Assert
        assertNotNull(result);
        assertEquals("TEST_001", result.getErrorCode());
        assertEquals("Test validation error", result.getErrorDescription());
        assertEquals("MSG_123", result.getMessageId());
        assertEquals("REQ_456", result.getRequestId());
        assertEquals("SCHEMA_VALIDATION_FAILURE", result.getErrorType());
        assertEquals("HIGH", result.getSeverity());
        assertFalse(result.isRetryable());

        verify(errorMasterDataRepository).findByErrorCode("TEST_001");
    }

    @Test
    void testGenerateErrorResponse_WithUnknownErrorCode() {
        // Arrange
        when(errorMasterDataRepository.findByErrorCode("UNKNOWN_001"))
                .thenReturn(Optional.empty());

        // Act
        ErrorResponse result = errorManagementService.generateErrorResponse("UNKNOWN_001", "MSG_123", "REQ_456");

        // Assert
        assertNotNull(result);
        assertEquals("UNKNOWN_001", result.getErrorCode());
        assertEquals("Unknown error occurred", result.getErrorDescription());
        assertEquals("MSG_123", result.getMessageId());
        assertEquals("REQ_456", result.getRequestId());

        verify(errorMasterDataRepository).findByErrorCode("UNKNOWN_001");
    }

    @Test
    void testGenerateErrorResponse_FromAS4Exception() {
        // Arrange
        when(errorMasterDataRepository.findByErrorCode("TEST_001"))
                .thenReturn(Optional.of(testErrorMasterData));

        // Act
        ErrorResponse result = errorManagementService.generateErrorResponse(testAS4Exception);

        // Assert
        assertNotNull(result);
        assertEquals("TEST_001", result.getErrorCode());
        assertEquals("Test error message", result.getErrorDescription()); // Should use exception message
        assertEquals("MSG_123", result.getMessageId());
        assertEquals("REQ_456", result.getRequestId());
        assertEquals("SCHEMA_VALIDATION_FAILURE", result.getErrorType());
        assertEquals("HIGH", result.getSeverity());
        assertFalse(result.isRetryable());

        verify(errorMasterDataRepository).findByErrorCode("TEST_001");
    }

    @Test
    void testGetErrorLogsForMessage() {
        // Arrange
        ErrorLog errorLog1 = new ErrorLog();
        errorLog1.setId(1L);
        errorLog1.setMessageId("MSG_123");

        ErrorLog errorLog2 = new ErrorLog();
        errorLog2.setId(2L);
        errorLog2.setMessageId("MSG_123");

        List<ErrorLog> expectedLogs = Arrays.asList(errorLog1, errorLog2);

        when(errorLogRepository.findByMessageId("MSG_123"))
                .thenReturn(expectedLogs);

        // Act
        List<ErrorLog> result = errorManagementService.getErrorLogsForMessage("MSG_123");

        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(expectedLogs, result);

        verify(errorLogRepository).findByMessageId("MSG_123");
    }

    @Test
    void testMarkErrorAsResolved() {
        // Arrange
        ErrorLog errorLog = new ErrorLog();
        errorLog.setId(1L);
        errorLog.setResolved(false);

        when(errorLogRepository.findById(1L))
                .thenReturn(Optional.of(errorLog));
        when(errorLogRepository.save(any(ErrorLog.class)))
                .thenReturn(errorLog);

        // Act
        errorManagementService.markErrorAsResolved(1L, "admin", "Fixed manually");

        // Assert
        assertTrue(errorLog.getResolved());
        assertEquals("admin", errorLog.getResolvedBy());
        assertEquals("Fixed manually", errorLog.getResolutionNotes());
        assertNotNull(errorLog.getResolvedDate());

        verify(errorLogRepository).findById(1L);
        verify(errorLogRepository).save(errorLog);
    }
}
