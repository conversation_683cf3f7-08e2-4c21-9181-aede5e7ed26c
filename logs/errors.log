2025-07-02 10:37:53.452 [http-nio-8081-exec-1] ERROR c.m.a.a.c.PeppolSbdInvoiceController - Failed to log invoice generation result
org.springframework.web.client.HttpServerErrorException$InternalServerError: 500 : "Error: JSON parse error: Cannot deserialize value of type `java.time.LocalDate` from String "UNKNOWN": Failed to deserialize java.time.LocalDate: (java.time.format.DateTimeParseException) Text 'UNKNOWN' could not be parsed at index 0"
	at org.springframework.web.client.HttpServerErrorException.create(HttpServerErrorException.java:102)
	at org.springframework.web.client.DefaultResponseErrorHandler.handleError(DefaultResponseErrorHandler.java:186)
	at org.springframework.web.client.DefaultResponseErrorHandler.handleError(DefaultResponseErrorHandler.java:137)
	at org.springframework.web.client.ResponseErrorHandler.handleError(ResponseErrorHandler.java:63)
	at org.springframework.web.client.RestTemplate.handleResponse(RestTemplate.java:932)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:881)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:781)
	at org.springframework.web.client.RestTemplate.postForEntity(RestTemplate.java:529)
	at com.morohub.apsp.api.controller.PeppolSbdInvoiceController.sendPeppolSbdInvoice(PeppolSbdInvoiceController.java:183)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:254)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:182)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 10:37:53.752 [http-nio-8081-exec-1] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed: ConnectivityException{errorCode='CONN_005', endpoint='http://localhost:8090/api/secure/decrypt-invoice/0235:1234567891', httpStatusCode=0, responseTime=0, message='Failed to connect to decryption service: 500 : "Error: Authorization failed due to wrong key"'}] with root cause
org.springframework.web.client.HttpServerErrorException$InternalServerError: 500 : "Error: Authorization failed due to wrong key"
	at org.springframework.web.client.HttpServerErrorException.create(HttpServerErrorException.java:102)
	at org.springframework.web.client.DefaultResponseErrorHandler.handleError(DefaultResponseErrorHandler.java:186)
	at org.springframework.web.client.DefaultResponseErrorHandler.handleError(DefaultResponseErrorHandler.java:137)
	at org.springframework.web.client.ResponseErrorHandler.handleError(ResponseErrorHandler.java:63)
	at org.springframework.web.client.RestTemplate.handleResponse(RestTemplate.java:932)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:881)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:781)
	at org.springframework.web.client.RestTemplate.postForEntity(RestTemplate.java:529)
	at com.morohub.apsp.api.controller.PeppolSbdInvoiceController.sendPeppolSbdInvoice(PeppolSbdInvoiceController.java:108)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:254)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:182)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:06:42.840 [http-nio-8081-exec-2] ERROR c.m.a.a.c.PeppolSbdInvoiceController - Failed to log invoice generation result
org.springframework.web.client.HttpServerErrorException$InternalServerError: 500 : "Error: JSON parse error: Cannot deserialize value of type `java.time.LocalDate` from String "UNKNOWN": Failed to deserialize java.time.LocalDate: (java.time.format.DateTimeParseException) Text 'UNKNOWN' could not be parsed at index 0"
	at org.springframework.web.client.HttpServerErrorException.create(HttpServerErrorException.java:102)
	at org.springframework.web.client.DefaultResponseErrorHandler.handleError(DefaultResponseErrorHandler.java:186)
	at org.springframework.web.client.DefaultResponseErrorHandler.handleError(DefaultResponseErrorHandler.java:137)
	at org.springframework.web.client.ResponseErrorHandler.handleError(ResponseErrorHandler.java:63)
	at org.springframework.web.client.RestTemplate.handleResponse(RestTemplate.java:932)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:881)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:781)
	at org.springframework.web.client.RestTemplate.postForEntity(RestTemplate.java:529)
	at com.morohub.apsp.api.controller.PeppolSbdInvoiceController.sendPeppolSbdInvoice(PeppolSbdInvoiceController.java:199)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:254)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:182)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:06:42.895 [http-nio-8081-exec-2] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed: ConnectivityException{errorCode='CONN_005', endpoint='http://localhost:8090/api/secure/decrypt-invoice/0235:1234567891', httpStatusCode=0, responseTime=0, message='Failed to connect to decryption service: 500 : "Error: Authorization failed due to wrong key"'}] with root cause
org.springframework.web.client.HttpServerErrorException$InternalServerError: 500 : "Error: Authorization failed due to wrong key"
	at org.springframework.web.client.HttpServerErrorException.create(HttpServerErrorException.java:102)
	at org.springframework.web.client.DefaultResponseErrorHandler.handleError(DefaultResponseErrorHandler.java:186)
	at org.springframework.web.client.DefaultResponseErrorHandler.handleError(DefaultResponseErrorHandler.java:137)
	at org.springframework.web.client.ResponseErrorHandler.handleError(ResponseErrorHandler.java:63)
	at org.springframework.web.client.RestTemplate.handleResponse(RestTemplate.java:932)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:881)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:781)
	at org.springframework.web.client.RestTemplate.postForEntity(RestTemplate.java:529)
	at com.morohub.apsp.api.controller.PeppolSbdInvoiceController.sendPeppolSbdInvoice(PeppolSbdInvoiceController.java:124)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:254)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:182)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:06:58.188 [pool-2-thread-1] ERROR c.m.a.c.service.XmlGeneratorService - Validation error generating invoice XML for DEFAULT/INVOICE: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:06:58.188 [pool-2-thread-1] ERROR c.m.apsp.core.service.InvoiceService - Validation error generating invoice for DEFAULT/INVOICE: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:06:58.188 [pool-2-thread-1] ERROR c.m.a.c.s.FlowSpecificRetryService - ❌ Exception during Peppol SBD Invoice retry for message MSG-7d35bcb6-ed03-491b-8ff0-889f13f90e98: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:07:28.175 [pool-2-thread-3] ERROR c.m.a.c.service.XmlGeneratorService - Validation error generating invoice XML for DEFAULT/INVOICE: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:07:28.175 [pool-2-thread-3] ERROR c.m.apsp.core.service.InvoiceService - Validation error generating invoice for DEFAULT/INVOICE: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:07:28.175 [pool-2-thread-3] ERROR c.m.a.c.s.FlowSpecificRetryService - ❌ Exception during Peppol SBD Invoice retry for message MSG-de3c612a-26f1-4f45-af20-83bc6ef3b710: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:07:28.175 [pool-2-thread-2] ERROR c.m.a.c.service.XmlGeneratorService - Validation error generating invoice XML for DEFAULT/INVOICE: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:07:28.175 [pool-2-thread-2] ERROR c.m.apsp.core.service.InvoiceService - Validation error generating invoice for DEFAULT/INVOICE: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:07:28.175 [pool-2-thread-2] ERROR c.m.a.c.s.FlowSpecificRetryService - ❌ Exception during Peppol SBD Invoice retry for message MSG-7d35bcb6-ed03-491b-8ff0-889f13f90e98: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:07:58.192 [pool-2-thread-4] ERROR c.m.a.c.service.XmlGeneratorService - Validation error generating invoice XML for DEFAULT/INVOICE: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:07:58.192 [pool-2-thread-4] ERROR c.m.apsp.core.service.InvoiceService - Validation error generating invoice for DEFAULT/INVOICE: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:07:58.192 [pool-2-thread-4] ERROR c.m.a.c.s.FlowSpecificRetryService - ❌ Exception during Peppol SBD Invoice retry for message MSG-7d35bcb6-ed03-491b-8ff0-889f13f90e98: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:07:58.192 [pool-2-thread-5] ERROR c.m.a.c.service.XmlGeneratorService - Validation error generating invoice XML for DEFAULT/INVOICE: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:07:58.192 [pool-2-thread-5] ERROR c.m.apsp.core.service.InvoiceService - Validation error generating invoice for DEFAULT/INVOICE: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:07:58.192 [pool-2-thread-5] ERROR c.m.a.c.s.FlowSpecificRetryService - ❌ Exception during Peppol SBD Invoice retry for message MSG-de3c612a-26f1-4f45-af20-83bc6ef3b710: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:08:28.203 [pool-2-thread-1] ERROR c.m.a.c.service.XmlGeneratorService - Validation error generating invoice XML for DEFAULT/INVOICE: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:08:28.203 [pool-2-thread-1] ERROR c.m.apsp.core.service.InvoiceService - Validation error generating invoice for DEFAULT/INVOICE: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:08:28.203 [pool-2-thread-1] ERROR c.m.a.c.s.FlowSpecificRetryService - ❌ Exception during Peppol SBD Invoice retry for message MSG-de3c612a-26f1-4f45-af20-83bc6ef3b710: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:09:28.552 [pool-2-thread-3] ERROR c.m.a.c.service.XmlGeneratorService - Validation error generating invoice XML for DEFAULT/INVOICE: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:09:28.552 [pool-2-thread-3] ERROR c.m.apsp.core.service.InvoiceService - Validation error generating invoice for DEFAULT/INVOICE: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:09:28.552 [pool-2-thread-3] ERROR c.m.a.c.s.FlowSpecificRetryService - ❌ Exception during Peppol SBD Invoice retry for message MSG-93a14006-9944-4cf2-b2c9-1fa50f81c0b7: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:09:58.578 [pool-2-thread-2] ERROR c.m.a.c.service.XmlGeneratorService - Validation error generating invoice XML for DEFAULT/INVOICE: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:09:58.579 [pool-2-thread-2] ERROR c.m.apsp.core.service.InvoiceService - Validation error generating invoice for DEFAULT/INVOICE: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:09:58.579 [pool-2-thread-2] ERROR c.m.a.c.s.FlowSpecificRetryService - ❌ Exception during Peppol SBD Invoice retry for message MSG-93a14006-9944-4cf2-b2c9-1fa50f81c0b7: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:10:28.583 [pool-2-thread-4] ERROR c.m.a.c.service.XmlGeneratorService - Validation error generating invoice XML for DEFAULT/INVOICE: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:10:28.583 [pool-2-thread-4] ERROR c.m.apsp.core.service.InvoiceService - Validation error generating invoice for DEFAULT/INVOICE: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:10:28.583 [pool-2-thread-4] ERROR c.m.a.c.s.FlowSpecificRetryService - ❌ Exception during Peppol SBD Invoice retry for message MSG-93a14006-9944-4cf2-b2c9-1fa50f81c0b7: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:28:18.638 [http-nio-8081-exec-3] ERROR c.m.a.config.GlobalExceptionHandler - ValidationException handled: 2 validation errors for 043fae0b-9d82-4c88-aa47-bf38f62f0af8
2025-07-02 11:28:48.556 [http-nio-8081-exec-5] ERROR c.m.a.config.GlobalExceptionHandler - ValidationException handled: 2 validation errors for 7afe70c8-7def-4a02-9cbb-27e705c9d5df
2025-07-02 11:29:08.905 [http-nio-8081-exec-8] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (19, DEFAULT, 2025-07-02 11:29:08.8966, INVOICE, null, null, null, 2025-07-02 11:29:08.8966, 3, MSG-2c1c6a47-b63f-4ba9-b6be-809222b0acee, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).
2025-07-02 11:29:08.937 [http-nio-8081-exec-8] ERROR c.m.a.c.service.MessageQueueService - ❌ Error adding message to queue: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (19, DEFAULT, 2025-07-02 11:29:08.8966, INVOICE, null, null, null, 2025-07-02 11:29:08.8966, 3, MSG-2c1c6a47-b63f-4ba9-b6be-809222b0acee, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
org.springframework.dao.DataIntegrityViolationException: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (19, DEFAULT, 2025-07-02 11:29:08.8966, INVOICE, null, null, null, 2025-07-02 11:29:08.8966, 3, MSG-2c1c6a47-b63f-4ba9-b6be-809222b0acee, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:290)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:550)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:335)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:152)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:164)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:249)
	at jdk.proxy2/jdk.proxy2.$Proxy133.save(Unknown Source)
	at com.morohub.apsp.core.service.MessageQueueService.addMessageToQueue(MessageQueueService.java:52)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.MessageQueueService$$SpringCGLIB$$0.addMessageToQueue(<generated>)
	at com.morohub.apsp.core.service.Phase4AS4ReceiverService.processIncomingAS4MessageContent(Phase4AS4ReceiverService.java:95)
	at com.morohub.apsp.config.Phase4ServletConfiguration$Phase4AS4Servlet.doPost(Phase4ServletConfiguration.java:101)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.hibernate.exception.ConstraintViolationException: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (19, DEFAULT, 2025-07-02 11:29:08.8966, INVOICE, null, null, null, 2025-07-02 11:29:08.8966, 3, MSG-2c1c6a47-b63f-4ba9-b6be-809222b0acee, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:97)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:283)
	at org.hibernate.id.insert.GetGeneratedKeysDelegate.performInsert(GetGeneratedKeysDelegate.java:107)
	at org.hibernate.engine.jdbc.mutation.internal.MutationExecutorPostInsertSingleTable.execute(MutationExecutorPostInsertSingleTable.java:100)
	at org.hibernate.persister.entity.mutation.InsertCoordinator.doStaticInserts(InsertCoordinator.java:171)
	at org.hibernate.persister.entity.mutation.InsertCoordinator.coordinateInsert(InsertCoordinator.java:112)
	at org.hibernate.persister.entity.AbstractEntityPersister.insert(AbstractEntityPersister.java:2860)
	at org.hibernate.action.internal.EntityIdentityInsertAction.execute(EntityIdentityInsertAction.java:81)
	at org.hibernate.engine.spi.ActionQueue.execute(ActionQueue.java:667)
	at org.hibernate.engine.spi.ActionQueue.addResolvedEntityInsertAction(ActionQueue.java:290)
	at org.hibernate.engine.spi.ActionQueue.addInsertAction(ActionQueue.java:271)
	at org.hibernate.engine.spi.ActionQueue.addAction(ActionQueue.java:321)
	at org.hibernate.event.internal.AbstractSaveEventListener.addInsertAction(AbstractSaveEventListener.java:386)
	at org.hibernate.event.internal.AbstractSaveEventListener.performSaveOrReplicate(AbstractSaveEventListener.java:300)
	at org.hibernate.event.internal.AbstractSaveEventListener.performSave(AbstractSaveEventListener.java:219)
	at org.hibernate.event.internal.AbstractSaveEventListener.saveWithGeneratedId(AbstractSaveEventListener.java:134)
	at org.hibernate.event.internal.DefaultPersistEventListener.entityIsTransient(DefaultPersistEventListener.java:175)
	at org.hibernate.event.internal.DefaultPersistEventListener.persist(DefaultPersistEventListener.java:93)
	at org.hibernate.event.internal.DefaultPersistEventListener.onPersist(DefaultPersistEventListener.java:77)
	at org.hibernate.event.internal.DefaultPersistEventListener.onPersist(DefaultPersistEventListener.java:54)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.firePersist(SessionImpl.java:766)
	at org.hibernate.internal.SessionImpl.persist(SessionImpl.java:750)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler.invoke(SharedEntityManagerCreator.java:311)
	at jdk.proxy2/jdk.proxy2.$Proxy123.persist(Unknown Source)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.save(SimpleJpaRepository.java:618)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:277)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:516)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:285)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:628)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:168)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:143)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	... 58 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (19, DEFAULT, 2025-07-02 11:29:08.8966, INVOICE, null, null, null, 2025-07-02 11:29:08.8966, 3, MSG-2c1c6a47-b63f-4ba9-b6be-809222b0acee, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2713)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2401)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:368)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:498)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:415)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:190)
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:152)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:280)
	... 104 common frames omitted
2025-07-02 11:29:08.945 [http-nio-8081-exec-8] ERROR c.m.a.c.s.Phase4AS4ReceiverService - ❌ Failed to add UBL XML to message processing queue: Failed to add message to queue: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (19, DEFAULT, 2025-07-02 11:29:08.8966, INVOICE, null, null, null, 2025-07-02 11:29:08.8966, 3, MSG-2c1c6a47-b63f-4ba9-b6be-809222b0acee, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
java.lang.RuntimeException: Failed to add message to queue: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (19, DEFAULT, 2025-07-02 11:29:08.8966, INVOICE, null, null, null, 2025-07-02 11:29:08.8966, 3, MSG-2c1c6a47-b63f-4ba9-b6be-809222b0acee, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
	at com.morohub.apsp.core.service.MessageQueueService.addMessageToQueue(MessageQueueService.java:59)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.MessageQueueService$$SpringCGLIB$$0.addMessageToQueue(<generated>)
	at com.morohub.apsp.core.service.Phase4AS4ReceiverService.processIncomingAS4MessageContent(Phase4AS4ReceiverService.java:95)
	at com.morohub.apsp.config.Phase4ServletConfiguration$Phase4AS4Servlet.doPost(Phase4ServletConfiguration.java:101)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.springframework.dao.DataIntegrityViolationException: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (19, DEFAULT, 2025-07-02 11:29:08.8966, INVOICE, null, null, null, 2025-07-02 11:29:08.8966, 3, MSG-2c1c6a47-b63f-4ba9-b6be-809222b0acee, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:290)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:550)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:335)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:152)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:164)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:249)
	at jdk.proxy2/jdk.proxy2.$Proxy133.save(Unknown Source)
	at com.morohub.apsp.core.service.MessageQueueService.addMessageToQueue(MessageQueueService.java:52)
	... 50 common frames omitted
Caused by: org.hibernate.exception.ConstraintViolationException: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (19, DEFAULT, 2025-07-02 11:29:08.8966, INVOICE, null, null, null, 2025-07-02 11:29:08.8966, 3, MSG-2c1c6a47-b63f-4ba9-b6be-809222b0acee, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:97)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:283)
	at org.hibernate.id.insert.GetGeneratedKeysDelegate.performInsert(GetGeneratedKeysDelegate.java:107)
	at org.hibernate.engine.jdbc.mutation.internal.MutationExecutorPostInsertSingleTable.execute(MutationExecutorPostInsertSingleTable.java:100)
	at org.hibernate.persister.entity.mutation.InsertCoordinator.doStaticInserts(InsertCoordinator.java:171)
	at org.hibernate.persister.entity.mutation.InsertCoordinator.coordinateInsert(InsertCoordinator.java:112)
	at org.hibernate.persister.entity.AbstractEntityPersister.insert(AbstractEntityPersister.java:2860)
	at org.hibernate.action.internal.EntityIdentityInsertAction.execute(EntityIdentityInsertAction.java:81)
	at org.hibernate.engine.spi.ActionQueue.execute(ActionQueue.java:667)
	at org.hibernate.engine.spi.ActionQueue.addResolvedEntityInsertAction(ActionQueue.java:290)
	at org.hibernate.engine.spi.ActionQueue.addInsertAction(ActionQueue.java:271)
	at org.hibernate.engine.spi.ActionQueue.addAction(ActionQueue.java:321)
	at org.hibernate.event.internal.AbstractSaveEventListener.addInsertAction(AbstractSaveEventListener.java:386)
	at org.hibernate.event.internal.AbstractSaveEventListener.performSaveOrReplicate(AbstractSaveEventListener.java:300)
	at org.hibernate.event.internal.AbstractSaveEventListener.performSave(AbstractSaveEventListener.java:219)
	at org.hibernate.event.internal.AbstractSaveEventListener.saveWithGeneratedId(AbstractSaveEventListener.java:134)
	at org.hibernate.event.internal.DefaultPersistEventListener.entityIsTransient(DefaultPersistEventListener.java:175)
	at org.hibernate.event.internal.DefaultPersistEventListener.persist(DefaultPersistEventListener.java:93)
	at org.hibernate.event.internal.DefaultPersistEventListener.onPersist(DefaultPersistEventListener.java:77)
	at org.hibernate.event.internal.DefaultPersistEventListener.onPersist(DefaultPersistEventListener.java:54)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.firePersist(SessionImpl.java:766)
	at org.hibernate.internal.SessionImpl.persist(SessionImpl.java:750)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler.invoke(SharedEntityManagerCreator.java:311)
	at jdk.proxy2/jdk.proxy2.$Proxy123.persist(Unknown Source)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.save(SimpleJpaRepository.java:618)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:277)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:516)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:285)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:628)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:168)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:143)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	... 58 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (19, DEFAULT, 2025-07-02 11:29:08.8966, INVOICE, null, null, null, 2025-07-02 11:29:08.8966, 3, MSG-2c1c6a47-b63f-4ba9-b6be-809222b0acee, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2713)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2401)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:368)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:498)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:415)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:190)
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:152)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:280)
	... 104 common frames omitted
2025-07-02 11:29:08.949 [http-nio-8081-exec-8] ERROR c.m.a.c.s.Phase4AS4ReceiverService - ❌ AS4 error processing incoming message: Failed to add message to processing queue: Failed to add message to queue: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (19, DEFAULT, 2025-07-02 11:29:08.8966, INVOICE, null, null, null, 2025-07-02 11:29:08.8966, 3, MSG-2c1c6a47-b63f-4ba9-b6be-809222b0acee, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
com.morohub.apsp.common.exception.AS4Exception: Failed to add message to processing queue: Failed to add message to queue: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (19, DEFAULT, 2025-07-02 11:29:08.8966, INVOICE, null, null, null, 2025-07-02 11:29:08.8966, 3, MSG-2c1c6a47-b63f-4ba9-b6be-809222b0acee, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
	at com.morohub.apsp.core.service.Phase4AS4ReceiverService.processIncomingAS4MessageContent(Phase4AS4ReceiverService.java:104)
	at com.morohub.apsp.config.Phase4ServletConfiguration$Phase4AS4Servlet.doPost(Phase4ServletConfiguration.java:101)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.RuntimeException: Failed to add message to queue: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (19, DEFAULT, 2025-07-02 11:29:08.8966, INVOICE, null, null, null, 2025-07-02 11:29:08.8966, 3, MSG-2c1c6a47-b63f-4ba9-b6be-809222b0acee, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
	at com.morohub.apsp.core.service.MessageQueueService.addMessageToQueue(MessageQueueService.java:59)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.MessageQueueService$$SpringCGLIB$$0.addMessageToQueue(<generated>)
	at com.morohub.apsp.core.service.Phase4AS4ReceiverService.processIncomingAS4MessageContent(Phase4AS4ReceiverService.java:95)
	... 36 common frames omitted
Caused by: org.springframework.dao.DataIntegrityViolationException: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (19, DEFAULT, 2025-07-02 11:29:08.8966, INVOICE, null, null, null, 2025-07-02 11:29:08.8966, 3, MSG-2c1c6a47-b63f-4ba9-b6be-809222b0acee, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:290)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:550)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:335)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:152)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:164)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:249)
	at jdk.proxy2/jdk.proxy2.$Proxy133.save(Unknown Source)
	at com.morohub.apsp.core.service.MessageQueueService.addMessageToQueue(MessageQueueService.java:52)
	... 50 common frames omitted
Caused by: org.hibernate.exception.ConstraintViolationException: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (19, DEFAULT, 2025-07-02 11:29:08.8966, INVOICE, null, null, null, 2025-07-02 11:29:08.8966, 3, MSG-2c1c6a47-b63f-4ba9-b6be-809222b0acee, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:97)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:283)
	at org.hibernate.id.insert.GetGeneratedKeysDelegate.performInsert(GetGeneratedKeysDelegate.java:107)
	at org.hibernate.engine.jdbc.mutation.internal.MutationExecutorPostInsertSingleTable.execute(MutationExecutorPostInsertSingleTable.java:100)
	at org.hibernate.persister.entity.mutation.InsertCoordinator.doStaticInserts(InsertCoordinator.java:171)
	at org.hibernate.persister.entity.mutation.InsertCoordinator.coordinateInsert(InsertCoordinator.java:112)
	at org.hibernate.persister.entity.AbstractEntityPersister.insert(AbstractEntityPersister.java:2860)
	at org.hibernate.action.internal.EntityIdentityInsertAction.execute(EntityIdentityInsertAction.java:81)
	at org.hibernate.engine.spi.ActionQueue.execute(ActionQueue.java:667)
	at org.hibernate.engine.spi.ActionQueue.addResolvedEntityInsertAction(ActionQueue.java:290)
	at org.hibernate.engine.spi.ActionQueue.addInsertAction(ActionQueue.java:271)
	at org.hibernate.engine.spi.ActionQueue.addAction(ActionQueue.java:321)
	at org.hibernate.event.internal.AbstractSaveEventListener.addInsertAction(AbstractSaveEventListener.java:386)
	at org.hibernate.event.internal.AbstractSaveEventListener.performSaveOrReplicate(AbstractSaveEventListener.java:300)
	at org.hibernate.event.internal.AbstractSaveEventListener.performSave(AbstractSaveEventListener.java:219)
	at org.hibernate.event.internal.AbstractSaveEventListener.saveWithGeneratedId(AbstractSaveEventListener.java:134)
	at org.hibernate.event.internal.DefaultPersistEventListener.entityIsTransient(DefaultPersistEventListener.java:175)
	at org.hibernate.event.internal.DefaultPersistEventListener.persist(DefaultPersistEventListener.java:93)
	at org.hibernate.event.internal.DefaultPersistEventListener.onPersist(DefaultPersistEventListener.java:77)
	at org.hibernate.event.internal.DefaultPersistEventListener.onPersist(DefaultPersistEventListener.java:54)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.firePersist(SessionImpl.java:766)
	at org.hibernate.internal.SessionImpl.persist(SessionImpl.java:750)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler.invoke(SharedEntityManagerCreator.java:311)
	at jdk.proxy2/jdk.proxy2.$Proxy123.persist(Unknown Source)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.save(SimpleJpaRepository.java:618)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:277)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:516)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:285)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:628)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:168)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:143)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	... 58 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (19, DEFAULT, 2025-07-02 11:29:08.8966, INVOICE, null, null, null, 2025-07-02 11:29:08.8966, 3, MSG-2c1c6a47-b63f-4ba9-b6be-809222b0acee, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2713)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2401)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:368)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:498)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:415)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:190)
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:152)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:280)
	... 104 common frames omitted
2025-07-02 11:29:08.951 [http-nio-8081-exec-8] ERROR c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - ❌ Failed to process AS4 message in servlet
com.morohub.apsp.common.exception.AS4Exception: Failed to add message to processing queue: Failed to add message to queue: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (19, DEFAULT, 2025-07-02 11:29:08.8966, INVOICE, null, null, null, 2025-07-02 11:29:08.8966, 3, MSG-2c1c6a47-b63f-4ba9-b6be-809222b0acee, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
	at com.morohub.apsp.core.service.Phase4AS4ReceiverService.processIncomingAS4MessageContent(Phase4AS4ReceiverService.java:104)
	at com.morohub.apsp.config.Phase4ServletConfiguration$Phase4AS4Servlet.doPost(Phase4ServletConfiguration.java:101)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.RuntimeException: Failed to add message to queue: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (19, DEFAULT, 2025-07-02 11:29:08.8966, INVOICE, null, null, null, 2025-07-02 11:29:08.8966, 3, MSG-2c1c6a47-b63f-4ba9-b6be-809222b0acee, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
	at com.morohub.apsp.core.service.MessageQueueService.addMessageToQueue(MessageQueueService.java:59)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.MessageQueueService$$SpringCGLIB$$0.addMessageToQueue(<generated>)
	at com.morohub.apsp.core.service.Phase4AS4ReceiverService.processIncomingAS4MessageContent(Phase4AS4ReceiverService.java:95)
	... 36 common frames omitted
Caused by: org.springframework.dao.DataIntegrityViolationException: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (19, DEFAULT, 2025-07-02 11:29:08.8966, INVOICE, null, null, null, 2025-07-02 11:29:08.8966, 3, MSG-2c1c6a47-b63f-4ba9-b6be-809222b0acee, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:290)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:550)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:335)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:152)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:164)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:249)
	at jdk.proxy2/jdk.proxy2.$Proxy133.save(Unknown Source)
	at com.morohub.apsp.core.service.MessageQueueService.addMessageToQueue(MessageQueueService.java:52)
	... 50 common frames omitted
Caused by: org.hibernate.exception.ConstraintViolationException: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (19, DEFAULT, 2025-07-02 11:29:08.8966, INVOICE, null, null, null, 2025-07-02 11:29:08.8966, 3, MSG-2c1c6a47-b63f-4ba9-b6be-809222b0acee, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:97)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:283)
	at org.hibernate.id.insert.GetGeneratedKeysDelegate.performInsert(GetGeneratedKeysDelegate.java:107)
	at org.hibernate.engine.jdbc.mutation.internal.MutationExecutorPostInsertSingleTable.execute(MutationExecutorPostInsertSingleTable.java:100)
	at org.hibernate.persister.entity.mutation.InsertCoordinator.doStaticInserts(InsertCoordinator.java:171)
	at org.hibernate.persister.entity.mutation.InsertCoordinator.coordinateInsert(InsertCoordinator.java:112)
	at org.hibernate.persister.entity.AbstractEntityPersister.insert(AbstractEntityPersister.java:2860)
	at org.hibernate.action.internal.EntityIdentityInsertAction.execute(EntityIdentityInsertAction.java:81)
	at org.hibernate.engine.spi.ActionQueue.execute(ActionQueue.java:667)
	at org.hibernate.engine.spi.ActionQueue.addResolvedEntityInsertAction(ActionQueue.java:290)
	at org.hibernate.engine.spi.ActionQueue.addInsertAction(ActionQueue.java:271)
	at org.hibernate.engine.spi.ActionQueue.addAction(ActionQueue.java:321)
	at org.hibernate.event.internal.AbstractSaveEventListener.addInsertAction(AbstractSaveEventListener.java:386)
	at org.hibernate.event.internal.AbstractSaveEventListener.performSaveOrReplicate(AbstractSaveEventListener.java:300)
	at org.hibernate.event.internal.AbstractSaveEventListener.performSave(AbstractSaveEventListener.java:219)
	at org.hibernate.event.internal.AbstractSaveEventListener.saveWithGeneratedId(AbstractSaveEventListener.java:134)
	at org.hibernate.event.internal.DefaultPersistEventListener.entityIsTransient(DefaultPersistEventListener.java:175)
	at org.hibernate.event.internal.DefaultPersistEventListener.persist(DefaultPersistEventListener.java:93)
	at org.hibernate.event.internal.DefaultPersistEventListener.onPersist(DefaultPersistEventListener.java:77)
	at org.hibernate.event.internal.DefaultPersistEventListener.onPersist(DefaultPersistEventListener.java:54)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.firePersist(SessionImpl.java:766)
	at org.hibernate.internal.SessionImpl.persist(SessionImpl.java:750)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler.invoke(SharedEntityManagerCreator.java:311)
	at jdk.proxy2/jdk.proxy2.$Proxy123.persist(Unknown Source)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.save(SimpleJpaRepository.java:618)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:277)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:516)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:285)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:628)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:168)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:143)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	... 58 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (19, DEFAULT, 2025-07-02 11:29:08.8966, INVOICE, null, null, null, 2025-07-02 11:29:08.8966, 3, MSG-2c1c6a47-b63f-4ba9-b6be-809222b0acee, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2713)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2401)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:368)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:498)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:415)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:190)
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:152)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:280)
	... 104 common frames omitted
2025-07-02 11:29:19.198 [http-nio-8081-exec-1] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (20, DEFAULT, 2025-07-02 11:29:19.195769, INVOICE, null, null, null, 2025-07-02 11:29:19.195769, 3, MSG-5ff22f30-1496-4e14-a059-597ba6668f3c, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).
2025-07-02 11:29:19.198 [http-nio-8081-exec-1] ERROR c.m.a.c.service.MessageQueueService - ❌ Error adding message to queue: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (20, DEFAULT, 2025-07-02 11:29:19.195769, INVOICE, null, null, null, 2025-07-02 11:29:19.195769, 3, MSG-5ff22f30-1496-4e14-a059-597ba6668f3c, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
org.springframework.dao.DataIntegrityViolationException: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (20, DEFAULT, 2025-07-02 11:29:19.195769, INVOICE, null, null, null, 2025-07-02 11:29:19.195769, 3, MSG-5ff22f30-1496-4e14-a059-597ba6668f3c, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:290)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:550)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:335)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:152)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:164)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:249)
	at jdk.proxy2/jdk.proxy2.$Proxy133.save(Unknown Source)
	at com.morohub.apsp.core.service.MessageQueueService.addMessageToQueue(MessageQueueService.java:52)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.MessageQueueService$$SpringCGLIB$$0.addMessageToQueue(<generated>)
	at com.morohub.apsp.core.service.Phase4AS4ReceiverService.processIncomingAS4MessageContent(Phase4AS4ReceiverService.java:95)
	at com.morohub.apsp.config.Phase4ServletConfiguration$Phase4AS4Servlet.doPost(Phase4ServletConfiguration.java:101)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.hibernate.exception.ConstraintViolationException: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (20, DEFAULT, 2025-07-02 11:29:19.195769, INVOICE, null, null, null, 2025-07-02 11:29:19.195769, 3, MSG-5ff22f30-1496-4e14-a059-597ba6668f3c, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:97)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:283)
	at org.hibernate.id.insert.GetGeneratedKeysDelegate.performInsert(GetGeneratedKeysDelegate.java:107)
	at org.hibernate.engine.jdbc.mutation.internal.MutationExecutorPostInsertSingleTable.execute(MutationExecutorPostInsertSingleTable.java:100)
	at org.hibernate.persister.entity.mutation.InsertCoordinator.doStaticInserts(InsertCoordinator.java:171)
	at org.hibernate.persister.entity.mutation.InsertCoordinator.coordinateInsert(InsertCoordinator.java:112)
	at org.hibernate.persister.entity.AbstractEntityPersister.insert(AbstractEntityPersister.java:2860)
	at org.hibernate.action.internal.EntityIdentityInsertAction.execute(EntityIdentityInsertAction.java:81)
	at org.hibernate.engine.spi.ActionQueue.execute(ActionQueue.java:667)
	at org.hibernate.engine.spi.ActionQueue.addResolvedEntityInsertAction(ActionQueue.java:290)
	at org.hibernate.engine.spi.ActionQueue.addInsertAction(ActionQueue.java:271)
	at org.hibernate.engine.spi.ActionQueue.addAction(ActionQueue.java:321)
	at org.hibernate.event.internal.AbstractSaveEventListener.addInsertAction(AbstractSaveEventListener.java:386)
	at org.hibernate.event.internal.AbstractSaveEventListener.performSaveOrReplicate(AbstractSaveEventListener.java:300)
	at org.hibernate.event.internal.AbstractSaveEventListener.performSave(AbstractSaveEventListener.java:219)
	at org.hibernate.event.internal.AbstractSaveEventListener.saveWithGeneratedId(AbstractSaveEventListener.java:134)
	at org.hibernate.event.internal.DefaultPersistEventListener.entityIsTransient(DefaultPersistEventListener.java:175)
	at org.hibernate.event.internal.DefaultPersistEventListener.persist(DefaultPersistEventListener.java:93)
	at org.hibernate.event.internal.DefaultPersistEventListener.onPersist(DefaultPersistEventListener.java:77)
	at org.hibernate.event.internal.DefaultPersistEventListener.onPersist(DefaultPersistEventListener.java:54)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.firePersist(SessionImpl.java:766)
	at org.hibernate.internal.SessionImpl.persist(SessionImpl.java:750)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler.invoke(SharedEntityManagerCreator.java:311)
	at jdk.proxy2/jdk.proxy2.$Proxy123.persist(Unknown Source)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.save(SimpleJpaRepository.java:618)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:277)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:516)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:285)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:628)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:168)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:143)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	... 58 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (20, DEFAULT, 2025-07-02 11:29:19.195769, INVOICE, null, null, null, 2025-07-02 11:29:19.195769, 3, MSG-5ff22f30-1496-4e14-a059-597ba6668f3c, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2713)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2401)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:368)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:498)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:415)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:190)
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:152)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:280)
	... 104 common frames omitted
2025-07-02 11:29:19.201 [http-nio-8081-exec-1] ERROR c.m.a.c.s.Phase4AS4ReceiverService - ❌ Failed to add UBL XML to message processing queue: Failed to add message to queue: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (20, DEFAULT, 2025-07-02 11:29:19.195769, INVOICE, null, null, null, 2025-07-02 11:29:19.195769, 3, MSG-5ff22f30-1496-4e14-a059-597ba6668f3c, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
java.lang.RuntimeException: Failed to add message to queue: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (20, DEFAULT, 2025-07-02 11:29:19.195769, INVOICE, null, null, null, 2025-07-02 11:29:19.195769, 3, MSG-5ff22f30-1496-4e14-a059-597ba6668f3c, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
	at com.morohub.apsp.core.service.MessageQueueService.addMessageToQueue(MessageQueueService.java:59)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.MessageQueueService$$SpringCGLIB$$0.addMessageToQueue(<generated>)
	at com.morohub.apsp.core.service.Phase4AS4ReceiverService.processIncomingAS4MessageContent(Phase4AS4ReceiverService.java:95)
	at com.morohub.apsp.config.Phase4ServletConfiguration$Phase4AS4Servlet.doPost(Phase4ServletConfiguration.java:101)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.springframework.dao.DataIntegrityViolationException: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (20, DEFAULT, 2025-07-02 11:29:19.195769, INVOICE, null, null, null, 2025-07-02 11:29:19.195769, 3, MSG-5ff22f30-1496-4e14-a059-597ba6668f3c, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:290)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:550)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:335)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:152)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:164)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:249)
	at jdk.proxy2/jdk.proxy2.$Proxy133.save(Unknown Source)
	at com.morohub.apsp.core.service.MessageQueueService.addMessageToQueue(MessageQueueService.java:52)
	... 50 common frames omitted
Caused by: org.hibernate.exception.ConstraintViolationException: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (20, DEFAULT, 2025-07-02 11:29:19.195769, INVOICE, null, null, null, 2025-07-02 11:29:19.195769, 3, MSG-5ff22f30-1496-4e14-a059-597ba6668f3c, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:97)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:283)
	at org.hibernate.id.insert.GetGeneratedKeysDelegate.performInsert(GetGeneratedKeysDelegate.java:107)
	at org.hibernate.engine.jdbc.mutation.internal.MutationExecutorPostInsertSingleTable.execute(MutationExecutorPostInsertSingleTable.java:100)
	at org.hibernate.persister.entity.mutation.InsertCoordinator.doStaticInserts(InsertCoordinator.java:171)
	at org.hibernate.persister.entity.mutation.InsertCoordinator.coordinateInsert(InsertCoordinator.java:112)
	at org.hibernate.persister.entity.AbstractEntityPersister.insert(AbstractEntityPersister.java:2860)
	at org.hibernate.action.internal.EntityIdentityInsertAction.execute(EntityIdentityInsertAction.java:81)
	at org.hibernate.engine.spi.ActionQueue.execute(ActionQueue.java:667)
	at org.hibernate.engine.spi.ActionQueue.addResolvedEntityInsertAction(ActionQueue.java:290)
	at org.hibernate.engine.spi.ActionQueue.addInsertAction(ActionQueue.java:271)
	at org.hibernate.engine.spi.ActionQueue.addAction(ActionQueue.java:321)
	at org.hibernate.event.internal.AbstractSaveEventListener.addInsertAction(AbstractSaveEventListener.java:386)
	at org.hibernate.event.internal.AbstractSaveEventListener.performSaveOrReplicate(AbstractSaveEventListener.java:300)
	at org.hibernate.event.internal.AbstractSaveEventListener.performSave(AbstractSaveEventListener.java:219)
	at org.hibernate.event.internal.AbstractSaveEventListener.saveWithGeneratedId(AbstractSaveEventListener.java:134)
	at org.hibernate.event.internal.DefaultPersistEventListener.entityIsTransient(DefaultPersistEventListener.java:175)
	at org.hibernate.event.internal.DefaultPersistEventListener.persist(DefaultPersistEventListener.java:93)
	at org.hibernate.event.internal.DefaultPersistEventListener.onPersist(DefaultPersistEventListener.java:77)
	at org.hibernate.event.internal.DefaultPersistEventListener.onPersist(DefaultPersistEventListener.java:54)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.firePersist(SessionImpl.java:766)
	at org.hibernate.internal.SessionImpl.persist(SessionImpl.java:750)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler.invoke(SharedEntityManagerCreator.java:311)
	at jdk.proxy2/jdk.proxy2.$Proxy123.persist(Unknown Source)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.save(SimpleJpaRepository.java:618)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:277)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:516)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:285)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:628)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:168)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:143)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	... 58 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (20, DEFAULT, 2025-07-02 11:29:19.195769, INVOICE, null, null, null, 2025-07-02 11:29:19.195769, 3, MSG-5ff22f30-1496-4e14-a059-597ba6668f3c, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2713)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2401)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:368)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:498)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:415)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:190)
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:152)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:280)
	... 104 common frames omitted
2025-07-02 11:29:19.202 [http-nio-8081-exec-1] ERROR c.m.a.c.s.Phase4AS4ReceiverService - ❌ AS4 error processing incoming message: Failed to add message to processing queue: Failed to add message to queue: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (20, DEFAULT, 2025-07-02 11:29:19.195769, INVOICE, null, null, null, 2025-07-02 11:29:19.195769, 3, MSG-5ff22f30-1496-4e14-a059-597ba6668f3c, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
com.morohub.apsp.common.exception.AS4Exception: Failed to add message to processing queue: Failed to add message to queue: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (20, DEFAULT, 2025-07-02 11:29:19.195769, INVOICE, null, null, null, 2025-07-02 11:29:19.195769, 3, MSG-5ff22f30-1496-4e14-a059-597ba6668f3c, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
	at com.morohub.apsp.core.service.Phase4AS4ReceiverService.processIncomingAS4MessageContent(Phase4AS4ReceiverService.java:104)
	at com.morohub.apsp.config.Phase4ServletConfiguration$Phase4AS4Servlet.doPost(Phase4ServletConfiguration.java:101)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.RuntimeException: Failed to add message to queue: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (20, DEFAULT, 2025-07-02 11:29:19.195769, INVOICE, null, null, null, 2025-07-02 11:29:19.195769, 3, MSG-5ff22f30-1496-4e14-a059-597ba6668f3c, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
	at com.morohub.apsp.core.service.MessageQueueService.addMessageToQueue(MessageQueueService.java:59)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.MessageQueueService$$SpringCGLIB$$0.addMessageToQueue(<generated>)
	at com.morohub.apsp.core.service.Phase4AS4ReceiverService.processIncomingAS4MessageContent(Phase4AS4ReceiverService.java:95)
	... 36 common frames omitted
Caused by: org.springframework.dao.DataIntegrityViolationException: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (20, DEFAULT, 2025-07-02 11:29:19.195769, INVOICE, null, null, null, 2025-07-02 11:29:19.195769, 3, MSG-5ff22f30-1496-4e14-a059-597ba6668f3c, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:290)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:550)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:335)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:152)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:164)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:249)
	at jdk.proxy2/jdk.proxy2.$Proxy133.save(Unknown Source)
	at com.morohub.apsp.core.service.MessageQueueService.addMessageToQueue(MessageQueueService.java:52)
	... 50 common frames omitted
Caused by: org.hibernate.exception.ConstraintViolationException: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (20, DEFAULT, 2025-07-02 11:29:19.195769, INVOICE, null, null, null, 2025-07-02 11:29:19.195769, 3, MSG-5ff22f30-1496-4e14-a059-597ba6668f3c, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:97)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:283)
	at org.hibernate.id.insert.GetGeneratedKeysDelegate.performInsert(GetGeneratedKeysDelegate.java:107)
	at org.hibernate.engine.jdbc.mutation.internal.MutationExecutorPostInsertSingleTable.execute(MutationExecutorPostInsertSingleTable.java:100)
	at org.hibernate.persister.entity.mutation.InsertCoordinator.doStaticInserts(InsertCoordinator.java:171)
	at org.hibernate.persister.entity.mutation.InsertCoordinator.coordinateInsert(InsertCoordinator.java:112)
	at org.hibernate.persister.entity.AbstractEntityPersister.insert(AbstractEntityPersister.java:2860)
	at org.hibernate.action.internal.EntityIdentityInsertAction.execute(EntityIdentityInsertAction.java:81)
	at org.hibernate.engine.spi.ActionQueue.execute(ActionQueue.java:667)
	at org.hibernate.engine.spi.ActionQueue.addResolvedEntityInsertAction(ActionQueue.java:290)
	at org.hibernate.engine.spi.ActionQueue.addInsertAction(ActionQueue.java:271)
	at org.hibernate.engine.spi.ActionQueue.addAction(ActionQueue.java:321)
	at org.hibernate.event.internal.AbstractSaveEventListener.addInsertAction(AbstractSaveEventListener.java:386)
	at org.hibernate.event.internal.AbstractSaveEventListener.performSaveOrReplicate(AbstractSaveEventListener.java:300)
	at org.hibernate.event.internal.AbstractSaveEventListener.performSave(AbstractSaveEventListener.java:219)
	at org.hibernate.event.internal.AbstractSaveEventListener.saveWithGeneratedId(AbstractSaveEventListener.java:134)
	at org.hibernate.event.internal.DefaultPersistEventListener.entityIsTransient(DefaultPersistEventListener.java:175)
	at org.hibernate.event.internal.DefaultPersistEventListener.persist(DefaultPersistEventListener.java:93)
	at org.hibernate.event.internal.DefaultPersistEventListener.onPersist(DefaultPersistEventListener.java:77)
	at org.hibernate.event.internal.DefaultPersistEventListener.onPersist(DefaultPersistEventListener.java:54)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.firePersist(SessionImpl.java:766)
	at org.hibernate.internal.SessionImpl.persist(SessionImpl.java:750)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler.invoke(SharedEntityManagerCreator.java:311)
	at jdk.proxy2/jdk.proxy2.$Proxy123.persist(Unknown Source)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.save(SimpleJpaRepository.java:618)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:277)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:516)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:285)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:628)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:168)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:143)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	... 58 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (20, DEFAULT, 2025-07-02 11:29:19.195769, INVOICE, null, null, null, 2025-07-02 11:29:19.195769, 3, MSG-5ff22f30-1496-4e14-a059-597ba6668f3c, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2713)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2401)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:368)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:498)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:415)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:190)
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:152)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:280)
	... 104 common frames omitted
2025-07-02 11:29:19.205 [http-nio-8081-exec-1] ERROR c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - ❌ Failed to process AS4 message in servlet
com.morohub.apsp.common.exception.AS4Exception: Failed to add message to processing queue: Failed to add message to queue: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (20, DEFAULT, 2025-07-02 11:29:19.195769, INVOICE, null, null, null, 2025-07-02 11:29:19.195769, 3, MSG-5ff22f30-1496-4e14-a059-597ba6668f3c, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
	at com.morohub.apsp.core.service.Phase4AS4ReceiverService.processIncomingAS4MessageContent(Phase4AS4ReceiverService.java:104)
	at com.morohub.apsp.config.Phase4ServletConfiguration$Phase4AS4Servlet.doPost(Phase4ServletConfiguration.java:101)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.RuntimeException: Failed to add message to queue: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (20, DEFAULT, 2025-07-02 11:29:19.195769, INVOICE, null, null, null, 2025-07-02 11:29:19.195769, 3, MSG-5ff22f30-1496-4e14-a059-597ba6668f3c, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
	at com.morohub.apsp.core.service.MessageQueueService.addMessageToQueue(MessageQueueService.java:59)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.MessageQueueService$$SpringCGLIB$$0.addMessageToQueue(<generated>)
	at com.morohub.apsp.core.service.Phase4AS4ReceiverService.processIncomingAS4MessageContent(Phase4AS4ReceiverService.java:95)
	... 36 common frames omitted
Caused by: org.springframework.dao.DataIntegrityViolationException: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (20, DEFAULT, 2025-07-02 11:29:19.195769, INVOICE, null, null, null, 2025-07-02 11:29:19.195769, 3, MSG-5ff22f30-1496-4e14-a059-597ba6668f3c, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:290)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:550)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:335)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:152)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:164)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:249)
	at jdk.proxy2/jdk.proxy2.$Proxy133.save(Unknown Source)
	at com.morohub.apsp.core.service.MessageQueueService.addMessageToQueue(MessageQueueService.java:52)
	... 50 common frames omitted
Caused by: org.hibernate.exception.ConstraintViolationException: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (20, DEFAULT, 2025-07-02 11:29:19.195769, INVOICE, null, null, null, 2025-07-02 11:29:19.195769, 3, MSG-5ff22f30-1496-4e14-a059-597ba6668f3c, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:97)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:283)
	at org.hibernate.id.insert.GetGeneratedKeysDelegate.performInsert(GetGeneratedKeysDelegate.java:107)
	at org.hibernate.engine.jdbc.mutation.internal.MutationExecutorPostInsertSingleTable.execute(MutationExecutorPostInsertSingleTable.java:100)
	at org.hibernate.persister.entity.mutation.InsertCoordinator.doStaticInserts(InsertCoordinator.java:171)
	at org.hibernate.persister.entity.mutation.InsertCoordinator.coordinateInsert(InsertCoordinator.java:112)
	at org.hibernate.persister.entity.AbstractEntityPersister.insert(AbstractEntityPersister.java:2860)
	at org.hibernate.action.internal.EntityIdentityInsertAction.execute(EntityIdentityInsertAction.java:81)
	at org.hibernate.engine.spi.ActionQueue.execute(ActionQueue.java:667)
	at org.hibernate.engine.spi.ActionQueue.addResolvedEntityInsertAction(ActionQueue.java:290)
	at org.hibernate.engine.spi.ActionQueue.addInsertAction(ActionQueue.java:271)
	at org.hibernate.engine.spi.ActionQueue.addAction(ActionQueue.java:321)
	at org.hibernate.event.internal.AbstractSaveEventListener.addInsertAction(AbstractSaveEventListener.java:386)
	at org.hibernate.event.internal.AbstractSaveEventListener.performSaveOrReplicate(AbstractSaveEventListener.java:300)
	at org.hibernate.event.internal.AbstractSaveEventListener.performSave(AbstractSaveEventListener.java:219)
	at org.hibernate.event.internal.AbstractSaveEventListener.saveWithGeneratedId(AbstractSaveEventListener.java:134)
	at org.hibernate.event.internal.DefaultPersistEventListener.entityIsTransient(DefaultPersistEventListener.java:175)
	at org.hibernate.event.internal.DefaultPersistEventListener.persist(DefaultPersistEventListener.java:93)
	at org.hibernate.event.internal.DefaultPersistEventListener.onPersist(DefaultPersistEventListener.java:77)
	at org.hibernate.event.internal.DefaultPersistEventListener.onPersist(DefaultPersistEventListener.java:54)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.firePersist(SessionImpl.java:766)
	at org.hibernate.internal.SessionImpl.persist(SessionImpl.java:750)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler.invoke(SharedEntityManagerCreator.java:311)
	at jdk.proxy2/jdk.proxy2.$Proxy123.persist(Unknown Source)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.save(SimpleJpaRepository.java:618)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:277)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:516)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:285)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:628)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:168)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:143)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	... 58 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (20, DEFAULT, 2025-07-02 11:29:19.195769, INVOICE, null, null, null, 2025-07-02 11:29:19.195769, 3, MSG-5ff22f30-1496-4e14-a059-597ba6668f3c, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2713)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2401)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:368)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:498)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:415)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:190)
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:152)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:280)
	... 104 common frames omitted
2025-07-02 11:29:19.218 [http-nio-8081-exec-7] ERROR c.h.p.s.AbstractAS4UserMessageBuilder - Exception sending AS4 user message
com.helger.phase4.util.Phase4Exception: Wrapped Phase4Exception
	at com.helger.phase4.sender.AbstractAS4UserMessageBuilderMIMEPayload.mainSendMessage(AbstractAS4UserMessageBuilderMIMEPayload.java:237)
	at com.helger.phase4.sender.AbstractAS4MessageBuilder.sendMessage(AbstractAS4MessageBuilder.java:856)
	at com.helger.phase4.sender.AbstractAS4UserMessageBuilder.sendMessageAndCheckForReceipt(AbstractAS4UserMessageBuilder.java:798)
	at com.helger.phase4.sender.AbstractAS4UserMessageBuilder.sendMessageAndCheckForReceipt(AbstractAS4UserMessageBuilder.java:764)
	at com.morohub.apsp.core.service.AS4ConversionService.processAs4message(AS4ConversionService.java:902)
	at com.morohub.apsp.core.service.AS4ConversionService.sendAS4Message(AS4ConversionService.java:824)
	at com.morohub.apsp.core.service.AS4ConversionService.convertAndSend(AS4ConversionService.java:250)
	at com.morohub.apsp.api.controller.XmlValidationController.validateAndConvertToAs4(XmlValidationController.java:78)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:254)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:182)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: com.helger.httpclient.response.ExtendedHttpResponseException:  [500]
All 4 headers returned
  Content-Type=application/soap+xml;charset=UTF-8
  Content-Length=2249
  Date=Wed, 02 Jul 2025 05:59:19 GMT
  Connection=close
Response Body (in UTF-8):
<?xml version="1.0" encoding="UTF-8"?>
<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope" xmlns:eb="http://docs.oasis-open.org/ebxml-msg/ebms/v3.0/ns/core/200704/">
  <soap:Header>
    <eb:Messaging soap:mustUnderstand="true">
      <eb:SignalMessage>
        <eb:MessageInfo>
          <eb:Timestamp>2025-07-02T05:59:19.207768200Z</eb:Timestamp>
          <eb:MessageId>f34f543e-612f-431b-a23b-e9879a2edced</eb:MessageId>
        </eb:MessageInfo>
        <eb:Error errorCode="EBMS:0004" severity="failure" shortDescription="Other">
          <eb:Description xml:lang="en">Failed to add message to processing queue: Failed to add message to queue: could not execute statement [ERROR: null value in column &quot;flow_type&quot; of relation &quot;message_processing_queue&quot; violates not-null constraint
  Detail: Failing row contains (20, DEFAULT, 2025-07-02 11:29:19.195769, INVOICE, null, null, null, 2025-07-02 11:29:19.195769, 3, MSG-5ff22f30-1496-4e14-a059-597ba6668f3c, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, &lt;Invoice xmlns=&quot;urn:oasis:names:specification:ubl:schema:xsd:Inv..., aff29bcb-7bb3-4098-8472-4da367a19890, &lt;BusinessScope 
                  &gt;
   &lt;Scope&gt;
      &lt;Type&gt;DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type&quot; of relation &quot;message_processing_queue]</eb:Description>
        </eb:Error>
      </eb:SignalMessage>
    </eb:Messaging>
  </soap:Header>
  <soap:Body/>
</soap:Envelope>
	at com.helger.httpclient.response.ExtendedHttpResponseException.create(ExtendedHttpResponseException.java:211)
	at com.helger.httpclient.response.ResponseHandlerHttpEntity.handleResponse(ResponseHandlerHttpEntity.java:55)
	at com.helger.phase4.sender.AS4BidirectionalClientHelper.lambda$sendAS4UserMessageAndReceiveAS4SignalMessage$0(AS4BidirectionalClientHelper.java:125)
	at com.helger.phase4.client.AbstractAS4Client.lambda$sendMessageWithRetries$1(AbstractAS4Client.java:589)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:247)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:188)
	at com.helger.httpclient.HttpClientManager.execute(HttpClientManager.java:137)
	at com.helger.httpclient.HttpClientManager.execute(HttpClientManager.java:102)
	at com.helger.phase4.http.BasicHttpPoster.sendGenericMessage(BasicHttpPoster.java:201)
	at com.helger.phase4.http.BasicHttpPoster.sendGenericMessageWithRetries(BasicHttpPoster.java:317)
	at com.helger.phase4.client.AbstractAS4Client.sendMessageWithRetries(AbstractAS4Client.java:591)
	at com.helger.phase4.sender.AS4BidirectionalClientHelper.sendAS4UserMessageAndReceiveAS4SignalMessage(AS4BidirectionalClientHelper.java:137)
	at com.helger.phase4.sender.AbstractAS4UserMessageBuilderMIMEPayload.mainSendMessage(AbstractAS4UserMessageBuilderMIMEPayload.java:211)
	... 55 common frames omitted
2025-07-02 11:29:19.219 [http-nio-8081-exec-7] ERROR c.m.a.c.service.AS4ConversionService - ❌ Production AS4 send failed: TRANSPORT_ERROR
2025-07-02 11:29:19.220 [http-nio-8081-exec-7] ERROR c.m.a.a.c.XmlValidationController - ❌ AS4 conversion failed for request: f1a45200-06eb-49f6-8c7d-cba39d370a58: Production AS4 send failed: TRANSPORT_ERROR
2025-07-02 11:29:19.240 [http-nio-8081-exec-7] ERROR c.m.a.config.GlobalExceptionHandler - AS4Exception handled: AS4_001 - AS4 conversion failed: Production AS4 send failed: TRANSPORT_ERROR
2025-07-02 11:31:10.761 [http-nio-8081-exec-5] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (22, DEFAULT, 2025-07-02 11:31:10.758493, INVOICE, null, null, null, 2025-07-02 11:31:10.758493, 3, MSG-3edaf855-0ff8-47d8-8d91-7785a456d901, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).
2025-07-02 11:31:10.762 [http-nio-8081-exec-5] ERROR c.m.a.c.service.MessageQueueService - ❌ Error adding message to queue: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (22, DEFAULT, 2025-07-02 11:31:10.758493, INVOICE, null, null, null, 2025-07-02 11:31:10.758493, 3, MSG-3edaf855-0ff8-47d8-8d91-7785a456d901, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
org.springframework.dao.DataIntegrityViolationException: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (22, DEFAULT, 2025-07-02 11:31:10.758493, INVOICE, null, null, null, 2025-07-02 11:31:10.758493, 3, MSG-3edaf855-0ff8-47d8-8d91-7785a456d901, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:290)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:550)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:335)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:152)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:164)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:249)
	at jdk.proxy2/jdk.proxy2.$Proxy133.save(Unknown Source)
	at com.morohub.apsp.core.service.MessageQueueService.addMessageToQueue(MessageQueueService.java:52)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.MessageQueueService$$SpringCGLIB$$0.addMessageToQueue(<generated>)
	at com.morohub.apsp.core.service.Phase4AS4ReceiverService.processIncomingAS4MessageContent(Phase4AS4ReceiverService.java:95)
	at com.morohub.apsp.config.Phase4ServletConfiguration$Phase4AS4Servlet.doPost(Phase4ServletConfiguration.java:101)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.hibernate.exception.ConstraintViolationException: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (22, DEFAULT, 2025-07-02 11:31:10.758493, INVOICE, null, null, null, 2025-07-02 11:31:10.758493, 3, MSG-3edaf855-0ff8-47d8-8d91-7785a456d901, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:97)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:283)
	at org.hibernate.id.insert.GetGeneratedKeysDelegate.performInsert(GetGeneratedKeysDelegate.java:107)
	at org.hibernate.engine.jdbc.mutation.internal.MutationExecutorPostInsertSingleTable.execute(MutationExecutorPostInsertSingleTable.java:100)
	at org.hibernate.persister.entity.mutation.InsertCoordinator.doStaticInserts(InsertCoordinator.java:171)
	at org.hibernate.persister.entity.mutation.InsertCoordinator.coordinateInsert(InsertCoordinator.java:112)
	at org.hibernate.persister.entity.AbstractEntityPersister.insert(AbstractEntityPersister.java:2860)
	at org.hibernate.action.internal.EntityIdentityInsertAction.execute(EntityIdentityInsertAction.java:81)
	at org.hibernate.engine.spi.ActionQueue.execute(ActionQueue.java:667)
	at org.hibernate.engine.spi.ActionQueue.addResolvedEntityInsertAction(ActionQueue.java:290)
	at org.hibernate.engine.spi.ActionQueue.addInsertAction(ActionQueue.java:271)
	at org.hibernate.engine.spi.ActionQueue.addAction(ActionQueue.java:321)
	at org.hibernate.event.internal.AbstractSaveEventListener.addInsertAction(AbstractSaveEventListener.java:386)
	at org.hibernate.event.internal.AbstractSaveEventListener.performSaveOrReplicate(AbstractSaveEventListener.java:300)
	at org.hibernate.event.internal.AbstractSaveEventListener.performSave(AbstractSaveEventListener.java:219)
	at org.hibernate.event.internal.AbstractSaveEventListener.saveWithGeneratedId(AbstractSaveEventListener.java:134)
	at org.hibernate.event.internal.DefaultPersistEventListener.entityIsTransient(DefaultPersistEventListener.java:175)
	at org.hibernate.event.internal.DefaultPersistEventListener.persist(DefaultPersistEventListener.java:93)
	at org.hibernate.event.internal.DefaultPersistEventListener.onPersist(DefaultPersistEventListener.java:77)
	at org.hibernate.event.internal.DefaultPersistEventListener.onPersist(DefaultPersistEventListener.java:54)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.firePersist(SessionImpl.java:766)
	at org.hibernate.internal.SessionImpl.persist(SessionImpl.java:750)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler.invoke(SharedEntityManagerCreator.java:311)
	at jdk.proxy2/jdk.proxy2.$Proxy123.persist(Unknown Source)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.save(SimpleJpaRepository.java:618)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:277)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:516)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:285)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:628)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:168)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:143)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	... 58 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (22, DEFAULT, 2025-07-02 11:31:10.758493, INVOICE, null, null, null, 2025-07-02 11:31:10.758493, 3, MSG-3edaf855-0ff8-47d8-8d91-7785a456d901, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2713)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2401)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:368)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:498)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:415)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:190)
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:152)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:280)
	... 104 common frames omitted
2025-07-02 11:31:10.764 [http-nio-8081-exec-5] ERROR c.m.a.c.s.Phase4AS4ReceiverService - ❌ Failed to add UBL XML to message processing queue: Failed to add message to queue: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (22, DEFAULT, 2025-07-02 11:31:10.758493, INVOICE, null, null, null, 2025-07-02 11:31:10.758493, 3, MSG-3edaf855-0ff8-47d8-8d91-7785a456d901, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
java.lang.RuntimeException: Failed to add message to queue: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (22, DEFAULT, 2025-07-02 11:31:10.758493, INVOICE, null, null, null, 2025-07-02 11:31:10.758493, 3, MSG-3edaf855-0ff8-47d8-8d91-7785a456d901, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
	at com.morohub.apsp.core.service.MessageQueueService.addMessageToQueue(MessageQueueService.java:59)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.MessageQueueService$$SpringCGLIB$$0.addMessageToQueue(<generated>)
	at com.morohub.apsp.core.service.Phase4AS4ReceiverService.processIncomingAS4MessageContent(Phase4AS4ReceiverService.java:95)
	at com.morohub.apsp.config.Phase4ServletConfiguration$Phase4AS4Servlet.doPost(Phase4ServletConfiguration.java:101)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.springframework.dao.DataIntegrityViolationException: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (22, DEFAULT, 2025-07-02 11:31:10.758493, INVOICE, null, null, null, 2025-07-02 11:31:10.758493, 3, MSG-3edaf855-0ff8-47d8-8d91-7785a456d901, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:290)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:550)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:335)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:152)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:164)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:249)
	at jdk.proxy2/jdk.proxy2.$Proxy133.save(Unknown Source)
	at com.morohub.apsp.core.service.MessageQueueService.addMessageToQueue(MessageQueueService.java:52)
	... 50 common frames omitted
Caused by: org.hibernate.exception.ConstraintViolationException: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (22, DEFAULT, 2025-07-02 11:31:10.758493, INVOICE, null, null, null, 2025-07-02 11:31:10.758493, 3, MSG-3edaf855-0ff8-47d8-8d91-7785a456d901, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:97)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:283)
	at org.hibernate.id.insert.GetGeneratedKeysDelegate.performInsert(GetGeneratedKeysDelegate.java:107)
	at org.hibernate.engine.jdbc.mutation.internal.MutationExecutorPostInsertSingleTable.execute(MutationExecutorPostInsertSingleTable.java:100)
	at org.hibernate.persister.entity.mutation.InsertCoordinator.doStaticInserts(InsertCoordinator.java:171)
	at org.hibernate.persister.entity.mutation.InsertCoordinator.coordinateInsert(InsertCoordinator.java:112)
	at org.hibernate.persister.entity.AbstractEntityPersister.insert(AbstractEntityPersister.java:2860)
	at org.hibernate.action.internal.EntityIdentityInsertAction.execute(EntityIdentityInsertAction.java:81)
	at org.hibernate.engine.spi.ActionQueue.execute(ActionQueue.java:667)
	at org.hibernate.engine.spi.ActionQueue.addResolvedEntityInsertAction(ActionQueue.java:290)
	at org.hibernate.engine.spi.ActionQueue.addInsertAction(ActionQueue.java:271)
	at org.hibernate.engine.spi.ActionQueue.addAction(ActionQueue.java:321)
	at org.hibernate.event.internal.AbstractSaveEventListener.addInsertAction(AbstractSaveEventListener.java:386)
	at org.hibernate.event.internal.AbstractSaveEventListener.performSaveOrReplicate(AbstractSaveEventListener.java:300)
	at org.hibernate.event.internal.AbstractSaveEventListener.performSave(AbstractSaveEventListener.java:219)
	at org.hibernate.event.internal.AbstractSaveEventListener.saveWithGeneratedId(AbstractSaveEventListener.java:134)
	at org.hibernate.event.internal.DefaultPersistEventListener.entityIsTransient(DefaultPersistEventListener.java:175)
	at org.hibernate.event.internal.DefaultPersistEventListener.persist(DefaultPersistEventListener.java:93)
	at org.hibernate.event.internal.DefaultPersistEventListener.onPersist(DefaultPersistEventListener.java:77)
	at org.hibernate.event.internal.DefaultPersistEventListener.onPersist(DefaultPersistEventListener.java:54)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.firePersist(SessionImpl.java:766)
	at org.hibernate.internal.SessionImpl.persist(SessionImpl.java:750)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler.invoke(SharedEntityManagerCreator.java:311)
	at jdk.proxy2/jdk.proxy2.$Proxy123.persist(Unknown Source)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.save(SimpleJpaRepository.java:618)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:277)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:516)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:285)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:628)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:168)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:143)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	... 58 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (22, DEFAULT, 2025-07-02 11:31:10.758493, INVOICE, null, null, null, 2025-07-02 11:31:10.758493, 3, MSG-3edaf855-0ff8-47d8-8d91-7785a456d901, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2713)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2401)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:368)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:498)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:415)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:190)
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:152)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:280)
	... 104 common frames omitted
2025-07-02 11:31:10.765 [http-nio-8081-exec-5] ERROR c.m.a.c.s.Phase4AS4ReceiverService - ❌ AS4 error processing incoming message: Failed to add message to processing queue: Failed to add message to queue: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (22, DEFAULT, 2025-07-02 11:31:10.758493, INVOICE, null, null, null, 2025-07-02 11:31:10.758493, 3, MSG-3edaf855-0ff8-47d8-8d91-7785a456d901, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
com.morohub.apsp.common.exception.AS4Exception: Failed to add message to processing queue: Failed to add message to queue: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (22, DEFAULT, 2025-07-02 11:31:10.758493, INVOICE, null, null, null, 2025-07-02 11:31:10.758493, 3, MSG-3edaf855-0ff8-47d8-8d91-7785a456d901, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
	at com.morohub.apsp.core.service.Phase4AS4ReceiverService.processIncomingAS4MessageContent(Phase4AS4ReceiverService.java:104)
	at com.morohub.apsp.config.Phase4ServletConfiguration$Phase4AS4Servlet.doPost(Phase4ServletConfiguration.java:101)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.RuntimeException: Failed to add message to queue: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (22, DEFAULT, 2025-07-02 11:31:10.758493, INVOICE, null, null, null, 2025-07-02 11:31:10.758493, 3, MSG-3edaf855-0ff8-47d8-8d91-7785a456d901, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
	at com.morohub.apsp.core.service.MessageQueueService.addMessageToQueue(MessageQueueService.java:59)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.MessageQueueService$$SpringCGLIB$$0.addMessageToQueue(<generated>)
	at com.morohub.apsp.core.service.Phase4AS4ReceiverService.processIncomingAS4MessageContent(Phase4AS4ReceiverService.java:95)
	... 36 common frames omitted
Caused by: org.springframework.dao.DataIntegrityViolationException: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (22, DEFAULT, 2025-07-02 11:31:10.758493, INVOICE, null, null, null, 2025-07-02 11:31:10.758493, 3, MSG-3edaf855-0ff8-47d8-8d91-7785a456d901, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:290)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:550)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:335)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:152)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:164)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:249)
	at jdk.proxy2/jdk.proxy2.$Proxy133.save(Unknown Source)
	at com.morohub.apsp.core.service.MessageQueueService.addMessageToQueue(MessageQueueService.java:52)
	... 50 common frames omitted
Caused by: org.hibernate.exception.ConstraintViolationException: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (22, DEFAULT, 2025-07-02 11:31:10.758493, INVOICE, null, null, null, 2025-07-02 11:31:10.758493, 3, MSG-3edaf855-0ff8-47d8-8d91-7785a456d901, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:97)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:283)
	at org.hibernate.id.insert.GetGeneratedKeysDelegate.performInsert(GetGeneratedKeysDelegate.java:107)
	at org.hibernate.engine.jdbc.mutation.internal.MutationExecutorPostInsertSingleTable.execute(MutationExecutorPostInsertSingleTable.java:100)
	at org.hibernate.persister.entity.mutation.InsertCoordinator.doStaticInserts(InsertCoordinator.java:171)
	at org.hibernate.persister.entity.mutation.InsertCoordinator.coordinateInsert(InsertCoordinator.java:112)
	at org.hibernate.persister.entity.AbstractEntityPersister.insert(AbstractEntityPersister.java:2860)
	at org.hibernate.action.internal.EntityIdentityInsertAction.execute(EntityIdentityInsertAction.java:81)
	at org.hibernate.engine.spi.ActionQueue.execute(ActionQueue.java:667)
	at org.hibernate.engine.spi.ActionQueue.addResolvedEntityInsertAction(ActionQueue.java:290)
	at org.hibernate.engine.spi.ActionQueue.addInsertAction(ActionQueue.java:271)
	at org.hibernate.engine.spi.ActionQueue.addAction(ActionQueue.java:321)
	at org.hibernate.event.internal.AbstractSaveEventListener.addInsertAction(AbstractSaveEventListener.java:386)
	at org.hibernate.event.internal.AbstractSaveEventListener.performSaveOrReplicate(AbstractSaveEventListener.java:300)
	at org.hibernate.event.internal.AbstractSaveEventListener.performSave(AbstractSaveEventListener.java:219)
	at org.hibernate.event.internal.AbstractSaveEventListener.saveWithGeneratedId(AbstractSaveEventListener.java:134)
	at org.hibernate.event.internal.DefaultPersistEventListener.entityIsTransient(DefaultPersistEventListener.java:175)
	at org.hibernate.event.internal.DefaultPersistEventListener.persist(DefaultPersistEventListener.java:93)
	at org.hibernate.event.internal.DefaultPersistEventListener.onPersist(DefaultPersistEventListener.java:77)
	at org.hibernate.event.internal.DefaultPersistEventListener.onPersist(DefaultPersistEventListener.java:54)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.firePersist(SessionImpl.java:766)
	at org.hibernate.internal.SessionImpl.persist(SessionImpl.java:750)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler.invoke(SharedEntityManagerCreator.java:311)
	at jdk.proxy2/jdk.proxy2.$Proxy123.persist(Unknown Source)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.save(SimpleJpaRepository.java:618)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:277)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:516)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:285)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:628)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:168)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:143)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	... 58 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (22, DEFAULT, 2025-07-02 11:31:10.758493, INVOICE, null, null, null, 2025-07-02 11:31:10.758493, 3, MSG-3edaf855-0ff8-47d8-8d91-7785a456d901, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2713)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2401)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:368)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:498)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:415)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:190)
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:152)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:280)
	... 104 common frames omitted
2025-07-02 11:31:10.766 [http-nio-8081-exec-5] ERROR c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - ❌ Failed to process AS4 message in servlet
com.morohub.apsp.common.exception.AS4Exception: Failed to add message to processing queue: Failed to add message to queue: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (22, DEFAULT, 2025-07-02 11:31:10.758493, INVOICE, null, null, null, 2025-07-02 11:31:10.758493, 3, MSG-3edaf855-0ff8-47d8-8d91-7785a456d901, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
	at com.morohub.apsp.core.service.Phase4AS4ReceiverService.processIncomingAS4MessageContent(Phase4AS4ReceiverService.java:104)
	at com.morohub.apsp.config.Phase4ServletConfiguration$Phase4AS4Servlet.doPost(Phase4ServletConfiguration.java:101)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.RuntimeException: Failed to add message to queue: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (22, DEFAULT, 2025-07-02 11:31:10.758493, INVOICE, null, null, null, 2025-07-02 11:31:10.758493, 3, MSG-3edaf855-0ff8-47d8-8d91-7785a456d901, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
	at com.morohub.apsp.core.service.MessageQueueService.addMessageToQueue(MessageQueueService.java:59)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.MessageQueueService$$SpringCGLIB$$0.addMessageToQueue(<generated>)
	at com.morohub.apsp.core.service.Phase4AS4ReceiverService.processIncomingAS4MessageContent(Phase4AS4ReceiverService.java:95)
	... 36 common frames omitted
Caused by: org.springframework.dao.DataIntegrityViolationException: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (22, DEFAULT, 2025-07-02 11:31:10.758493, INVOICE, null, null, null, 2025-07-02 11:31:10.758493, 3, MSG-3edaf855-0ff8-47d8-8d91-7785a456d901, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:290)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:550)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:335)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:152)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:164)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:249)
	at jdk.proxy2/jdk.proxy2.$Proxy133.save(Unknown Source)
	at com.morohub.apsp.core.service.MessageQueueService.addMessageToQueue(MessageQueueService.java:52)
	... 50 common frames omitted
Caused by: org.hibernate.exception.ConstraintViolationException: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (22, DEFAULT, 2025-07-02 11:31:10.758493, INVOICE, null, null, null, 2025-07-02 11:31:10.758493, 3, MSG-3edaf855-0ff8-47d8-8d91-7785a456d901, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:97)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:283)
	at org.hibernate.id.insert.GetGeneratedKeysDelegate.performInsert(GetGeneratedKeysDelegate.java:107)
	at org.hibernate.engine.jdbc.mutation.internal.MutationExecutorPostInsertSingleTable.execute(MutationExecutorPostInsertSingleTable.java:100)
	at org.hibernate.persister.entity.mutation.InsertCoordinator.doStaticInserts(InsertCoordinator.java:171)
	at org.hibernate.persister.entity.mutation.InsertCoordinator.coordinateInsert(InsertCoordinator.java:112)
	at org.hibernate.persister.entity.AbstractEntityPersister.insert(AbstractEntityPersister.java:2860)
	at org.hibernate.action.internal.EntityIdentityInsertAction.execute(EntityIdentityInsertAction.java:81)
	at org.hibernate.engine.spi.ActionQueue.execute(ActionQueue.java:667)
	at org.hibernate.engine.spi.ActionQueue.addResolvedEntityInsertAction(ActionQueue.java:290)
	at org.hibernate.engine.spi.ActionQueue.addInsertAction(ActionQueue.java:271)
	at org.hibernate.engine.spi.ActionQueue.addAction(ActionQueue.java:321)
	at org.hibernate.event.internal.AbstractSaveEventListener.addInsertAction(AbstractSaveEventListener.java:386)
	at org.hibernate.event.internal.AbstractSaveEventListener.performSaveOrReplicate(AbstractSaveEventListener.java:300)
	at org.hibernate.event.internal.AbstractSaveEventListener.performSave(AbstractSaveEventListener.java:219)
	at org.hibernate.event.internal.AbstractSaveEventListener.saveWithGeneratedId(AbstractSaveEventListener.java:134)
	at org.hibernate.event.internal.DefaultPersistEventListener.entityIsTransient(DefaultPersistEventListener.java:175)
	at org.hibernate.event.internal.DefaultPersistEventListener.persist(DefaultPersistEventListener.java:93)
	at org.hibernate.event.internal.DefaultPersistEventListener.onPersist(DefaultPersistEventListener.java:77)
	at org.hibernate.event.internal.DefaultPersistEventListener.onPersist(DefaultPersistEventListener.java:54)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.firePersist(SessionImpl.java:766)
	at org.hibernate.internal.SessionImpl.persist(SessionImpl.java:750)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler.invoke(SharedEntityManagerCreator.java:311)
	at jdk.proxy2/jdk.proxy2.$Proxy123.persist(Unknown Source)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.save(SimpleJpaRepository.java:618)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:277)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:516)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:285)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:628)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:168)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:143)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	... 58 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (22, DEFAULT, 2025-07-02 11:31:10.758493, INVOICE, null, null, null, 2025-07-02 11:31:10.758493, 3, MSG-3edaf855-0ff8-47d8-8d91-7785a456d901, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2713)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2401)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:368)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:498)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:415)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:190)
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:152)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:280)
	... 104 common frames omitted
2025-07-02 11:31:20.906 [http-nio-8081-exec-9] ERROR o.h.e.jdbc.spi.SqlExceptionHelper - ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (23, DEFAULT, 2025-07-02 11:31:20.903205, INVOICE, null, null, null, 2025-07-02 11:31:20.903205, 3, MSG-c7a05025-e5d2-4621-a677-d5f0b0511d77, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).
2025-07-02 11:31:20.906 [http-nio-8081-exec-9] ERROR c.m.a.c.service.MessageQueueService - ❌ Error adding message to queue: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (23, DEFAULT, 2025-07-02 11:31:20.903205, INVOICE, null, null, null, 2025-07-02 11:31:20.903205, 3, MSG-c7a05025-e5d2-4621-a677-d5f0b0511d77, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
org.springframework.dao.DataIntegrityViolationException: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (23, DEFAULT, 2025-07-02 11:31:20.903205, INVOICE, null, null, null, 2025-07-02 11:31:20.903205, 3, MSG-c7a05025-e5d2-4621-a677-d5f0b0511d77, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:290)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:550)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:335)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:152)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:164)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:249)
	at jdk.proxy2/jdk.proxy2.$Proxy133.save(Unknown Source)
	at com.morohub.apsp.core.service.MessageQueueService.addMessageToQueue(MessageQueueService.java:52)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.MessageQueueService$$SpringCGLIB$$0.addMessageToQueue(<generated>)
	at com.morohub.apsp.core.service.Phase4AS4ReceiverService.processIncomingAS4MessageContent(Phase4AS4ReceiverService.java:95)
	at com.morohub.apsp.config.Phase4ServletConfiguration$Phase4AS4Servlet.doPost(Phase4ServletConfiguration.java:101)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.hibernate.exception.ConstraintViolationException: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (23, DEFAULT, 2025-07-02 11:31:20.903205, INVOICE, null, null, null, 2025-07-02 11:31:20.903205, 3, MSG-c7a05025-e5d2-4621-a677-d5f0b0511d77, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:97)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:283)
	at org.hibernate.id.insert.GetGeneratedKeysDelegate.performInsert(GetGeneratedKeysDelegate.java:107)
	at org.hibernate.engine.jdbc.mutation.internal.MutationExecutorPostInsertSingleTable.execute(MutationExecutorPostInsertSingleTable.java:100)
	at org.hibernate.persister.entity.mutation.InsertCoordinator.doStaticInserts(InsertCoordinator.java:171)
	at org.hibernate.persister.entity.mutation.InsertCoordinator.coordinateInsert(InsertCoordinator.java:112)
	at org.hibernate.persister.entity.AbstractEntityPersister.insert(AbstractEntityPersister.java:2860)
	at org.hibernate.action.internal.EntityIdentityInsertAction.execute(EntityIdentityInsertAction.java:81)
	at org.hibernate.engine.spi.ActionQueue.execute(ActionQueue.java:667)
	at org.hibernate.engine.spi.ActionQueue.addResolvedEntityInsertAction(ActionQueue.java:290)
	at org.hibernate.engine.spi.ActionQueue.addInsertAction(ActionQueue.java:271)
	at org.hibernate.engine.spi.ActionQueue.addAction(ActionQueue.java:321)
	at org.hibernate.event.internal.AbstractSaveEventListener.addInsertAction(AbstractSaveEventListener.java:386)
	at org.hibernate.event.internal.AbstractSaveEventListener.performSaveOrReplicate(AbstractSaveEventListener.java:300)
	at org.hibernate.event.internal.AbstractSaveEventListener.performSave(AbstractSaveEventListener.java:219)
	at org.hibernate.event.internal.AbstractSaveEventListener.saveWithGeneratedId(AbstractSaveEventListener.java:134)
	at org.hibernate.event.internal.DefaultPersistEventListener.entityIsTransient(DefaultPersistEventListener.java:175)
	at org.hibernate.event.internal.DefaultPersistEventListener.persist(DefaultPersistEventListener.java:93)
	at org.hibernate.event.internal.DefaultPersistEventListener.onPersist(DefaultPersistEventListener.java:77)
	at org.hibernate.event.internal.DefaultPersistEventListener.onPersist(DefaultPersistEventListener.java:54)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.firePersist(SessionImpl.java:766)
	at org.hibernate.internal.SessionImpl.persist(SessionImpl.java:750)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler.invoke(SharedEntityManagerCreator.java:311)
	at jdk.proxy2/jdk.proxy2.$Proxy123.persist(Unknown Source)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.save(SimpleJpaRepository.java:618)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:277)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:516)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:285)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:628)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:168)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:143)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	... 58 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (23, DEFAULT, 2025-07-02 11:31:20.903205, INVOICE, null, null, null, 2025-07-02 11:31:20.903205, 3, MSG-c7a05025-e5d2-4621-a677-d5f0b0511d77, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2713)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2401)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:368)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:498)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:415)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:190)
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:152)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:280)
	... 104 common frames omitted
2025-07-02 11:31:20.909 [http-nio-8081-exec-9] ERROR c.m.a.c.s.Phase4AS4ReceiverService - ❌ Failed to add UBL XML to message processing queue: Failed to add message to queue: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (23, DEFAULT, 2025-07-02 11:31:20.903205, INVOICE, null, null, null, 2025-07-02 11:31:20.903205, 3, MSG-c7a05025-e5d2-4621-a677-d5f0b0511d77, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
java.lang.RuntimeException: Failed to add message to queue: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (23, DEFAULT, 2025-07-02 11:31:20.903205, INVOICE, null, null, null, 2025-07-02 11:31:20.903205, 3, MSG-c7a05025-e5d2-4621-a677-d5f0b0511d77, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
	at com.morohub.apsp.core.service.MessageQueueService.addMessageToQueue(MessageQueueService.java:59)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.MessageQueueService$$SpringCGLIB$$0.addMessageToQueue(<generated>)
	at com.morohub.apsp.core.service.Phase4AS4ReceiverService.processIncomingAS4MessageContent(Phase4AS4ReceiverService.java:95)
	at com.morohub.apsp.config.Phase4ServletConfiguration$Phase4AS4Servlet.doPost(Phase4ServletConfiguration.java:101)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: org.springframework.dao.DataIntegrityViolationException: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (23, DEFAULT, 2025-07-02 11:31:20.903205, INVOICE, null, null, null, 2025-07-02 11:31:20.903205, 3, MSG-c7a05025-e5d2-4621-a677-d5f0b0511d77, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:290)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:550)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:335)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:152)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:164)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:249)
	at jdk.proxy2/jdk.proxy2.$Proxy133.save(Unknown Source)
	at com.morohub.apsp.core.service.MessageQueueService.addMessageToQueue(MessageQueueService.java:52)
	... 50 common frames omitted
Caused by: org.hibernate.exception.ConstraintViolationException: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (23, DEFAULT, 2025-07-02 11:31:20.903205, INVOICE, null, null, null, 2025-07-02 11:31:20.903205, 3, MSG-c7a05025-e5d2-4621-a677-d5f0b0511d77, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:97)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:283)
	at org.hibernate.id.insert.GetGeneratedKeysDelegate.performInsert(GetGeneratedKeysDelegate.java:107)
	at org.hibernate.engine.jdbc.mutation.internal.MutationExecutorPostInsertSingleTable.execute(MutationExecutorPostInsertSingleTable.java:100)
	at org.hibernate.persister.entity.mutation.InsertCoordinator.doStaticInserts(InsertCoordinator.java:171)
	at org.hibernate.persister.entity.mutation.InsertCoordinator.coordinateInsert(InsertCoordinator.java:112)
	at org.hibernate.persister.entity.AbstractEntityPersister.insert(AbstractEntityPersister.java:2860)
	at org.hibernate.action.internal.EntityIdentityInsertAction.execute(EntityIdentityInsertAction.java:81)
	at org.hibernate.engine.spi.ActionQueue.execute(ActionQueue.java:667)
	at org.hibernate.engine.spi.ActionQueue.addResolvedEntityInsertAction(ActionQueue.java:290)
	at org.hibernate.engine.spi.ActionQueue.addInsertAction(ActionQueue.java:271)
	at org.hibernate.engine.spi.ActionQueue.addAction(ActionQueue.java:321)
	at org.hibernate.event.internal.AbstractSaveEventListener.addInsertAction(AbstractSaveEventListener.java:386)
	at org.hibernate.event.internal.AbstractSaveEventListener.performSaveOrReplicate(AbstractSaveEventListener.java:300)
	at org.hibernate.event.internal.AbstractSaveEventListener.performSave(AbstractSaveEventListener.java:219)
	at org.hibernate.event.internal.AbstractSaveEventListener.saveWithGeneratedId(AbstractSaveEventListener.java:134)
	at org.hibernate.event.internal.DefaultPersistEventListener.entityIsTransient(DefaultPersistEventListener.java:175)
	at org.hibernate.event.internal.DefaultPersistEventListener.persist(DefaultPersistEventListener.java:93)
	at org.hibernate.event.internal.DefaultPersistEventListener.onPersist(DefaultPersistEventListener.java:77)
	at org.hibernate.event.internal.DefaultPersistEventListener.onPersist(DefaultPersistEventListener.java:54)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.firePersist(SessionImpl.java:766)
	at org.hibernate.internal.SessionImpl.persist(SessionImpl.java:750)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler.invoke(SharedEntityManagerCreator.java:311)
	at jdk.proxy2/jdk.proxy2.$Proxy123.persist(Unknown Source)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.save(SimpleJpaRepository.java:618)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:277)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:516)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:285)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:628)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:168)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:143)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	... 58 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (23, DEFAULT, 2025-07-02 11:31:20.903205, INVOICE, null, null, null, 2025-07-02 11:31:20.903205, 3, MSG-c7a05025-e5d2-4621-a677-d5f0b0511d77, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2713)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2401)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:368)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:498)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:415)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:190)
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:152)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:280)
	... 104 common frames omitted
2025-07-02 11:31:20.910 [http-nio-8081-exec-9] ERROR c.m.a.c.s.Phase4AS4ReceiverService - ❌ AS4 error processing incoming message: Failed to add message to processing queue: Failed to add message to queue: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (23, DEFAULT, 2025-07-02 11:31:20.903205, INVOICE, null, null, null, 2025-07-02 11:31:20.903205, 3, MSG-c7a05025-e5d2-4621-a677-d5f0b0511d77, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
com.morohub.apsp.common.exception.AS4Exception: Failed to add message to processing queue: Failed to add message to queue: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (23, DEFAULT, 2025-07-02 11:31:20.903205, INVOICE, null, null, null, 2025-07-02 11:31:20.903205, 3, MSG-c7a05025-e5d2-4621-a677-d5f0b0511d77, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
	at com.morohub.apsp.core.service.Phase4AS4ReceiverService.processIncomingAS4MessageContent(Phase4AS4ReceiverService.java:104)
	at com.morohub.apsp.config.Phase4ServletConfiguration$Phase4AS4Servlet.doPost(Phase4ServletConfiguration.java:101)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.RuntimeException: Failed to add message to queue: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (23, DEFAULT, 2025-07-02 11:31:20.903205, INVOICE, null, null, null, 2025-07-02 11:31:20.903205, 3, MSG-c7a05025-e5d2-4621-a677-d5f0b0511d77, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
	at com.morohub.apsp.core.service.MessageQueueService.addMessageToQueue(MessageQueueService.java:59)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.MessageQueueService$$SpringCGLIB$$0.addMessageToQueue(<generated>)
	at com.morohub.apsp.core.service.Phase4AS4ReceiverService.processIncomingAS4MessageContent(Phase4AS4ReceiverService.java:95)
	... 36 common frames omitted
Caused by: org.springframework.dao.DataIntegrityViolationException: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (23, DEFAULT, 2025-07-02 11:31:20.903205, INVOICE, null, null, null, 2025-07-02 11:31:20.903205, 3, MSG-c7a05025-e5d2-4621-a677-d5f0b0511d77, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:290)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:550)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:335)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:152)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:164)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:249)
	at jdk.proxy2/jdk.proxy2.$Proxy133.save(Unknown Source)
	at com.morohub.apsp.core.service.MessageQueueService.addMessageToQueue(MessageQueueService.java:52)
	... 50 common frames omitted
Caused by: org.hibernate.exception.ConstraintViolationException: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (23, DEFAULT, 2025-07-02 11:31:20.903205, INVOICE, null, null, null, 2025-07-02 11:31:20.903205, 3, MSG-c7a05025-e5d2-4621-a677-d5f0b0511d77, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:97)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:283)
	at org.hibernate.id.insert.GetGeneratedKeysDelegate.performInsert(GetGeneratedKeysDelegate.java:107)
	at org.hibernate.engine.jdbc.mutation.internal.MutationExecutorPostInsertSingleTable.execute(MutationExecutorPostInsertSingleTable.java:100)
	at org.hibernate.persister.entity.mutation.InsertCoordinator.doStaticInserts(InsertCoordinator.java:171)
	at org.hibernate.persister.entity.mutation.InsertCoordinator.coordinateInsert(InsertCoordinator.java:112)
	at org.hibernate.persister.entity.AbstractEntityPersister.insert(AbstractEntityPersister.java:2860)
	at org.hibernate.action.internal.EntityIdentityInsertAction.execute(EntityIdentityInsertAction.java:81)
	at org.hibernate.engine.spi.ActionQueue.execute(ActionQueue.java:667)
	at org.hibernate.engine.spi.ActionQueue.addResolvedEntityInsertAction(ActionQueue.java:290)
	at org.hibernate.engine.spi.ActionQueue.addInsertAction(ActionQueue.java:271)
	at org.hibernate.engine.spi.ActionQueue.addAction(ActionQueue.java:321)
	at org.hibernate.event.internal.AbstractSaveEventListener.addInsertAction(AbstractSaveEventListener.java:386)
	at org.hibernate.event.internal.AbstractSaveEventListener.performSaveOrReplicate(AbstractSaveEventListener.java:300)
	at org.hibernate.event.internal.AbstractSaveEventListener.performSave(AbstractSaveEventListener.java:219)
	at org.hibernate.event.internal.AbstractSaveEventListener.saveWithGeneratedId(AbstractSaveEventListener.java:134)
	at org.hibernate.event.internal.DefaultPersistEventListener.entityIsTransient(DefaultPersistEventListener.java:175)
	at org.hibernate.event.internal.DefaultPersistEventListener.persist(DefaultPersistEventListener.java:93)
	at org.hibernate.event.internal.DefaultPersistEventListener.onPersist(DefaultPersistEventListener.java:77)
	at org.hibernate.event.internal.DefaultPersistEventListener.onPersist(DefaultPersistEventListener.java:54)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.firePersist(SessionImpl.java:766)
	at org.hibernate.internal.SessionImpl.persist(SessionImpl.java:750)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler.invoke(SharedEntityManagerCreator.java:311)
	at jdk.proxy2/jdk.proxy2.$Proxy123.persist(Unknown Source)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.save(SimpleJpaRepository.java:618)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:277)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:516)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:285)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:628)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:168)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:143)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	... 58 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (23, DEFAULT, 2025-07-02 11:31:20.903205, INVOICE, null, null, null, 2025-07-02 11:31:20.903205, 3, MSG-c7a05025-e5d2-4621-a677-d5f0b0511d77, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2713)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2401)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:368)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:498)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:415)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:190)
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:152)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:280)
	... 104 common frames omitted
2025-07-02 11:31:20.911 [http-nio-8081-exec-9] ERROR c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - ❌ Failed to process AS4 message in servlet
com.morohub.apsp.common.exception.AS4Exception: Failed to add message to processing queue: Failed to add message to queue: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (23, DEFAULT, 2025-07-02 11:31:20.903205, INVOICE, null, null, null, 2025-07-02 11:31:20.903205, 3, MSG-c7a05025-e5d2-4621-a677-d5f0b0511d77, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
	at com.morohub.apsp.core.service.Phase4AS4ReceiverService.processIncomingAS4MessageContent(Phase4AS4ReceiverService.java:104)
	at com.morohub.apsp.config.Phase4ServletConfiguration$Phase4AS4Servlet.doPost(Phase4ServletConfiguration.java:101)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: java.lang.RuntimeException: Failed to add message to queue: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (23, DEFAULT, 2025-07-02 11:31:20.903205, INVOICE, null, null, null, 2025-07-02 11:31:20.903205, 3, MSG-c7a05025-e5d2-4621-a677-d5f0b0511d77, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
	at com.morohub.apsp.core.service.MessageQueueService.addMessageToQueue(MessageQueueService.java:59)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.MessageQueueService$$SpringCGLIB$$0.addMessageToQueue(<generated>)
	at com.morohub.apsp.core.service.Phase4AS4ReceiverService.processIncomingAS4MessageContent(Phase4AS4ReceiverService.java:95)
	... 36 common frames omitted
Caused by: org.springframework.dao.DataIntegrityViolationException: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (23, DEFAULT, 2025-07-02 11:31:20.903205, INVOICE, null, null, null, 2025-07-02 11:31:20.903205, 3, MSG-c7a05025-e5d2-4621-a677-d5f0b0511d77, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type" of relation "message_processing_queue]
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.convertHibernateAccessException(HibernateJpaDialect.java:290)
	at org.springframework.orm.jpa.vendor.HibernateJpaDialect.translateExceptionIfPossible(HibernateJpaDialect.java:241)
	at org.springframework.orm.jpa.AbstractEntityManagerFactoryBean.translateExceptionIfPossible(AbstractEntityManagerFactoryBean.java:550)
	at org.springframework.dao.support.ChainedPersistenceExceptionTranslator.translateExceptionIfPossible(ChainedPersistenceExceptionTranslator.java:61)
	at org.springframework.dao.support.DataAccessUtils.translateIfNecessary(DataAccessUtils.java:335)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:152)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.jpa.repository.support.CrudMethodMetadataPostProcessor$CrudMethodMetadataPopulatingMethodInterceptor.invoke(CrudMethodMetadataPostProcessor.java:164)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.interceptor.ExposeInvocationInterceptor.invoke(ExposeInvocationInterceptor.java:97)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:249)
	at jdk.proxy2/jdk.proxy2.$Proxy133.save(Unknown Source)
	at com.morohub.apsp.core.service.MessageQueueService.addMessageToQueue(MessageQueueService.java:52)
	... 50 common frames omitted
Caused by: org.hibernate.exception.ConstraintViolationException: could not execute statement [ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (23, DEFAULT, 2025-07-02 11:31:20.903205, INVOICE, null, null, null, 2025-07-02 11:31:20.903205, 3, MSG-c7a05025-e5d2-4621-a677-d5f0b0511d77, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]
	at org.hibernate.exception.internal.SQLStateConversionDelegate.convert(SQLStateConversionDelegate.java:97)
	at org.hibernate.exception.internal.StandardSQLExceptionConverter.convert(StandardSQLExceptionConverter.java:58)
	at org.hibernate.engine.jdbc.spi.SqlExceptionHelper.convert(SqlExceptionHelper.java:108)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:283)
	at org.hibernate.id.insert.GetGeneratedKeysDelegate.performInsert(GetGeneratedKeysDelegate.java:107)
	at org.hibernate.engine.jdbc.mutation.internal.MutationExecutorPostInsertSingleTable.execute(MutationExecutorPostInsertSingleTable.java:100)
	at org.hibernate.persister.entity.mutation.InsertCoordinator.doStaticInserts(InsertCoordinator.java:171)
	at org.hibernate.persister.entity.mutation.InsertCoordinator.coordinateInsert(InsertCoordinator.java:112)
	at org.hibernate.persister.entity.AbstractEntityPersister.insert(AbstractEntityPersister.java:2860)
	at org.hibernate.action.internal.EntityIdentityInsertAction.execute(EntityIdentityInsertAction.java:81)
	at org.hibernate.engine.spi.ActionQueue.execute(ActionQueue.java:667)
	at org.hibernate.engine.spi.ActionQueue.addResolvedEntityInsertAction(ActionQueue.java:290)
	at org.hibernate.engine.spi.ActionQueue.addInsertAction(ActionQueue.java:271)
	at org.hibernate.engine.spi.ActionQueue.addAction(ActionQueue.java:321)
	at org.hibernate.event.internal.AbstractSaveEventListener.addInsertAction(AbstractSaveEventListener.java:386)
	at org.hibernate.event.internal.AbstractSaveEventListener.performSaveOrReplicate(AbstractSaveEventListener.java:300)
	at org.hibernate.event.internal.AbstractSaveEventListener.performSave(AbstractSaveEventListener.java:219)
	at org.hibernate.event.internal.AbstractSaveEventListener.saveWithGeneratedId(AbstractSaveEventListener.java:134)
	at org.hibernate.event.internal.DefaultPersistEventListener.entityIsTransient(DefaultPersistEventListener.java:175)
	at org.hibernate.event.internal.DefaultPersistEventListener.persist(DefaultPersistEventListener.java:93)
	at org.hibernate.event.internal.DefaultPersistEventListener.onPersist(DefaultPersistEventListener.java:77)
	at org.hibernate.event.internal.DefaultPersistEventListener.onPersist(DefaultPersistEventListener.java:54)
	at org.hibernate.event.service.internal.EventListenerGroupImpl.fireEventOnEachListener(EventListenerGroupImpl.java:127)
	at org.hibernate.internal.SessionImpl.firePersist(SessionImpl.java:766)
	at org.hibernate.internal.SessionImpl.persist(SessionImpl.java:750)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.orm.jpa.SharedEntityManagerCreator$SharedEntityManagerInvocationHandler.invoke(SharedEntityManagerCreator.java:311)
	at jdk.proxy2/jdk.proxy2.$Proxy123.persist(Unknown Source)
	at org.springframework.data.jpa.repository.support.SimpleJpaRepository.save(SimpleJpaRepository.java:618)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker$RepositoryFragmentMethodInvoker.lambda$new$0(RepositoryMethodInvoker.java:277)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.doInvoke(RepositoryMethodInvoker.java:170)
	at org.springframework.data.repository.core.support.RepositoryMethodInvoker.invoke(RepositoryMethodInvoker.java:158)
	at org.springframework.data.repository.core.support.RepositoryComposition$RepositoryFragments.invoke(RepositoryComposition.java:516)
	at org.springframework.data.repository.core.support.RepositoryComposition.invoke(RepositoryComposition.java:285)
	at org.springframework.data.repository.core.support.RepositoryFactorySupport$ImplementationMethodExecutionInterceptor.invoke(RepositoryFactorySupport.java:628)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.doInvoke(QueryExecutorMethodInterceptor.java:168)
	at org.springframework.data.repository.core.support.QueryExecutorMethodInterceptor.invoke(QueryExecutorMethodInterceptor.java:143)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.data.projection.DefaultMethodInvokingMethodInterceptor.invoke(DefaultMethodInvokingMethodInterceptor.java:70)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)
	... 58 common frames omitted
Caused by: org.postgresql.util.PSQLException: ERROR: null value in column "flow_type" of relation "message_processing_queue" violates not-null constraint
  Detail: Failing row contains (23, DEFAULT, 2025-07-02 11:31:20.903205, INVOICE, null, null, null, 2025-07-02 11:31:20.903205, 3, MSG-c7a05025-e5d2-4621-a677-d5f0b0511d77, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, <BusinessScope 
                  >
   <Scope>
      <Type>DOCUM..., null, null, null, null).
	at org.postgresql.core.v3.QueryExecutorImpl.receiveErrorResponse(QueryExecutorImpl.java:2713)
	at org.postgresql.core.v3.QueryExecutorImpl.processResults(QueryExecutorImpl.java:2401)
	at org.postgresql.core.v3.QueryExecutorImpl.execute(QueryExecutorImpl.java:368)
	at org.postgresql.jdbc.PgStatement.executeInternal(PgStatement.java:498)
	at org.postgresql.jdbc.PgStatement.execute(PgStatement.java:415)
	at org.postgresql.jdbc.PgPreparedStatement.executeWithFlags(PgPreparedStatement.java:190)
	at org.postgresql.jdbc.PgPreparedStatement.executeUpdate(PgPreparedStatement.java:152)
	at com.zaxxer.hikari.pool.ProxyPreparedStatement.executeUpdate(ProxyPreparedStatement.java:61)
	at com.zaxxer.hikari.pool.HikariProxyPreparedStatement.executeUpdate(HikariProxyPreparedStatement.java)
	at org.hibernate.engine.jdbc.internal.ResultSetReturnImpl.executeUpdate(ResultSetReturnImpl.java:280)
	... 104 common frames omitted
2025-07-02 11:31:20.915 [http-nio-8081-exec-3] ERROR c.h.p.s.AbstractAS4UserMessageBuilder - Exception sending AS4 user message
com.helger.phase4.util.Phase4Exception: Wrapped Phase4Exception
	at com.helger.phase4.sender.AbstractAS4UserMessageBuilderMIMEPayload.mainSendMessage(AbstractAS4UserMessageBuilderMIMEPayload.java:237)
	at com.helger.phase4.sender.AbstractAS4MessageBuilder.sendMessage(AbstractAS4MessageBuilder.java:856)
	at com.helger.phase4.sender.AbstractAS4UserMessageBuilder.sendMessageAndCheckForReceipt(AbstractAS4UserMessageBuilder.java:798)
	at com.helger.phase4.sender.AbstractAS4UserMessageBuilder.sendMessageAndCheckForReceipt(AbstractAS4UserMessageBuilder.java:764)
	at com.morohub.apsp.core.service.AS4ConversionService.processAs4message(AS4ConversionService.java:902)
	at com.morohub.apsp.core.service.AS4ConversionService.sendAS4Message(AS4ConversionService.java:824)
	at com.morohub.apsp.core.service.AS4ConversionService.convertAndSend(AS4ConversionService.java:250)
	at com.morohub.apsp.api.controller.XmlValidationController.validateAndConvertToAs4(XmlValidationController.java:78)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:254)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:182)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
Caused by: com.helger.httpclient.response.ExtendedHttpResponseException:  [500]
All 4 headers returned
  Content-Type=application/soap+xml;charset=UTF-8
  Content-Length=2249
  Date=Wed, 02 Jul 2025 06:01:20 GMT
  Connection=close
Response Body (in UTF-8):
<?xml version="1.0" encoding="UTF-8"?>
<soap:Envelope xmlns:soap="http://www.w3.org/2003/05/soap-envelope" xmlns:eb="http://docs.oasis-open.org/ebxml-msg/ebms/v3.0/ns/core/200704/">
  <soap:Header>
    <eb:Messaging soap:mustUnderstand="true">
      <eb:SignalMessage>
        <eb:MessageInfo>
          <eb:Timestamp>2025-07-02T06:01:20.912204300Z</eb:Timestamp>
          <eb:MessageId>b88376dd-efdc-4066-8bf6-915efd640a92</eb:MessageId>
        </eb:MessageInfo>
        <eb:Error errorCode="EBMS:0004" severity="failure" shortDescription="Other">
          <eb:Description xml:lang="en">Failed to add message to processing queue: Failed to add message to queue: could not execute statement [ERROR: null value in column &quot;flow_type&quot; of relation &quot;message_processing_queue&quot; violates not-null constraint
  Detail: Failing row contains (23, DEFAULT, 2025-07-02 11:31:20.903205, INVOICE, null, null, null, 2025-07-02 11:31:20.903205, 3, MSG-c7a05025-e5d2-4621-a677-d5f0b0511d77, null, 9908:receiver-default, 0, 9908:sender-default, READY_TO_SEND, &lt;Invoice xmlns=&quot;urn:oasis:names:specification:ubl:schema:xsd:Inv..., e00f27e7-27ca-4fae-a349-cc6b71379e32, &lt;BusinessScope 
                  &gt;
   &lt;Scope&gt;
      &lt;Type&gt;DOCUM..., null, null, null, null).] [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; SQL [insert into message_processing_queue (country_code,created_date,document_type,endpoint_url,error_message,flow_type,last_processed,last_updated,max_retries,message_id,mls_message_id,original_document_instance_id,original_sbdh_business_scope,receiver_participant_id,request_id,retry_count,sender_participant_id,status,ubl_xml) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)]; constraint [flow_type&quot; of relation &quot;message_processing_queue]</eb:Description>
        </eb:Error>
      </eb:SignalMessage>
    </eb:Messaging>
  </soap:Header>
  <soap:Body/>
</soap:Envelope>
	at com.helger.httpclient.response.ExtendedHttpResponseException.create(ExtendedHttpResponseException.java:211)
	at com.helger.httpclient.response.ResponseHandlerHttpEntity.handleResponse(ResponseHandlerHttpEntity.java:55)
	at com.helger.phase4.sender.AS4BidirectionalClientHelper.lambda$sendAS4UserMessageAndReceiveAS4SignalMessage$0(AS4BidirectionalClientHelper.java:125)
	at com.helger.phase4.client.AbstractAS4Client.lambda$sendMessageWithRetries$1(AbstractAS4Client.java:589)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:247)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:188)
	at com.helger.httpclient.HttpClientManager.execute(HttpClientManager.java:137)
	at com.helger.httpclient.HttpClientManager.execute(HttpClientManager.java:102)
	at com.helger.phase4.http.BasicHttpPoster.sendGenericMessage(BasicHttpPoster.java:201)
	at com.helger.phase4.http.BasicHttpPoster.sendGenericMessageWithRetries(BasicHttpPoster.java:317)
	at com.helger.phase4.client.AbstractAS4Client.sendMessageWithRetries(AbstractAS4Client.java:591)
	at com.helger.phase4.sender.AS4BidirectionalClientHelper.sendAS4UserMessageAndReceiveAS4SignalMessage(AS4BidirectionalClientHelper.java:137)
	at com.helger.phase4.sender.AbstractAS4UserMessageBuilderMIMEPayload.mainSendMessage(AbstractAS4UserMessageBuilderMIMEPayload.java:211)
	... 55 common frames omitted
2025-07-02 11:31:20.916 [http-nio-8081-exec-3] ERROR c.m.a.c.service.AS4ConversionService - ❌ Production AS4 send failed: TRANSPORT_ERROR
2025-07-02 11:31:20.916 [http-nio-8081-exec-3] ERROR c.m.a.a.c.XmlValidationController - ❌ AS4 conversion failed for request: 9ba43a43-7e8a-4750-b7df-3d987e682c08: Production AS4 send failed: TRANSPORT_ERROR
2025-07-02 11:31:20.934 [http-nio-8081-exec-3] ERROR c.m.a.config.GlobalExceptionHandler - AS4Exception handled: AS4_001 - AS4 conversion failed: Production AS4 send failed: TRANSPORT_ERROR
2025-07-02 12:19:35.650 [pool-2-thread-1] ERROR c.h.s.h.AbstractGenericSMPClient - Error performing SMP query
java.net.UnknownHostException: This is usually a temporary error during hostname resolution and means that the local server did not receive a response from an authoritative server (smp.peppolcentral.org)
	at java.base/java.net.Inet6AddressImpl.lookupAllHostAddr(Native Method)
	at java.base/java.net.Inet6AddressImpl.lookupAllHostAddr(Inet6AddressImpl.java:52)
	at java.base/java.net.InetAddress$PlatformResolver.lookupByName(InetAddress.java:1211)
	at java.base/java.net.InetAddress.getAddressesFromNameService(InetAddress.java:1828)
	at java.base/java.net.InetAddress$NameServiceAddresses.get(InetAddress.java:1139)
	at java.base/java.net.InetAddress.getAllByName0(InetAddress.java:1818)
	at java.base/java.net.InetAddress.getAllByName(InetAddress.java:1688)
	at org.apache.hc.client5.http.SystemDefaultDnsResolver.resolve(SystemDefaultDnsResolver.java:43)
	at org.apache.hc.client5.http.impl.io.DefaultHttpClientConnectionOperator.connect(DefaultHttpClientConnectionOperator.java:143)
	at org.apache.hc.client5.http.impl.io.PoolingHttpClientConnectionManager.connect(PoolingHttpClientConnectionManager.java:447)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:162)
	at org.apache.hc.client5.http.impl.classic.InternalExecRuntime.connectEndpoint(InternalExecRuntime.java:172)
	at org.apache.hc.client5.http.impl.classic.ConnectExec.execute(ConnectExec.java:142)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ProtocolExec.execute(ProtocolExec.java:192)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.HttpRequestRetryExec.execute(HttpRequestRetryExec.java:96)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.ContentCompressionExec.execute(ContentCompressionExec.java:152)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.RedirectExec.execute(RedirectExec.java:115)
	at org.apache.hc.client5.http.impl.classic.ExecChainElement.execute(ExecChainElement.java:51)
	at org.apache.hc.client5.http.impl.classic.InternalHttpClient.doExecute(InternalHttpClient.java:170)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:245)
	at org.apache.hc.client5.http.impl.classic.CloseableHttpClient.execute(CloseableHttpClient.java:188)
	at com.helger.httpclient.HttpClientManager.execute(HttpClientManager.java:137)
	at com.helger.smpclient.httpclient.AbstractGenericSMPClient.executeRequest(AbstractGenericSMPClient.java:363)
	at com.helger.smpclient.httpclient.AbstractGenericSMPClient.executeGenericRequest(AbstractGenericSMPClient.java:401)
	at com.helger.smpclient.peppol.SMPClientReadOnly.getServiceMetadata(SMPClientReadOnly.java:551)
	at com.helger.smpclient.peppol.SMPClientReadOnly.getServiceMetadataOrNull(SMPClientReadOnly.java:639)
	at com.helger.smpclient.peppol.ISMPServiceMetadataProvider.getEndpointAt(ISMPServiceMetadataProvider.java:180)
	at com.helger.smpclient.peppol.ISMPServiceMetadataProvider.getEndpoint(ISMPServiceMetadataProvider.java:126)
	at com.helger.phase4.dynamicdiscovery.AS4EndpointDetailProviderPeppol.init(AS4EndpointDetailProviderPeppol.java:188)
	at com.morohub.apsp.core.service.SMPLookupService.performMLSSMPLookup(SMPLookupService.java:92)
	at com.morohub.apsp.core.service.MessageProcessingService.processMlsMessage(MessageProcessingService.java:190)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:135)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:102)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 12:20:07.096 [pool-2-thread-1] ERROR c.m.a.core.service.SMPLookupService - ❌ MLS SMP lookup failed for participant 9908:receiver-default: Failed to resolve SMP endpoint (iso6523-actorid-upis::receiver-default, busdox-docid-qns::urn:oasis:names:specification:ubl:schema:xsd:ApplicationResponse-2::ApplicationResponse##urn:fdc:peppol.eu:poacc:trns:mls:3::2.1, cenbii-procid-ubl::urn:fdc:peppol.eu:2017:poacc:mls:01:1.0, peppol-transport-as4-v2_0) [static]
com.helger.phase4.dynamicdiscovery.Phase4SMPException: Failed to resolve SMP endpoint (iso6523-actorid-upis::receiver-default, busdox-docid-qns::urn:oasis:names:specification:ubl:schema:xsd:ApplicationResponse-2::ApplicationResponse##urn:fdc:peppol.eu:poacc:trns:mls:3::2.1, cenbii-procid-ubl::urn:fdc:peppol.eu:2017:poacc:mls:01:1.0, peppol-transport-as4-v2_0) [static]
	at com.helger.phase4.dynamicdiscovery.AS4EndpointDetailProviderPeppol.init(AS4EndpointDetailProviderPeppol.java:200)
	at com.morohub.apsp.core.service.SMPLookupService.performMLSSMPLookup(SMPLookupService.java:92)
	at com.morohub.apsp.core.service.MessageProcessingService.processMlsMessage(MessageProcessingService.java:190)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:135)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:102)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run$$$capture(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 12:20:11.461 [pool-2-thread-1] ERROR c.m.a.c.s.MessageProcessingService - ❌ MLS SMP lookup failed for message MSG-feebe386-ee2c-457a-b05d-4a12b18b63b2: MLS SMP lookup failed: Failed to resolve SMP endpoint (iso6523-actorid-upis::receiver-default, busdox-docid-qns::urn:oasis:names:specification:ubl:schema:xsd:ApplicationResponse-2::ApplicationResponse##urn:fdc:peppol.eu:poacc:trns:mls:3::2.1, cenbii-procid-ubl::urn:fdc:peppol.eu:2017:poacc:mls:01:1.0, peppol-transport-as4-v2_0) [static]
