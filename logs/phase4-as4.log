2025-07-02 10:29:07.785 [main] INFO  com.morohub.apsp.AS4Application - Starting AS4Application using Java 21.0.3 with PID 4476 (C:\Work\as4-main\target\classes started by kushagrat in C:\Work\as4-main)
2025-07-02 10:29:07.910 [main] DEBUG com.morohub.apsp.AS4Application - Running with Spring Boot v3.2.0, Spring v6.1.0
2025-07-02 10:29:07.910 [main] INFO  com.morohub.apsp.AS4Application - The following 1 profile is active: "dummy"
2025-07-02 10:29:10.553 [main] INFO  c.m.a.c.Phase4ServletConfiguration - 🔧 Registering AS4 servlet for incoming message handling
2025-07-02 10:29:10.553 [main] INFO  c.m.a.c.Phase4ServletConfiguration - ✅ AS4 servlet registered at /reverse-flow/as4/*
2025-07-02 10:29:16.123 [main] INFO  c.m.a.config.AS4CryptoConfiguration - === Creating Configured AS4 Crypto Factory ===
2025-07-02 10:29:16.124 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:29:16.127 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:29:16.127 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:29:16.128 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:29:16.161 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Keystore loaded from: classpath: keystore/cert.p12
2025-07-02 10:29:16.161 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:29:16.162 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:29:16.162 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:29:16.162 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:29:16.170 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Truststore loaded from: classpath: keystore/cert.p12
2025-07-02 10:29:16.171 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Truststore loaded from: classpath: keystore/cert.p12
2025-07-02 10:29:16.176 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - Validating crypto configuration...
2025-07-02 10:29:16.177 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:29:16.177 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:29:16.207 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Crypto configuration validation successful
2025-07-02 10:29:16.207 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 🔑 Private key algorithm: RSA
2025-07-02 10:29:16.207 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 📜 Certificate subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE
2025-07-02 10:29:16.208 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 📅 Certificate valid from: Wed Jul 10 05:30:00 IST 2024 to: Wed Jul 01 05:29:59 IST 2026
2025-07-02 10:29:16.208 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ AS4 Crypto Factory configured successfully and set as default
2025-07-02 10:29:16.209 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 📁 Keystore: cert.p12
2025-07-02 10:29:16.209 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 🔑 Key Alias: cert
2025-07-02 10:29:16.209 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 🛡️ Truststore: cert.p12
2025-07-02 10:29:16.298 [main] INFO  c.m.a.c.CountryConfigurationService - Loading unified country configuration from: country-config.json
2025-07-02 10:29:16.299 [main] INFO  c.m.a.c.CountryConfigurationService - Resource location: classpath: country-config.json
2025-07-02 10:29:16.299 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: country-config.json
2025-07-02 10:29:16.300 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: country-config.json
2025-07-02 10:29:16.329 [main] INFO  c.m.a.c.CountryConfigurationService - ✅ Unified country configuration loaded successfully
2025-07-02 10:29:16.330 [main] INFO  c.m.a.c.CountryConfigurationService - 📊 Available countries: DE, FR, IT, NL, ES, DEFAULT
2025-07-02 10:29:16.430 [main] INFO  c.m.a.c.CountryConfigurationService - 📋 Configuration Summary:
2025-07-02 10:29:16.431 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 DE (Germany): 3 document types
2025-07-02 10:29:16.432 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:29:16.432 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:29:16.432 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:29:16.433 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 FR (France): 3 document types
2025-07-02 10:29:16.433 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-FR.sch
2025-07-02 10:29:16.433 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-FR.sch
2025-07-02 10:29:16.433 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-FR.sch
2025-07-02 10:29:16.433 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 IT (Italy): 3 document types
2025-07-02 10:29:16.433 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-IT.sch
2025-07-02 10:29:16.434 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-IT.sch
2025-07-02 10:29:16.434 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-IT.sch
2025-07-02 10:29:16.434 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 NL (Netherlands): 3 document types
2025-07-02 10:29:16.434 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-NL.sch
2025-07-02 10:29:16.435 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-NL.sch
2025-07-02 10:29:16.435 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-NL.sch
2025-07-02 10:29:16.435 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 ES (Spain): 3 document types
2025-07-02 10:29:16.435 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-ES.sch
2025-07-02 10:29:16.435 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-ES.sch
2025-07-02 10:29:16.435 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-ES.sch
2025-07-02 10:29:16.435 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 DEFAULT (Default/Generic): 3 document types
2025-07-02 10:29:16.435 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | null
2025-07-02 10:29:16.435 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:29:16.436 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:29:16.437 [main] INFO  c.m.a.c.service.AS4ConversionService - === Initializing Production-Ready AS4 Service ===
2025-07-02 10:29:16.437 [main] INFO  c.m.a.c.service.AS4ConversionService - AS4 Mode: dummy
2025-07-02 10:29:16.437 [main] INFO  c.m.a.c.service.AS4ConversionService - Security Enabled: true
2025-07-02 10:29:16.437 [main] INFO  c.m.a.c.service.AS4ConversionService - Validation Enabled: false
2025-07-02 10:29:16.437 [main] INFO  c.m.a.c.service.AS4ConversionService - Endpoint: http://localhost:8081/reverse-flow/receive-as4-message
2025-07-02 10:29:16.437 [main] INFO  c.m.a.c.service.AS4ConversionService - === Checking Keystore Availability ===
2025-07-02 10:29:16.437 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:29:16.438 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:29:16.438 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Keystore found at: classpath: keystore/cert.p12
2025-07-02 10:29:16.438 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:29:16.439 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:29:16.444 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Private key accessible for alias: cert
2025-07-02 10:29:16.444 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Keystore validation successful
2025-07-02 10:29:16.444 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:29:16.445 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:29:16.445 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Truststore found at: classpath: keystore/cert.p12
2025-07-02 10:29:16.477 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Phase4 global scope initialized
2025-07-02 10:29:16.477 [main] INFO  c.m.a.c.service.AS4ConversionService - === Configuring Phase4 Crypto Settings ===
2025-07-02 10:29:16.477 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:29:16.477 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:29:16.492 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Crypto configuration is properly set up
2025-07-02 10:29:16.492 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:29:16.492 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:29:16.492 [main] INFO  c.m.a.c.service.AS4ConversionService - 📁 Keystore info: Keystore: cert.p12, Alias: cert, Subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE, Valid: Wed Jul 10 05:30:00 IST 2024 to Wed Jul 01 05:29:59 IST 2026
2025-07-02 10:29:16.654 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ MetaAS4Manager initialized
2025-07-02 10:29:16.654 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 service initialized successfully for dummy mode
2025-07-02 10:29:17.000 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-02 10:29:17.539 [main] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 🔧 Initializing AS4 Servlet for message processing
2025-07-02 10:29:17.539 [main] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - ✅ AS4 Servlet initialized successfully
2025-07-02 10:29:17.555 [main] INFO  com.morohub.apsp.AS4Application - Started AS4Application in 10.724 seconds (process running for 11.453)
2025-07-02 10:29:17.555 [main] INFO  c.m.a.c.ErrorMasterDataInitializer - 🔧 Initializing error master data...
2025-07-02 10:29:17.569 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 🔄 Starting message processing cycle
2025-07-02 10:29:17.679 [scheduling-1] DEBUG c.m.a.c.service.MessageQueueService - 📋 Found 0 messages ready for processing
2025-07-02 10:29:17.683 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_001
2025-07-02 10:29:17.684 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 📋 No messages ready for processing
2025-07-02 10:29:17.685 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - 🔄 Processing retry queue...
2025-07-02 10:29:17.686 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_002
2025-07-02 10:29:17.689 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_003
2025-07-02 10:29:17.691 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_004
2025-07-02 10:29:17.695 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_001
2025-07-02 10:29:17.696 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_002
2025-07-02 10:29:17.696 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_003
2025-07-02 10:29:17.696 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - No errors ready for retry
2025-07-02 10:29:17.696 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_004
2025-07-02 10:29:17.696 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_001
2025-07-02 10:29:17.696 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_002
2025-07-02 10:29:17.696 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_003
2025-07-02 10:29:17.696 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_004
2025-07-02 10:29:17.712 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_005
2025-07-02 10:29:17.712 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: SYS_001
2025-07-02 10:29:17.712 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: SYS_002
2025-07-02 10:29:17.712 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: SYS_003
2025-07-02 10:29:17.712 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_001
2025-07-02 10:29:17.712 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_002
2025-07-02 10:29:17.727 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_003
2025-07-02 10:29:17.727 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_004
2025-07-02 10:29:17.727 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: PEPPOL_001
2025-07-02 10:29:17.727 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: PEPPOL_002
2025-07-02 10:29:17.727 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: PEPPOL_003
2025-07-02 10:29:17.727 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: MLS_001
2025-07-02 10:29:17.727 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: MLS_002
2025-07-02 10:29:17.727 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: RATE_001
2025-07-02 10:29:17.743 [main] INFO  c.m.a.c.ErrorMasterDataInitializer - Initialized 26 error master data entries
2025-07-02 10:29:17.743 [main] INFO  c.m.a.c.ErrorMasterDataInitializer - ✅ Error master data initialization completed
2025-07-02 10:29:38.600 [http-nio-8081-exec-1] INFO  c.m.a.a.c.PeppolSbdInvoiceController - Processing request for country: DEFAULT and document type: INVOICE
2025-07-02 10:29:38.602 [http-nio-8081-exec-1] INFO  c.m.a.a.c.PeppolSbdInvoiceController - Using UBL class: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType for DEFAULT/INVOICE
2025-07-02 10:29:39.993 [http-nio-8081-exec-1] INFO  c.m.apsp.core.service.InvoiceService - Generating invoice XML for country: DEFAULT and document type: INVOICE
2025-07-02 10:29:39.994 [http-nio-8081-exec-1] DEBUG c.m.a.c.service.XmlGeneratorService - Generating invoice XML using country-specific configuration: DEFAULT/INVOICE
2025-07-02 10:29:39.995 [http-nio-8081-exec-1] INFO  c.m.a.c.service.XmlGeneratorService - Using classes for DEFAULT/INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType and oasis.names.specification.ubl.schema.xsd.invoice_2.ObjectFactory
2025-07-02 10:29:39.996 [http-nio-8081-exec-1] DEBUG c.m.a.c.s.XmlJsonConversionService - 🔍 Creating UBL JAXB context for package: oasis.names.specification.ubl.schema.xsd.invoice_2
2025-07-02 10:29:39.997 [http-nio-8081-exec-1] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.invoice_2
2025-07-02 10:29:40.033 [http-nio-8081-exec-1] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.commonaggregatecomponents_2
2025-07-02 10:29:40.117 [http-nio-8081-exec-1] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.commonbasiccomponents_2
2025-07-02 10:29:40.119 [http-nio-8081-exec-1] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.commonextensioncomponents_2
2025-07-02 10:29:40.119 [http-nio-8081-exec-1] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.creditnote_2
2025-07-02 10:29:40.119 [http-nio-8081-exec-1] DEBUG c.m.a.c.s.XmlJsonConversionService - 🔍 Using UBL context path: oasis.names.specification.ubl.schema.xsd.invoice_2:oasis.names.specification.ubl.schema.xsd.commonaggregatecomponents_2:oasis.names.specification.ubl.schema.xsd.commonbasiccomponents_2:oasis.names.specification.ubl.schema.xsd.commonextensioncomponents_2:oasis.names.specification.ubl.schema.xsd.creditnote_2
2025-07-02 10:29:40.917 [http-nio-8081-exec-1] INFO  c.m.a.c.service.XmlGeneratorService - Successfully generated XML for DEFAULT/INVOICE
2025-07-02 10:29:40.917 [http-nio-8081-exec-1] DEBUG c.m.a.core.service.ValidationService - Validating XML using 2 schematron files
2025-07-02 10:29:40.997 [http-nio-8081-exec-1] DEBUG c.m.a.core.service.ValidationService - Validating with schematron file 1 of 2: PINT-UBL-validation-preprocessed.sch
2025-07-02 10:29:40.997 [http-nio-8081-exec-1] DEBUG c.m.a.core.service.ValidationService - Validating XML using schematron file: PINT-UBL-validation-preprocessed.sch
2025-07-02 10:29:41.002 [http-nio-8081-exec-1] INFO  c.m.a.core.service.ValidationService - Country-specific schematron validation passed for: PINT-UBL-validation-preprocessed.sch
2025-07-02 10:29:41.003 [http-nio-8081-exec-1] DEBUG c.m.a.core.service.ValidationService - Validating with schematron file 2 of 2: PINT-jurisdiction-aligned-rules.sch
2025-07-02 10:29:41.003 [http-nio-8081-exec-1] DEBUG c.m.a.core.service.ValidationService - Validating XML using schematron file: PINT-jurisdiction-aligned-rules.sch
2025-07-02 10:29:41.006 [http-nio-8081-exec-1] INFO  c.m.a.core.service.ValidationService - Country-specific schematron validation passed for: PINT-jurisdiction-aligned-rules.sch
2025-07-02 10:29:41.006 [http-nio-8081-exec-1] INFO  c.m.a.core.service.ValidationService - Country-specific schematron validation passed for all 2 files
2025-07-02 10:29:41.007 [http-nio-8081-exec-1] INFO  c.m.apsp.core.service.InvoiceService - Invoice XML generated and validated successfully for DEFAULT/INVOICE
2025-07-02 10:29:47.692 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 🔄 Starting message processing cycle
2025-07-02 10:29:47.704 [scheduling-1] DEBUG c.m.a.c.service.MessageQueueService - 📋 Found 0 messages ready for processing
2025-07-02 10:29:47.705 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 📋 No messages ready for processing
2025-07-02 10:29:47.706 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - 🔄 Processing retry queue...
2025-07-02 10:29:47.712 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - No errors ready for retry
2025-07-02 10:29:53.787 [SpringApplicationShutdownHook] INFO  c.m.a.c.service.AS4ConversionService - Shutting down AS4 service...
2025-07-02 10:29:53.787 [SpringApplicationShutdownHook] INFO  c.m.a.c.service.AS4ConversionService - ✅ Phase4 global scope cleaned up
2025-07-02 10:30:01.783 [main] INFO  com.morohub.apsp.AS4Application - Starting AS4Application using Java 21.0.3 with PID 16200 (C:\Work\as4-main\target\classes started by kushagrat in C:\Work\as4-main)
2025-07-02 10:30:01.787 [main] DEBUG com.morohub.apsp.AS4Application - Running with Spring Boot v3.2.0, Spring v6.1.0
2025-07-02 10:30:01.787 [main] INFO  com.morohub.apsp.AS4Application - The following 1 profile is active: "dummy"
2025-07-02 10:30:05.470 [main] INFO  c.m.a.c.Phase4ServletConfiguration - 🔧 Registering AS4 servlet for incoming message handling
2025-07-02 10:30:05.470 [main] INFO  c.m.a.c.Phase4ServletConfiguration - ✅ AS4 servlet registered at /reverse-flow/as4/*
2025-07-02 10:30:11.033 [main] INFO  c.m.a.config.AS4CryptoConfiguration - === Creating Configured AS4 Crypto Factory ===
2025-07-02 10:30:11.033 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:30:11.033 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:30:11.033 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:30:11.033 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:30:11.080 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Keystore loaded from: classpath: keystore/cert.p12
2025-07-02 10:30:11.080 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:30:11.080 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:30:11.080 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:30:11.080 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:30:11.087 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Truststore loaded from: classpath: keystore/cert.p12
2025-07-02 10:30:11.087 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Truststore loaded from: classpath: keystore/cert.p12
2025-07-02 10:30:11.087 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - Validating crypto configuration...
2025-07-02 10:30:11.087 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:30:11.087 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:30:11.122 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Crypto configuration validation successful
2025-07-02 10:30:11.122 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 🔑 Private key algorithm: RSA
2025-07-02 10:30:11.122 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 📜 Certificate subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE
2025-07-02 10:30:11.122 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 📅 Certificate valid from: Wed Jul 10 05:30:00 IST 2024 to: Wed Jul 01 05:29:59 IST 2026
2025-07-02 10:30:11.122 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ AS4 Crypto Factory configured successfully and set as default
2025-07-02 10:30:11.122 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 📁 Keystore: cert.p12
2025-07-02 10:30:11.122 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 🔑 Key Alias: cert
2025-07-02 10:30:11.122 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 🛡️ Truststore: cert.p12
2025-07-02 10:30:11.220 [main] INFO  c.m.a.c.CountryConfigurationService - Loading unified country configuration from: country-config.json
2025-07-02 10:30:11.220 [main] INFO  c.m.a.c.CountryConfigurationService - Resource location: classpath: country-config.json
2025-07-02 10:30:11.220 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: country-config.json
2025-07-02 10:30:11.220 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: country-config.json
2025-07-02 10:30:11.246 [main] INFO  c.m.a.c.CountryConfigurationService - ✅ Unified country configuration loaded successfully
2025-07-02 10:30:11.246 [main] INFO  c.m.a.c.CountryConfigurationService - 📊 Available countries: DE, FR, IT, NL, ES, DEFAULT
2025-07-02 10:30:11.348 [main] INFO  c.m.a.c.CountryConfigurationService - 📋 Configuration Summary:
2025-07-02 10:30:11.348 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 DE (Germany): 3 document types
2025-07-02 10:30:11.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:30:11.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:30:11.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:30:11.348 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 FR (France): 3 document types
2025-07-02 10:30:11.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-FR.sch
2025-07-02 10:30:11.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-FR.sch
2025-07-02 10:30:11.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-FR.sch
2025-07-02 10:30:11.348 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 IT (Italy): 3 document types
2025-07-02 10:30:11.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-IT.sch
2025-07-02 10:30:11.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-IT.sch
2025-07-02 10:30:11.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-IT.sch
2025-07-02 10:30:11.348 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 NL (Netherlands): 3 document types
2025-07-02 10:30:11.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-NL.sch
2025-07-02 10:30:11.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-NL.sch
2025-07-02 10:30:11.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-NL.sch
2025-07-02 10:30:11.348 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 ES (Spain): 3 document types
2025-07-02 10:30:11.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-ES.sch
2025-07-02 10:30:11.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-ES.sch
2025-07-02 10:30:11.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-ES.sch
2025-07-02 10:30:11.348 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 DEFAULT (Default/Generic): 3 document types
2025-07-02 10:30:11.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | null
2025-07-02 10:30:11.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:30:11.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:30:11.348 [main] INFO  c.m.a.c.service.AS4ConversionService - === Initializing Production-Ready AS4 Service ===
2025-07-02 10:30:11.348 [main] INFO  c.m.a.c.service.AS4ConversionService - AS4 Mode: dummy
2025-07-02 10:30:11.348 [main] INFO  c.m.a.c.service.AS4ConversionService - Security Enabled: true
2025-07-02 10:30:11.348 [main] INFO  c.m.a.c.service.AS4ConversionService - Validation Enabled: false
2025-07-02 10:30:11.348 [main] INFO  c.m.a.c.service.AS4ConversionService - Endpoint: http://localhost:8081/reverse-flow/receive-as4-message
2025-07-02 10:30:11.348 [main] INFO  c.m.a.c.service.AS4ConversionService - === Checking Keystore Availability ===
2025-07-02 10:30:11.348 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:30:11.348 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:30:11.348 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Keystore found at: classpath: keystore/cert.p12
2025-07-02 10:30:11.348 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:30:11.348 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:30:11.365 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Private key accessible for alias: cert
2025-07-02 10:30:11.365 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Keystore validation successful
2025-07-02 10:30:11.365 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:30:11.365 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:30:11.365 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Truststore found at: classpath: keystore/cert.p12
2025-07-02 10:30:11.407 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Phase4 global scope initialized
2025-07-02 10:30:11.407 [main] INFO  c.m.a.c.service.AS4ConversionService - === Configuring Phase4 Crypto Settings ===
2025-07-02 10:30:11.407 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:30:11.407 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:30:11.422 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Crypto configuration is properly set up
2025-07-02 10:30:11.422 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:30:11.422 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:30:11.422 [main] INFO  c.m.a.c.service.AS4ConversionService - 📁 Keystore info: Keystore: cert.p12, Alias: cert, Subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE, Valid: Wed Jul 10 05:30:00 IST 2024 to Wed Jul 01 05:29:59 IST 2026
2025-07-02 10:30:11.589 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ MetaAS4Manager initialized
2025-07-02 10:30:11.589 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 service initialized successfully for dummy mode
2025-07-02 10:30:11.904 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-02 10:30:12.530 [main] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 🔧 Initializing AS4 Servlet for message processing
2025-07-02 10:30:12.530 [main] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - ✅ AS4 Servlet initialized successfully
2025-07-02 10:30:12.550 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 🔄 Starting message processing cycle
2025-07-02 10:30:12.550 [main] INFO  com.morohub.apsp.AS4Application - Started AS4Application in 12.933 seconds (process running for 15.093)
2025-07-02 10:30:12.566 [main] INFO  c.m.a.c.ErrorMasterDataInitializer - 🔧 Initializing error master data...
2025-07-02 10:30:12.719 [scheduling-1] DEBUG c.m.a.c.service.MessageQueueService - 📋 Found 0 messages ready for processing
2025-07-02 10:30:12.719 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_001
2025-07-02 10:30:12.737 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_002
2025-07-02 10:30:12.737 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 📋 No messages ready for processing
2025-07-02 10:30:12.737 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_003
2025-07-02 10:30:12.737 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - 🔄 Processing retry queue...
2025-07-02 10:30:12.737 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_004
2025-07-02 10:30:12.737 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_001
2025-07-02 10:30:12.755 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_002
2025-07-02 10:30:12.759 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_003
2025-07-02 10:30:12.759 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_004
2025-07-02 10:30:12.759 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - No errors ready for retry
2025-07-02 10:30:12.759 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_001
2025-07-02 10:30:12.759 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_002
2025-07-02 10:30:12.759 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_003
2025-07-02 10:30:12.759 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_004
2025-07-02 10:30:12.775 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_005
2025-07-02 10:30:12.775 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: SYS_001
2025-07-02 10:30:12.775 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: SYS_002
2025-07-02 10:30:12.775 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: SYS_003
2025-07-02 10:30:12.775 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_001
2025-07-02 10:30:12.775 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_002
2025-07-02 10:30:12.790 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_003
2025-07-02 10:30:12.790 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_004
2025-07-02 10:30:12.790 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: PEPPOL_001
2025-07-02 10:30:12.790 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: PEPPOL_002
2025-07-02 10:30:12.790 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: PEPPOL_003
2025-07-02 10:30:12.790 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: MLS_001
2025-07-02 10:30:12.806 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: MLS_002
2025-07-02 10:30:12.806 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: RATE_001
2025-07-02 10:30:12.806 [main] INFO  c.m.a.c.ErrorMasterDataInitializer - Initialized 26 error master data entries
2025-07-02 10:30:12.806 [main] INFO  c.m.a.c.ErrorMasterDataInitializer - ✅ Error master data initialization completed
2025-07-02 10:30:29.032 [http-nio-8081-exec-1] INFO  c.m.a.a.c.PeppolSbdInvoiceController - Processing request for country: DEFAULT and document type: INVOICE
2025-07-02 10:30:29.034 [http-nio-8081-exec-1] INFO  c.m.a.a.c.PeppolSbdInvoiceController - Using UBL class: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType for DEFAULT/INVOICE
2025-07-02 10:30:29.918 [http-nio-8081-exec-1] INFO  c.m.apsp.core.service.InvoiceService - Generating invoice XML for country: DEFAULT and document type: INVOICE
2025-07-02 10:30:29.919 [http-nio-8081-exec-1] DEBUG c.m.a.c.service.XmlGeneratorService - Generating invoice XML using country-specific configuration: DEFAULT/INVOICE
2025-07-02 10:30:29.920 [http-nio-8081-exec-1] INFO  c.m.a.c.service.XmlGeneratorService - Using classes for DEFAULT/INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType and oasis.names.specification.ubl.schema.xsd.invoice_2.ObjectFactory
2025-07-02 10:30:29.922 [http-nio-8081-exec-1] DEBUG c.m.a.c.s.XmlJsonConversionService - 🔍 Creating UBL JAXB context for package: oasis.names.specification.ubl.schema.xsd.invoice_2
2025-07-02 10:30:29.922 [http-nio-8081-exec-1] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.invoice_2
2025-07-02 10:30:29.967 [http-nio-8081-exec-1] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.commonaggregatecomponents_2
2025-07-02 10:30:30.056 [http-nio-8081-exec-1] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.commonbasiccomponents_2
2025-07-02 10:30:30.057 [http-nio-8081-exec-1] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.commonextensioncomponents_2
2025-07-02 10:30:30.058 [http-nio-8081-exec-1] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.creditnote_2
2025-07-02 10:30:30.058 [http-nio-8081-exec-1] DEBUG c.m.a.c.s.XmlJsonConversionService - 🔍 Using UBL context path: oasis.names.specification.ubl.schema.xsd.invoice_2:oasis.names.specification.ubl.schema.xsd.commonaggregatecomponents_2:oasis.names.specification.ubl.schema.xsd.commonbasiccomponents_2:oasis.names.specification.ubl.schema.xsd.commonextensioncomponents_2:oasis.names.specification.ubl.schema.xsd.creditnote_2
2025-07-02 10:30:30.892 [http-nio-8081-exec-1] INFO  c.m.a.c.service.XmlGeneratorService - Successfully generated XML for DEFAULT/INVOICE
2025-07-02 10:30:30.893 [http-nio-8081-exec-1] DEBUG c.m.a.core.service.ValidationService - Validating XML using 2 schematron files
2025-07-02 10:30:30.911 [http-nio-8081-exec-1] DEBUG c.m.a.core.service.ValidationService - Validating with schematron file 1 of 2: PINT-UBL-validation-preprocessed.sch
2025-07-02 10:30:30.911 [http-nio-8081-exec-1] DEBUG c.m.a.core.service.ValidationService - Validating XML using schematron file: PINT-UBL-validation-preprocessed.sch
2025-07-02 10:30:30.933 [http-nio-8081-exec-1] INFO  c.m.a.core.service.ValidationService - Country-specific schematron validation passed for: PINT-UBL-validation-preprocessed.sch
2025-07-02 10:30:30.933 [http-nio-8081-exec-1] DEBUG c.m.a.core.service.ValidationService - Validating with schematron file 2 of 2: PINT-jurisdiction-aligned-rules.sch
2025-07-02 10:30:30.933 [http-nio-8081-exec-1] DEBUG c.m.a.core.service.ValidationService - Validating XML using schematron file: PINT-jurisdiction-aligned-rules.sch
2025-07-02 10:30:30.936 [http-nio-8081-exec-1] INFO  c.m.a.core.service.ValidationService - Country-specific schematron validation passed for: PINT-jurisdiction-aligned-rules.sch
2025-07-02 10:30:30.937 [http-nio-8081-exec-1] INFO  c.m.a.core.service.ValidationService - Country-specific schematron validation passed for all 2 files
2025-07-02 10:30:30.937 [http-nio-8081-exec-1] INFO  c.m.apsp.core.service.InvoiceService - Invoice XML generated and validated successfully for DEFAULT/INVOICE
2025-07-02 10:32:00.705 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 🔄 Starting message processing cycle
2025-07-02 10:32:00.705 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m53s893ms803µs100ns).
2025-07-02 10:32:00.714 [scheduling-1] DEBUG c.m.a.c.service.MessageQueueService - 📋 Found 0 messages ready for processing
2025-07-02 10:32:00.716 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 📋 No messages ready for processing
2025-07-02 10:32:00.716 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - 🔄 Processing retry queue...
2025-07-02 10:32:00.720 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - No errors ready for retry
2025-07-02 10:32:11.898 [SpringApplicationShutdownHook] INFO  c.m.a.c.service.AS4ConversionService - Shutting down AS4 service...
2025-07-02 10:32:11.914 [SpringApplicationShutdownHook] INFO  c.m.a.c.service.AS4ConversionService - ✅ Phase4 global scope cleaned up
2025-07-02 10:32:18.711 [main] INFO  com.morohub.apsp.AS4Application - Starting AS4Application using Java 21.0.3 with PID 25488 (C:\Work\as4-main\target\classes started by kushagrat in C:\Work\as4-main)
2025-07-02 10:32:18.711 [main] DEBUG com.morohub.apsp.AS4Application - Running with Spring Boot v3.2.0, Spring v6.1.0
2025-07-02 10:32:18.711 [main] INFO  com.morohub.apsp.AS4Application - The following 1 profile is active: "dummy"
2025-07-02 10:32:21.800 [main] INFO  c.m.a.c.Phase4ServletConfiguration - 🔧 Registering AS4 servlet for incoming message handling
2025-07-02 10:32:21.801 [main] INFO  c.m.a.c.Phase4ServletConfiguration - ✅ AS4 servlet registered at /reverse-flow/as4/*
2025-07-02 10:32:27.574 [main] INFO  c.m.a.config.AS4CryptoConfiguration - === Creating Configured AS4 Crypto Factory ===
2025-07-02 10:32:27.589 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:32:27.589 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:32:27.589 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:32:27.589 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:32:27.626 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Keystore loaded from: classpath: keystore/cert.p12
2025-07-02 10:32:27.626 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:32:27.626 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:32:27.626 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:32:27.626 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:32:27.641 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Truststore loaded from: classpath: keystore/cert.p12
2025-07-02 10:32:27.641 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Truststore loaded from: classpath: keystore/cert.p12
2025-07-02 10:32:27.657 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - Validating crypto configuration...
2025-07-02 10:32:27.657 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:32:27.657 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:32:27.704 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Crypto configuration validation successful
2025-07-02 10:32:27.704 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 🔑 Private key algorithm: RSA
2025-07-02 10:32:27.704 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 📜 Certificate subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE
2025-07-02 10:32:27.704 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 📅 Certificate valid from: Wed Jul 10 05:30:00 IST 2024 to: Wed Jul 01 05:29:59 IST 2026
2025-07-02 10:32:27.704 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ AS4 Crypto Factory configured successfully and set as default
2025-07-02 10:32:27.704 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 📁 Keystore: cert.p12
2025-07-02 10:32:27.704 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 🔑 Key Alias: cert
2025-07-02 10:32:27.704 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 🛡️ Truststore: cert.p12
2025-07-02 10:32:27.783 [main] INFO  c.m.a.c.CountryConfigurationService - Loading unified country configuration from: country-config.json
2025-07-02 10:32:27.783 [main] INFO  c.m.a.c.CountryConfigurationService - Resource location: classpath: country-config.json
2025-07-02 10:32:27.783 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: country-config.json
2025-07-02 10:32:27.783 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: country-config.json
2025-07-02 10:32:27.827 [main] INFO  c.m.a.c.CountryConfigurationService - ✅ Unified country configuration loaded successfully
2025-07-02 10:32:27.827 [main] INFO  c.m.a.c.CountryConfigurationService - 📊 Available countries: DE, FR, IT, NL, ES, DEFAULT
2025-07-02 10:32:27.926 [main] INFO  c.m.a.c.CountryConfigurationService - 📋 Configuration Summary:
2025-07-02 10:32:27.926 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 DE (Germany): 3 document types
2025-07-02 10:32:27.926 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:32:27.926 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:32:27.926 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:32:27.926 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 FR (France): 3 document types
2025-07-02 10:32:27.926 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-FR.sch
2025-07-02 10:32:27.926 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-FR.sch
2025-07-02 10:32:27.926 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-FR.sch
2025-07-02 10:32:27.926 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 IT (Italy): 3 document types
2025-07-02 10:32:27.926 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-IT.sch
2025-07-02 10:32:27.926 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-IT.sch
2025-07-02 10:32:27.926 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-IT.sch
2025-07-02 10:32:27.926 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 NL (Netherlands): 3 document types
2025-07-02 10:32:27.926 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-NL.sch
2025-07-02 10:32:27.926 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-NL.sch
2025-07-02 10:32:27.926 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-NL.sch
2025-07-02 10:32:27.926 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 ES (Spain): 3 document types
2025-07-02 10:32:27.926 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-ES.sch
2025-07-02 10:32:27.926 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-ES.sch
2025-07-02 10:32:27.926 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-ES.sch
2025-07-02 10:32:27.926 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 DEFAULT (Default/Generic): 3 document types
2025-07-02 10:32:27.926 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | null
2025-07-02 10:32:27.926 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:32:27.926 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:32:27.941 [main] INFO  c.m.a.c.service.AS4ConversionService - === Initializing Production-Ready AS4 Service ===
2025-07-02 10:32:27.941 [main] INFO  c.m.a.c.service.AS4ConversionService - AS4 Mode: dummy
2025-07-02 10:32:27.941 [main] INFO  c.m.a.c.service.AS4ConversionService - Security Enabled: true
2025-07-02 10:32:27.941 [main] INFO  c.m.a.c.service.AS4ConversionService - Validation Enabled: false
2025-07-02 10:32:27.941 [main] INFO  c.m.a.c.service.AS4ConversionService - Endpoint: http://localhost:8081/reverse-flow/receive-as4-message
2025-07-02 10:32:27.941 [main] INFO  c.m.a.c.service.AS4ConversionService - === Checking Keystore Availability ===
2025-07-02 10:32:27.941 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:32:27.941 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:32:27.941 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Keystore found at: classpath: keystore/cert.p12
2025-07-02 10:32:27.941 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:32:27.941 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:32:27.957 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Private key accessible for alias: cert
2025-07-02 10:32:27.957 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Keystore validation successful
2025-07-02 10:32:27.957 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:32:27.957 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:32:27.957 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Truststore found at: classpath: keystore/cert.p12
2025-07-02 10:32:27.988 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Phase4 global scope initialized
2025-07-02 10:32:27.988 [main] INFO  c.m.a.c.service.AS4ConversionService - === Configuring Phase4 Crypto Settings ===
2025-07-02 10:32:28.004 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:32:28.004 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:32:28.004 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Crypto configuration is properly set up
2025-07-02 10:32:28.004 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:32:28.004 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:32:28.004 [main] INFO  c.m.a.c.service.AS4ConversionService - 📁 Keystore info: Keystore: cert.p12, Alias: cert, Subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE, Valid: Wed Jul 10 05:30:00 IST 2024 to Wed Jul 01 05:29:59 IST 2026
2025-07-02 10:32:28.201 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ MetaAS4Manager initialized
2025-07-02 10:32:28.201 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 service initialized successfully for dummy mode
2025-07-02 10:32:28.579 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-02 10:32:29.132 [main] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 🔧 Initializing AS4 Servlet for message processing
2025-07-02 10:32:29.132 [main] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - ✅ AS4 Servlet initialized successfully
2025-07-02 10:32:29.163 [main] INFO  com.morohub.apsp.AS4Application - Started AS4Application in 11.637 seconds (process running for 12.631)
2025-07-02 10:32:29.163 [main] INFO  c.m.a.c.ErrorMasterDataInitializer - 🔧 Initializing error master data...
2025-07-02 10:32:29.163 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 🔄 Starting message processing cycle
2025-07-02 10:32:29.302 [scheduling-1] DEBUG c.m.a.c.service.MessageQueueService - 📋 Found 0 messages ready for processing
2025-07-02 10:32:29.320 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_001
2025-07-02 10:32:29.320 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 📋 No messages ready for processing
2025-07-02 10:32:29.320 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - 🔄 Processing retry queue...
2025-07-02 10:32:29.320 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_002
2025-07-02 10:32:29.327 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_003
2025-07-02 10:32:29.327 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_004
2025-07-02 10:32:29.327 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_001
2025-07-02 10:32:29.327 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - No errors ready for retry
2025-07-02 10:32:29.327 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_002
2025-07-02 10:32:29.327 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_003
2025-07-02 10:32:29.327 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_004
2025-07-02 10:32:29.343 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_001
2025-07-02 10:32:29.343 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_002
2025-07-02 10:32:29.343 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_003
2025-07-02 10:32:29.343 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_004
2025-07-02 10:32:29.343 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_005
2025-07-02 10:32:29.343 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: SYS_001
2025-07-02 10:32:29.358 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: SYS_002
2025-07-02 10:32:29.358 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: SYS_003
2025-07-02 10:32:29.358 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_001
2025-07-02 10:32:29.358 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_002
2025-07-02 10:32:29.358 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_003
2025-07-02 10:32:29.358 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_004
2025-07-02 10:32:29.374 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: PEPPOL_001
2025-07-02 10:32:29.374 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: PEPPOL_002
2025-07-02 10:32:29.374 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: PEPPOL_003
2025-07-02 10:32:29.374 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: MLS_001
2025-07-02 10:32:29.374 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: MLS_002
2025-07-02 10:32:29.374 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: RATE_001
2025-07-02 10:32:29.374 [main] INFO  c.m.a.c.ErrorMasterDataInitializer - Initialized 26 error master data entries
2025-07-02 10:32:29.374 [main] INFO  c.m.a.c.ErrorMasterDataInitializer - ✅ Error master data initialization completed
2025-07-02 10:32:37.444 [http-nio-8081-exec-2] INFO  c.m.a.a.c.PeppolSbdInvoiceController - Processing request for country: DEFAULT and document type: INVOICE
2025-07-02 10:32:37.448 [http-nio-8081-exec-2] INFO  c.m.a.a.c.PeppolSbdInvoiceController - Using UBL class: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType for DEFAULT/INVOICE
2025-07-02 10:32:38.308 [http-nio-8081-exec-2] INFO  c.m.apsp.core.service.InvoiceService - Generating invoice XML for country: DEFAULT and document type: INVOICE
2025-07-02 10:32:38.308 [http-nio-8081-exec-2] DEBUG c.m.a.c.service.XmlGeneratorService - Generating invoice XML using country-specific configuration: DEFAULT/INVOICE
2025-07-02 10:32:38.309 [http-nio-8081-exec-2] INFO  c.m.a.c.service.XmlGeneratorService - Using classes for DEFAULT/INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType and oasis.names.specification.ubl.schema.xsd.invoice_2.ObjectFactory
2025-07-02 10:32:38.311 [http-nio-8081-exec-2] DEBUG c.m.a.c.s.XmlJsonConversionService - 🔍 Creating UBL JAXB context for package: oasis.names.specification.ubl.schema.xsd.invoice_2
2025-07-02 10:32:38.311 [http-nio-8081-exec-2] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.invoice_2
2025-07-02 10:32:38.362 [http-nio-8081-exec-2] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.commonaggregatecomponents_2
2025-07-02 10:32:38.448 [http-nio-8081-exec-2] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.commonbasiccomponents_2
2025-07-02 10:32:38.449 [http-nio-8081-exec-2] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.commonextensioncomponents_2
2025-07-02 10:32:38.450 [http-nio-8081-exec-2] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.creditnote_2
2025-07-02 10:32:38.450 [http-nio-8081-exec-2] DEBUG c.m.a.c.s.XmlJsonConversionService - 🔍 Using UBL context path: oasis.names.specification.ubl.schema.xsd.invoice_2:oasis.names.specification.ubl.schema.xsd.commonaggregatecomponents_2:oasis.names.specification.ubl.schema.xsd.commonbasiccomponents_2:oasis.names.specification.ubl.schema.xsd.commonextensioncomponents_2:oasis.names.specification.ubl.schema.xsd.creditnote_2
2025-07-02 10:32:39.275 [http-nio-8081-exec-2] INFO  c.m.a.c.service.XmlGeneratorService - Successfully generated XML for DEFAULT/INVOICE
2025-07-02 10:32:39.275 [http-nio-8081-exec-2] DEBUG c.m.a.core.service.ValidationService - Validating XML using 2 schematron files
2025-07-02 10:32:39.292 [http-nio-8081-exec-2] DEBUG c.m.a.core.service.ValidationService - Validating with schematron file 1 of 2: PINT-UBL-validation-preprocessed.sch
2025-07-02 10:32:39.292 [http-nio-8081-exec-2] DEBUG c.m.a.core.service.ValidationService - Validating XML using schematron file: PINT-UBL-validation-preprocessed.sch
2025-07-02 10:32:39.296 [http-nio-8081-exec-2] INFO  c.m.a.core.service.ValidationService - Country-specific schematron validation passed for: PINT-UBL-validation-preprocessed.sch
2025-07-02 10:32:39.296 [http-nio-8081-exec-2] DEBUG c.m.a.core.service.ValidationService - Validating with schematron file 2 of 2: PINT-jurisdiction-aligned-rules.sch
2025-07-02 10:32:39.296 [http-nio-8081-exec-2] DEBUG c.m.a.core.service.ValidationService - Validating XML using schematron file: PINT-jurisdiction-aligned-rules.sch
2025-07-02 10:32:39.299 [http-nio-8081-exec-2] INFO  c.m.a.core.service.ValidationService - Country-specific schematron validation passed for: PINT-jurisdiction-aligned-rules.sch
2025-07-02 10:32:39.299 [http-nio-8081-exec-2] INFO  c.m.a.core.service.ValidationService - Country-specific schematron validation passed for all 2 files
2025-07-02 10:32:39.300 [http-nio-8081-exec-2] INFO  c.m.apsp.core.service.InvoiceService - Invoice XML generated and validated successfully for DEFAULT/INVOICE
2025-07-02 10:32:50.508 [SpringApplicationShutdownHook] INFO  c.m.a.c.service.AS4ConversionService - Shutting down AS4 service...
2025-07-02 10:32:50.508 [SpringApplicationShutdownHook] INFO  c.m.a.c.service.AS4ConversionService - ✅ Phase4 global scope cleaned up
2025-07-02 10:37:42.280 [main] INFO  com.morohub.apsp.AS4Application - Starting AS4Application using Java 21.0.3 with PID 8308 (C:\Work\as4-main\target\classes started by kushagrat in C:\Work\as4-main)
2025-07-02 10:37:42.280 [main] DEBUG com.morohub.apsp.AS4Application - Running with Spring Boot v3.2.0, Spring v6.1.0
2025-07-02 10:37:42.280 [main] INFO  com.morohub.apsp.AS4Application - The following 1 profile is active: "dummy"
2025-07-02 10:37:45.009 [main] INFO  c.m.a.c.Phase4ServletConfiguration - 🔧 Registering AS4 servlet for incoming message handling
2025-07-02 10:37:45.009 [main] INFO  c.m.a.c.Phase4ServletConfiguration - ✅ AS4 servlet registered at /reverse-flow/as4/*
2025-07-02 10:37:49.646 [main] INFO  c.m.a.config.AS4CryptoConfiguration - === Creating Configured AS4 Crypto Factory ===
2025-07-02 10:37:49.646 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:37:49.646 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:37:49.646 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:37:49.646 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:37:49.678 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Keystore loaded from: classpath: keystore/cert.p12
2025-07-02 10:37:49.678 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:37:49.678 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:37:49.678 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:37:49.678 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:37:49.693 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Truststore loaded from: classpath: keystore/cert.p12
2025-07-02 10:37:49.693 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Truststore loaded from: classpath: keystore/cert.p12
2025-07-02 10:37:49.693 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - Validating crypto configuration...
2025-07-02 10:37:49.693 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:37:49.693 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:37:49.724 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Crypto configuration validation successful
2025-07-02 10:37:49.724 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 🔑 Private key algorithm: RSA
2025-07-02 10:37:49.724 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 📜 Certificate subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE
2025-07-02 10:37:49.724 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 📅 Certificate valid from: Wed Jul 10 05:30:00 IST 2024 to: Wed Jul 01 05:29:59 IST 2026
2025-07-02 10:37:49.724 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ AS4 Crypto Factory configured successfully and set as default
2025-07-02 10:37:49.724 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 📁 Keystore: cert.p12
2025-07-02 10:37:49.724 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 🔑 Key Alias: cert
2025-07-02 10:37:49.724 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 🛡️ Truststore: cert.p12
2025-07-02 10:37:49.803 [main] INFO  c.m.a.c.CountryConfigurationService - Loading unified country configuration from: country-config.json
2025-07-02 10:37:49.803 [main] INFO  c.m.a.c.CountryConfigurationService - Resource location: classpath: country-config.json
2025-07-02 10:37:49.803 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: country-config.json
2025-07-02 10:37:49.803 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: country-config.json
2025-07-02 10:37:49.818 [main] INFO  c.m.a.c.CountryConfigurationService - ✅ Unified country configuration loaded successfully
2025-07-02 10:37:49.818 [main] INFO  c.m.a.c.CountryConfigurationService - 📊 Available countries: DE, FR, IT, NL, ES, DEFAULT
2025-07-02 10:37:49.926 [main] INFO  c.m.a.c.CountryConfigurationService - 📋 Configuration Summary:
2025-07-02 10:37:49.927 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 DE (Germany): 3 document types
2025-07-02 10:37:49.928 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:37:49.928 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:37:49.928 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:37:49.928 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 FR (France): 3 document types
2025-07-02 10:37:49.928 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-FR.sch
2025-07-02 10:37:49.929 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-FR.sch
2025-07-02 10:37:49.929 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-FR.sch
2025-07-02 10:37:49.929 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 IT (Italy): 3 document types
2025-07-02 10:37:49.929 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-IT.sch
2025-07-02 10:37:49.929 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-IT.sch
2025-07-02 10:37:49.930 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-IT.sch
2025-07-02 10:37:49.930 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 NL (Netherlands): 3 document types
2025-07-02 10:37:49.930 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-NL.sch
2025-07-02 10:37:49.930 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-NL.sch
2025-07-02 10:37:49.930 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-NL.sch
2025-07-02 10:37:49.930 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 ES (Spain): 3 document types
2025-07-02 10:37:49.930 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-ES.sch
2025-07-02 10:37:49.930 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-ES.sch
2025-07-02 10:37:49.931 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-ES.sch
2025-07-02 10:37:49.931 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 DEFAULT (Default/Generic): 3 document types
2025-07-02 10:37:49.931 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | null
2025-07-02 10:37:49.931 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:37:49.931 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:37:49.933 [main] INFO  c.m.a.c.service.AS4ConversionService - === Initializing Production-Ready AS4 Service ===
2025-07-02 10:37:49.933 [main] INFO  c.m.a.c.service.AS4ConversionService - AS4 Mode: dummy
2025-07-02 10:37:49.933 [main] INFO  c.m.a.c.service.AS4ConversionService - Security Enabled: true
2025-07-02 10:37:49.933 [main] INFO  c.m.a.c.service.AS4ConversionService - Validation Enabled: false
2025-07-02 10:37:49.933 [main] INFO  c.m.a.c.service.AS4ConversionService - Endpoint: http://localhost:8081/reverse-flow/receive-as4-message
2025-07-02 10:37:49.933 [main] INFO  c.m.a.c.service.AS4ConversionService - === Checking Keystore Availability ===
2025-07-02 10:37:49.933 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:37:49.934 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:37:49.934 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Keystore found at: classpath: keystore/cert.p12
2025-07-02 10:37:49.935 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:37:49.935 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:37:49.939 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Private key accessible for alias: cert
2025-07-02 10:37:49.939 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Keystore validation successful
2025-07-02 10:37:49.939 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:37:49.939 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:37:49.939 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Truststore found at: classpath: keystore/cert.p12
2025-07-02 10:37:49.972 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Phase4 global scope initialized
2025-07-02 10:37:49.972 [main] INFO  c.m.a.c.service.AS4ConversionService - === Configuring Phase4 Crypto Settings ===
2025-07-02 10:37:49.972 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:37:49.972 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:37:49.972 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Crypto configuration is properly set up
2025-07-02 10:37:49.972 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:37:49.972 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:37:49.972 [main] INFO  c.m.a.c.service.AS4ConversionService - 📁 Keystore info: Keystore: cert.p12, Alias: cert, Subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE, Valid: Wed Jul 10 05:30:00 IST 2024 to Wed Jul 01 05:29:59 IST 2026
2025-07-02 10:37:50.153 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ MetaAS4Manager initialized
2025-07-02 10:37:50.153 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 service initialized successfully for dummy mode
2025-07-02 10:37:50.454 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-02 10:37:50.970 [main] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 🔧 Initializing AS4 Servlet for message processing
2025-07-02 10:37:50.970 [main] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - ✅ AS4 Servlet initialized successfully
2025-07-02 10:37:50.986 [main] INFO  com.morohub.apsp.AS4Application - Started AS4Application in 9.649 seconds (process running for 10.355)
2025-07-02 10:37:50.986 [main] INFO  c.m.a.c.ErrorMasterDataInitializer - 🔧 Initializing error master data...
2025-07-02 10:37:50.986 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 🔄 Starting message processing cycle
2025-07-02 10:37:51.123 [scheduling-1] DEBUG c.m.a.c.service.MessageQueueService - 📋 Found 0 messages ready for processing
2025-07-02 10:37:51.125 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_001
2025-07-02 10:37:51.126 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 📋 No messages ready for processing
2025-07-02 10:37:51.128 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - 🔄 Processing retry queue...
2025-07-02 10:37:51.128 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_002
2025-07-02 10:37:51.131 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_003
2025-07-02 10:37:51.133 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_004
2025-07-02 10:37:51.137 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_001
2025-07-02 10:37:51.140 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_002
2025-07-02 10:37:51.141 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - No errors ready for retry
2025-07-02 10:37:51.142 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_003
2025-07-02 10:37:51.144 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_004
2025-07-02 10:37:51.146 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_001
2025-07-02 10:37:51.148 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_002
2025-07-02 10:37:51.150 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_003
2025-07-02 10:37:51.152 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_004
2025-07-02 10:37:51.154 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_005
2025-07-02 10:37:51.156 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: SYS_001
2025-07-02 10:37:51.157 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: SYS_002
2025-07-02 10:37:51.159 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: SYS_003
2025-07-02 10:37:51.161 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_001
2025-07-02 10:37:51.164 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_002
2025-07-02 10:37:51.167 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_003
2025-07-02 10:37:51.169 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_004
2025-07-02 10:37:51.172 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: PEPPOL_001
2025-07-02 10:37:51.174 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: PEPPOL_002
2025-07-02 10:37:51.176 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: PEPPOL_003
2025-07-02 10:37:51.179 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: MLS_001
2025-07-02 10:37:51.182 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: MLS_002
2025-07-02 10:37:51.185 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: RATE_001
2025-07-02 10:37:51.185 [main] INFO  c.m.a.c.ErrorMasterDataInitializer - Initialized 26 error master data entries
2025-07-02 10:37:51.185 [main] INFO  c.m.a.c.ErrorMasterDataInitializer - ✅ Error master data initialization completed
2025-07-02 10:37:53.295 [http-nio-8081-exec-1] INFO  c.m.a.a.c.PeppolSbdInvoiceController - Processing request for country: DEFAULT and document type: INVOICE
2025-07-02 10:37:53.297 [http-nio-8081-exec-1] INFO  c.m.a.a.c.PeppolSbdInvoiceController - Using UBL class: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType for DEFAULT/INVOICE
2025-07-02 10:37:53.452 [http-nio-8081-exec-1] ERROR c.m.a.a.c.PeppolSbdInvoiceController - Failed to log invoice generation result
org.springframework.web.client.HttpServerErrorException$InternalServerError: 500 : "Error: JSON parse error: Cannot deserialize value of type `java.time.LocalDate` from String "UNKNOWN": Failed to deserialize java.time.LocalDate: (java.time.format.DateTimeParseException) Text 'UNKNOWN' could not be parsed at index 0"
	at org.springframework.web.client.HttpServerErrorException.create(HttpServerErrorException.java:102)
	at org.springframework.web.client.DefaultResponseErrorHandler.handleError(DefaultResponseErrorHandler.java:186)
	at org.springframework.web.client.DefaultResponseErrorHandler.handleError(DefaultResponseErrorHandler.java:137)
	at org.springframework.web.client.ResponseErrorHandler.handleError(ResponseErrorHandler.java:63)
	at org.springframework.web.client.RestTemplate.handleResponse(RestTemplate.java:932)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:881)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:781)
	at org.springframework.web.client.RestTemplate.postForEntity(RestTemplate.java:529)
	at com.morohub.apsp.api.controller.PeppolSbdInvoiceController.sendPeppolSbdInvoice(PeppolSbdInvoiceController.java:183)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:254)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:182)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 10:37:53.734 [http-nio-8081-exec-1] INFO  c.m.a.c.s.ErrorManagementService - Error logged: CONN_005 for message: null
2025-07-02 10:37:53.749 [http-nio-8081-exec-1] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Failure in @ExceptionHandler com.morohub.apsp.config.GlobalExceptionHandler#handleConnectivityException(ConnectivityException)
java.lang.NullPointerException: Cannot invoke "java.util.Map.put(Object, Object)" because the return value of "com.morohub.apsp.common.dto.ErrorResponse.getAdditionalInfo()" is null
	at com.morohub.apsp.config.GlobalExceptionHandler.handleConnectivityException(GlobalExceptionHandler.java:130)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:254)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:182)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:432)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:74)
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:161)
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80)
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1357)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1160)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1106)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 10:37:53.752 [http-nio-8081-exec-1] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed: ConnectivityException{errorCode='CONN_005', endpoint='http://localhost:8090/api/secure/decrypt-invoice/0235:1234567891', httpStatusCode=0, responseTime=0, message='Failed to connect to decryption service: 500 : "Error: Authorization failed due to wrong key"'}] with root cause
org.springframework.web.client.HttpServerErrorException$InternalServerError: 500 : "Error: Authorization failed due to wrong key"
	at org.springframework.web.client.HttpServerErrorException.create(HttpServerErrorException.java:102)
	at org.springframework.web.client.DefaultResponseErrorHandler.handleError(DefaultResponseErrorHandler.java:186)
	at org.springframework.web.client.DefaultResponseErrorHandler.handleError(DefaultResponseErrorHandler.java:137)
	at org.springframework.web.client.ResponseErrorHandler.handleError(ResponseErrorHandler.java:63)
	at org.springframework.web.client.RestTemplate.handleResponse(RestTemplate.java:932)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:881)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:781)
	at org.springframework.web.client.RestTemplate.postForEntity(RestTemplate.java:529)
	at com.morohub.apsp.api.controller.PeppolSbdInvoiceController.sendPeppolSbdInvoice(PeppolSbdInvoiceController.java:108)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:254)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:182)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 10:38:21.132 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 🔄 Starting message processing cycle
2025-07-02 10:38:21.132 [scheduling-1] DEBUG c.m.a.c.service.MessageQueueService - 📋 Found 0 messages ready for processing
2025-07-02 10:38:21.132 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 📋 No messages ready for processing
2025-07-02 10:38:21.132 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - 🔄 Processing retry queue...
2025-07-02 10:38:21.147 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - No errors ready for retry
2025-07-02 10:38:51.148 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 🔄 Starting message processing cycle
2025-07-02 10:38:51.148 [scheduling-1] DEBUG c.m.a.c.service.MessageQueueService - 📋 Found 0 messages ready for processing
2025-07-02 10:38:51.148 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 📋 No messages ready for processing
2025-07-02 10:38:51.148 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - 🔄 Processing retry queue...
2025-07-02 10:38:51.148 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - No errors ready for retry
2025-07-02 10:39:21.172 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 🔄 Starting message processing cycle
2025-07-02 10:39:21.172 [scheduling-1] DEBUG c.m.a.c.service.MessageQueueService - 📋 Found 0 messages ready for processing
2025-07-02 10:39:21.172 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 📋 No messages ready for processing
2025-07-02 10:39:21.172 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - 🔄 Processing retry queue...
2025-07-02 10:39:21.188 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - No errors ready for retry
2025-07-02 10:39:51.184 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 🔄 Starting message processing cycle
2025-07-02 10:39:51.184 [scheduling-1] DEBUG c.m.a.c.service.MessageQueueService - 📋 Found 0 messages ready for processing
2025-07-02 10:39:51.184 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 📋 No messages ready for processing
2025-07-02 10:39:51.184 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - 🔄 Processing retry queue...
2025-07-02 10:39:51.200 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - No errors ready for retry
2025-07-02 10:40:21.200 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 🔄 Starting message processing cycle
2025-07-02 10:40:21.201 [scheduling-1] DEBUG c.m.a.c.service.MessageQueueService - 📋 Found 0 messages ready for processing
2025-07-02 10:40:21.201 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 📋 No messages ready for processing
2025-07-02 10:40:21.201 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - 🔄 Processing retry queue...
2025-07-02 10:40:21.218 [scheduling-1] INFO  c.m.apsp.core.service.RetryService - Found 1 errors ready for retry
2025-07-02 10:40:21.218 [scheduling-1] INFO  c.m.apsp.core.service.RetryService - 🔄 Processing retry for error log: 2 (attempt 1)
2025-07-02 10:40:21.233 [scheduling-1] WARN  c.m.apsp.core.service.RetryService - No message ID found for error log 2, cannot retry
2025-07-02 10:40:21.233 [scheduling-1] WARN  c.m.apsp.core.service.RetryService - ❌ Retry failed for error log: 2, scheduling next retry
2025-07-02 10:40:21.233 [scheduling-1] INFO  c.m.apsp.core.service.RetryService - ✅ Processed 1 retry attempts
2025-07-02 10:40:51.213 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 🔄 Starting message processing cycle
2025-07-02 10:40:51.213 [scheduling-1] DEBUG c.m.a.c.service.MessageQueueService - 📋 Found 0 messages ready for processing
2025-07-02 10:40:51.213 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 📋 No messages ready for processing
2025-07-02 10:40:51.259 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - 🔄 Processing retry queue...
2025-07-02 10:40:51.259 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - No errors ready for retry
2025-07-02 10:41:14.817 [SpringApplicationShutdownHook] INFO  c.m.a.c.service.AS4ConversionService - Shutting down AS4 service...
2025-07-02 10:41:14.817 [SpringApplicationShutdownHook] INFO  c.m.a.c.service.AS4ConversionService - ✅ Phase4 global scope cleaned up
2025-07-02 10:41:19.358 [main] INFO  com.morohub.apsp.AS4Application - Starting AS4Application using Java 21.0.3 with PID 26156 (C:\Work\as4-main\target\classes started by kushagrat in C:\Work\as4-main)
2025-07-02 10:41:19.358 [main] DEBUG com.morohub.apsp.AS4Application - Running with Spring Boot v3.2.0, Spring v6.1.0
2025-07-02 10:41:19.358 [main] INFO  com.morohub.apsp.AS4Application - The following 1 profile is active: "dummy"
2025-07-02 10:41:22.653 [main] INFO  c.m.a.c.Phase4ServletConfiguration - 🔧 Registering AS4 servlet for incoming message handling
2025-07-02 10:41:22.653 [main] INFO  c.m.a.c.Phase4ServletConfiguration - ✅ AS4 servlet registered at /reverse-flow/as4/*
2025-07-02 10:41:28.038 [main] INFO  c.m.a.config.AS4CryptoConfiguration - === Creating Configured AS4 Crypto Factory ===
2025-07-02 10:41:28.038 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:41:28.038 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:41:28.038 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:41:28.038 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:41:28.076 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Keystore loaded from: classpath: keystore/cert.p12
2025-07-02 10:41:28.076 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:41:28.076 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:41:28.076 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:41:28.076 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:41:28.091 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Truststore loaded from: classpath: keystore/cert.p12
2025-07-02 10:41:28.091 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Truststore loaded from: classpath: keystore/cert.p12
2025-07-02 10:41:28.091 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - Validating crypto configuration...
2025-07-02 10:41:28.091 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:41:28.091 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:41:28.138 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Crypto configuration validation successful
2025-07-02 10:41:28.138 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 🔑 Private key algorithm: RSA
2025-07-02 10:41:28.138 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 📜 Certificate subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE
2025-07-02 10:41:28.138 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 📅 Certificate valid from: Wed Jul 10 05:30:00 IST 2024 to: Wed Jul 01 05:29:59 IST 2026
2025-07-02 10:41:28.138 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ AS4 Crypto Factory configured successfully and set as default
2025-07-02 10:41:28.138 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 📁 Keystore: cert.p12
2025-07-02 10:41:28.138 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 🔑 Key Alias: cert
2025-07-02 10:41:28.138 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 🛡️ Truststore: cert.p12
2025-07-02 10:41:28.216 [main] INFO  c.m.a.c.CountryConfigurationService - Loading unified country configuration from: country-config.json
2025-07-02 10:41:28.216 [main] INFO  c.m.a.c.CountryConfigurationService - Resource location: classpath: country-config.json
2025-07-02 10:41:28.216 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: country-config.json
2025-07-02 10:41:28.216 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: country-config.json
2025-07-02 10:41:28.254 [main] INFO  c.m.a.c.CountryConfigurationService - ✅ Unified country configuration loaded successfully
2025-07-02 10:41:28.254 [main] INFO  c.m.a.c.CountryConfigurationService - 📊 Available countries: DE, FR, IT, NL, ES, DEFAULT
2025-07-02 10:41:28.348 [main] INFO  c.m.a.c.CountryConfigurationService - 📋 Configuration Summary:
2025-07-02 10:41:28.348 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 DE (Germany): 3 document types
2025-07-02 10:41:28.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:41:28.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:41:28.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:41:28.348 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 FR (France): 3 document types
2025-07-02 10:41:28.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-FR.sch
2025-07-02 10:41:28.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-FR.sch
2025-07-02 10:41:28.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-FR.sch
2025-07-02 10:41:28.348 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 IT (Italy): 3 document types
2025-07-02 10:41:28.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-IT.sch
2025-07-02 10:41:28.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-IT.sch
2025-07-02 10:41:28.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-IT.sch
2025-07-02 10:41:28.348 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 NL (Netherlands): 3 document types
2025-07-02 10:41:28.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-NL.sch
2025-07-02 10:41:28.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-NL.sch
2025-07-02 10:41:28.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-NL.sch
2025-07-02 10:41:28.348 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 ES (Spain): 3 document types
2025-07-02 10:41:28.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-ES.sch
2025-07-02 10:41:28.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-ES.sch
2025-07-02 10:41:28.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-ES.sch
2025-07-02 10:41:28.348 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 DEFAULT (Default/Generic): 3 document types
2025-07-02 10:41:28.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | null
2025-07-02 10:41:28.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:41:28.348 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL.sch
2025-07-02 10:41:28.348 [main] INFO  c.m.a.c.service.AS4ConversionService - === Initializing Production-Ready AS4 Service ===
2025-07-02 10:41:28.348 [main] INFO  c.m.a.c.service.AS4ConversionService - AS4 Mode: dummy
2025-07-02 10:41:28.348 [main] INFO  c.m.a.c.service.AS4ConversionService - Security Enabled: true
2025-07-02 10:41:28.348 [main] INFO  c.m.a.c.service.AS4ConversionService - Validation Enabled: false
2025-07-02 10:41:28.348 [main] INFO  c.m.a.c.service.AS4ConversionService - Endpoint: http://localhost:8081/reverse-flow/receive-as4-message
2025-07-02 10:41:28.348 [main] INFO  c.m.a.c.service.AS4ConversionService - === Checking Keystore Availability ===
2025-07-02 10:41:28.348 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:41:28.348 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:41:28.348 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Keystore found at: classpath: keystore/cert.p12
2025-07-02 10:41:28.348 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:41:28.348 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:41:28.364 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Private key accessible for alias: cert
2025-07-02 10:41:28.364 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Keystore validation successful
2025-07-02 10:41:28.364 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:41:28.364 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:41:28.364 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Truststore found at: classpath: keystore/cert.p12
2025-07-02 10:41:28.412 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Phase4 global scope initialized
2025-07-02 10:41:28.412 [main] INFO  c.m.a.c.service.AS4ConversionService - === Configuring Phase4 Crypto Settings ===
2025-07-02 10:41:28.412 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:41:28.412 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:41:28.412 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Crypto configuration is properly set up
2025-07-02 10:41:28.427 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 10:41:28.427 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 10:41:28.427 [main] INFO  c.m.a.c.service.AS4ConversionService - 📁 Keystore info: Keystore: cert.p12, Alias: cert, Subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE, Valid: Wed Jul 10 05:30:00 IST 2024 to Wed Jul 01 05:29:59 IST 2026
2025-07-02 10:41:28.620 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ MetaAS4Manager initialized
2025-07-02 10:41:28.620 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 service initialized successfully for dummy mode
2025-07-02 10:41:28.978 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-02 10:41:29.539 [main] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 🔧 Initializing AS4 Servlet for message processing
2025-07-02 10:41:29.539 [main] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - ✅ AS4 Servlet initialized successfully
2025-07-02 10:41:29.555 [main] INFO  com.morohub.apsp.AS4Application - Started AS4Application in 11.653 seconds (process running for 13.018)
2025-07-02 10:41:29.555 [main] INFO  c.m.a.c.ErrorMasterDataInitializer - 🔧 Initializing error master data...
2025-07-02 10:41:29.580 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 🔄 Starting message processing cycle
2025-07-02 10:41:29.733 [scheduling-1] DEBUG c.m.a.c.service.MessageQueueService - 📋 Found 0 messages ready for processing
2025-07-02 10:41:29.745 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 📋 No messages ready for processing
2025-07-02 10:41:29.745 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_001
2025-07-02 10:41:29.745 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_002
2025-07-02 10:41:29.745 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - 🔄 Processing retry queue...
2025-07-02 10:41:29.751 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_003
2025-07-02 10:41:29.755 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_004
2025-07-02 10:41:29.755 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_001
2025-07-02 10:41:29.755 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_002
2025-07-02 10:41:29.755 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_003
2025-07-02 10:41:29.755 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - No errors ready for retry
2025-07-02 10:41:29.755 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_004
2025-07-02 10:41:29.755 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_001
2025-07-02 10:41:29.755 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_002
2025-07-02 10:41:29.770 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_003
2025-07-02 10:41:29.770 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_004
2025-07-02 10:41:29.770 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_005
2025-07-02 10:41:29.770 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: SYS_001
2025-07-02 10:41:29.770 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: SYS_002
2025-07-02 10:41:29.770 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: SYS_003
2025-07-02 10:41:29.770 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_001
2025-07-02 10:41:29.786 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_002
2025-07-02 10:41:29.786 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_003
2025-07-02 10:41:29.786 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_004
2025-07-02 10:41:29.786 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: PEPPOL_001
2025-07-02 10:41:29.786 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: PEPPOL_002
2025-07-02 10:41:29.786 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: PEPPOL_003
2025-07-02 10:41:29.786 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: MLS_001
2025-07-02 10:41:29.802 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: MLS_002
2025-07-02 10:41:29.802 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: RATE_001
2025-07-02 10:41:29.802 [main] INFO  c.m.a.c.ErrorMasterDataInitializer - Initialized 26 error master data entries
2025-07-02 10:41:29.802 [main] INFO  c.m.a.c.ErrorMasterDataInitializer - ✅ Error master data initialization completed
2025-07-02 10:41:59.758 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 🔄 Starting message processing cycle
2025-07-02 10:41:59.758 [scheduling-1] DEBUG c.m.a.c.service.MessageQueueService - 📋 Found 0 messages ready for processing
2025-07-02 10:41:59.758 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 📋 No messages ready for processing
2025-07-02 10:41:59.773 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - 🔄 Processing retry queue...
2025-07-02 10:42:10.102 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - No errors ready for retry
2025-07-02 10:42:29.779 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 🔄 Starting message processing cycle
2025-07-02 10:42:29.779 [scheduling-1] DEBUG c.m.a.c.service.MessageQueueService - 📋 Found 0 messages ready for processing
2025-07-02 10:42:29.779 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 📋 No messages ready for processing
2025-07-02 10:42:40.124 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - 🔄 Processing retry queue...
2025-07-02 10:42:47.878 [scheduling-1] INFO  c.m.apsp.core.service.RetryService - Found 1 errors ready for retry
2025-07-02 10:42:56.100 [scheduling-1] INFO  c.m.apsp.core.service.RetryService - 🔄 Processing retry for error log: 2 (attempt 2)
2025-07-02 10:43:09.650 [scheduling-1] WARN  c.m.apsp.core.service.RetryService - No message ID found for error log 2, cannot retry
2025-07-02 10:43:15.468 [scheduling-1] WARN  c.m.apsp.core.service.RetryService - ❌ Retry failed for error log: 2, scheduling next retry
2025-07-02 10:44:07.701 [HikariPool-1 housekeeper] WARN  com.zaxxer.hikari.pool.HikariPool - HikariPool-1 - Thread starvation or clock leap detected (housekeeper delta=1m13s737ms341µs900ns).
2025-07-02 10:44:07.723 [scheduling-1] INFO  c.m.apsp.core.service.RetryService - ✅ Processed 1 retry attempts
2025-07-02 10:44:07.845 [SpringApplicationShutdownHook] INFO  c.m.a.c.service.AS4ConversionService - Shutting down AS4 service...
2025-07-02 10:44:07.845 [SpringApplicationShutdownHook] INFO  c.m.a.c.service.AS4ConversionService - ✅ Phase4 global scope cleaned up
2025-07-02 11:06:18.258 [main] INFO  com.morohub.apsp.AS4Application - Starting AS4Application using Java 21.0.3 with PID 20740 (C:\Work\as4-main\target\classes started by kushagrat in C:\Work\as4-main)
2025-07-02 11:06:18.258 [main] DEBUG com.morohub.apsp.AS4Application - Running with Spring Boot v3.2.0, Spring v6.1.0
2025-07-02 11:06:18.258 [main] INFO  com.morohub.apsp.AS4Application - The following 1 profile is active: "dummy"
2025-07-02 11:06:20.877 [main] INFO  c.m.a.c.Phase4ServletConfiguration - 🔧 Registering AS4 servlet for incoming message handling
2025-07-02 11:06:20.877 [main] INFO  c.m.a.c.Phase4ServletConfiguration - ✅ AS4 servlet registered at /reverse-flow/as4/*
2025-07-02 11:06:26.620 [main] INFO  c.m.a.config.AS4CryptoConfiguration - === Creating Configured AS4 Crypto Factory ===
2025-07-02 11:06:26.620 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 11:06:26.620 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 11:06:26.620 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 11:06:26.620 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 11:06:26.668 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Keystore loaded from: classpath: keystore/cert.p12
2025-07-02 11:06:26.668 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 11:06:26.668 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 11:06:26.668 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 11:06:26.668 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 11:06:26.683 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Truststore loaded from: classpath: keystore/cert.p12
2025-07-02 11:06:26.683 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Truststore loaded from: classpath: keystore/cert.p12
2025-07-02 11:06:26.683 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - Validating crypto configuration...
2025-07-02 11:06:26.683 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 11:06:26.683 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 11:06:26.715 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Crypto configuration validation successful
2025-07-02 11:06:26.715 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 🔑 Private key algorithm: RSA
2025-07-02 11:06:26.715 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 📜 Certificate subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE
2025-07-02 11:06:26.715 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 📅 Certificate valid from: Wed Jul 10 05:30:00 IST 2024 to: Wed Jul 01 05:29:59 IST 2026
2025-07-02 11:06:26.715 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ AS4 Crypto Factory configured successfully and set as default
2025-07-02 11:06:26.715 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 📁 Keystore: cert.p12
2025-07-02 11:06:26.715 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 🔑 Key Alias: cert
2025-07-02 11:06:26.715 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 🛡️ Truststore: cert.p12
2025-07-02 11:06:26.779 [main] INFO  c.m.a.c.CountryConfigurationService - Loading unified country configuration from: country-config.json
2025-07-02 11:06:26.779 [main] INFO  c.m.a.c.CountryConfigurationService - Resource location: classpath: country-config.json
2025-07-02 11:06:26.779 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: country-config.json
2025-07-02 11:06:26.779 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: country-config.json
2025-07-02 11:06:26.810 [main] INFO  c.m.a.c.CountryConfigurationService - ✅ Unified country configuration loaded successfully
2025-07-02 11:06:26.810 [main] INFO  c.m.a.c.CountryConfigurationService - 📊 Available countries: DE, FR, IT, NL, ES, DEFAULT
2025-07-02 11:06:26.888 [main] INFO  c.m.a.c.CountryConfigurationService - 📋 Configuration Summary:
2025-07-02 11:06:26.888 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 DE (Germany): 3 document types
2025-07-02 11:06:26.888 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL.sch
2025-07-02 11:06:26.888 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL.sch
2025-07-02 11:06:26.888 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL.sch
2025-07-02 11:06:26.888 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 FR (France): 3 document types
2025-07-02 11:06:26.888 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-FR.sch
2025-07-02 11:06:26.888 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-FR.sch
2025-07-02 11:06:26.888 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-FR.sch
2025-07-02 11:06:26.888 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 IT (Italy): 3 document types
2025-07-02 11:06:26.888 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-IT.sch
2025-07-02 11:06:26.888 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-IT.sch
2025-07-02 11:06:26.888 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-IT.sch
2025-07-02 11:06:26.888 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 NL (Netherlands): 3 document types
2025-07-02 11:06:26.888 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-NL.sch
2025-07-02 11:06:26.888 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-NL.sch
2025-07-02 11:06:26.888 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-NL.sch
2025-07-02 11:06:26.888 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 ES (Spain): 3 document types
2025-07-02 11:06:26.888 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-ES.sch
2025-07-02 11:06:26.888 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-ES.sch
2025-07-02 11:06:26.888 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-ES.sch
2025-07-02 11:06:26.888 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 DEFAULT (Default/Generic): 3 document types
2025-07-02 11:06:26.888 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | null
2025-07-02 11:06:26.888 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL.sch
2025-07-02 11:06:26.888 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL.sch
2025-07-02 11:06:26.888 [main] INFO  c.m.a.c.service.AS4ConversionService - === Initializing Production-Ready AS4 Service ===
2025-07-02 11:06:26.888 [main] INFO  c.m.a.c.service.AS4ConversionService - AS4 Mode: dummy
2025-07-02 11:06:26.888 [main] INFO  c.m.a.c.service.AS4ConversionService - Security Enabled: true
2025-07-02 11:06:26.888 [main] INFO  c.m.a.c.service.AS4ConversionService - Validation Enabled: false
2025-07-02 11:06:26.888 [main] INFO  c.m.a.c.service.AS4ConversionService - Endpoint: http://localhost:8081/reverse-flow/receive-as4-message
2025-07-02 11:06:26.888 [main] INFO  c.m.a.c.service.AS4ConversionService - === Checking Keystore Availability ===
2025-07-02 11:06:26.888 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 11:06:26.888 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 11:06:26.888 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Keystore found at: classpath: keystore/cert.p12
2025-07-02 11:06:26.888 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 11:06:26.888 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 11:06:26.904 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Private key accessible for alias: cert
2025-07-02 11:06:26.904 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Keystore validation successful
2025-07-02 11:06:26.904 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 11:06:26.904 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 11:06:26.904 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Truststore found at: classpath: keystore/cert.p12
2025-07-02 11:06:26.935 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Phase4 global scope initialized
2025-07-02 11:06:26.935 [main] INFO  c.m.a.c.service.AS4ConversionService - === Configuring Phase4 Crypto Settings ===
2025-07-02 11:06:26.935 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 11:06:26.935 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 11:06:26.935 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Crypto configuration is properly set up
2025-07-02 11:06:26.950 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 11:06:26.950 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 11:06:26.950 [main] INFO  c.m.a.c.service.AS4ConversionService - 📁 Keystore info: Keystore: cert.p12, Alias: cert, Subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE, Valid: Wed Jul 10 05:30:00 IST 2024 to Wed Jul 01 05:29:59 IST 2026
2025-07-02 11:06:27.124 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ MetaAS4Manager initialized
2025-07-02 11:06:27.124 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 service initialized successfully for dummy mode
2025-07-02 11:06:27.426 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-02 11:06:27.942 [main] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 🔧 Initializing AS4 Servlet for message processing
2025-07-02 11:06:27.942 [main] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - ✅ AS4 Servlet initialized successfully
2025-07-02 11:06:27.957 [main] INFO  com.morohub.apsp.AS4Application - Started AS4Application in 10.59 seconds (process running for 11.299)
2025-07-02 11:06:27.973 [main] INFO  c.m.a.c.ErrorMasterDataInitializer - 🔧 Initializing error master data...
2025-07-02 11:06:27.995 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 🔄 Starting message processing cycle
2025-07-02 11:06:28.112 [scheduling-1] DEBUG c.m.a.c.service.MessageQueueService - 📋 Found 0 messages ready for processing
2025-07-02 11:06:28.112 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_001
2025-07-02 11:06:28.112 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 📋 No messages ready for processing
2025-07-02 11:06:28.112 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - 🔄 Processing retry queue...
2025-07-02 11:06:28.128 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_002
2025-07-02 11:06:28.128 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_003
2025-07-02 11:06:28.128 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_004
2025-07-02 11:06:28.128 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_001
2025-07-02 11:06:28.128 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_002
2025-07-02 11:06:28.128 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_003
2025-07-02 11:06:28.128 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - No errors ready for retry
2025-07-02 11:06:28.128 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_004
2025-07-02 11:06:28.144 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_001
2025-07-02 11:06:28.144 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_002
2025-07-02 11:06:28.144 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_003
2025-07-02 11:06:28.144 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_004
2025-07-02 11:06:28.144 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_005
2025-07-02 11:06:28.144 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: SYS_001
2025-07-02 11:06:28.144 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: SYS_002
2025-07-02 11:06:28.144 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: SYS_003
2025-07-02 11:06:28.159 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_001
2025-07-02 11:06:28.159 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_002
2025-07-02 11:06:28.159 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_003
2025-07-02 11:06:28.159 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_004
2025-07-02 11:06:28.159 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: PEPPOL_001
2025-07-02 11:06:28.159 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: PEPPOL_002
2025-07-02 11:06:28.159 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: PEPPOL_003
2025-07-02 11:06:28.159 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: MLS_001
2025-07-02 11:06:28.159 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: MLS_002
2025-07-02 11:06:28.175 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: RATE_001
2025-07-02 11:06:28.175 [main] INFO  c.m.a.c.ErrorMasterDataInitializer - Initialized 26 error master data entries
2025-07-02 11:06:28.175 [main] INFO  c.m.a.c.ErrorMasterDataInitializer - ✅ Error master data initialization completed
2025-07-02 11:06:42.601 [http-nio-8081-exec-2] INFO  c.m.a.a.c.PeppolSbdInvoiceController - Processing Peppol SBD Invoice request: 67e61a8a-5515-44c6-beb8-a10491b51d5f for country: DEFAULT and document type: INVOICE
2025-07-02 11:06:42.708 [http-nio-8081-exec-2] INFO  c.m.a.c.service.MessageQueueService - ✅ Added PEPPOL_SBD_INVOICE message to processing queue: MSG-7d35bcb6-ed03-491b-8ff0-889f13f90e98
2025-07-02 11:06:42.717 [http-nio-8081-exec-2] INFO  c.m.a.a.c.PeppolSbdInvoiceController - ✅ Peppol SBD Invoice request added to processing queue: 67e61a8a-5515-44c6-beb8-a10491b51d5f
2025-07-02 11:06:42.719 [http-nio-8081-exec-2] INFO  c.m.a.a.c.PeppolSbdInvoiceController - Using UBL class: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType for DEFAULT/INVOICE
2025-07-02 11:06:42.840 [http-nio-8081-exec-2] ERROR c.m.a.a.c.PeppolSbdInvoiceController - Failed to log invoice generation result
org.springframework.web.client.HttpServerErrorException$InternalServerError: 500 : "Error: JSON parse error: Cannot deserialize value of type `java.time.LocalDate` from String "UNKNOWN": Failed to deserialize java.time.LocalDate: (java.time.format.DateTimeParseException) Text 'UNKNOWN' could not be parsed at index 0"
	at org.springframework.web.client.HttpServerErrorException.create(HttpServerErrorException.java:102)
	at org.springframework.web.client.DefaultResponseErrorHandler.handleError(DefaultResponseErrorHandler.java:186)
	at org.springframework.web.client.DefaultResponseErrorHandler.handleError(DefaultResponseErrorHandler.java:137)
	at org.springframework.web.client.ResponseErrorHandler.handleError(ResponseErrorHandler.java:63)
	at org.springframework.web.client.RestTemplate.handleResponse(RestTemplate.java:932)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:881)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:781)
	at org.springframework.web.client.RestTemplate.postForEntity(RestTemplate.java:529)
	at com.morohub.apsp.api.controller.PeppolSbdInvoiceController.sendPeppolSbdInvoice(PeppolSbdInvoiceController.java:199)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:254)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:182)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:06:42.884 [http-nio-8081-exec-2] INFO  c.m.a.c.s.ErrorManagementService - Error logged: CONN_005 for message: null
2025-07-02 11:06:42.892 [http-nio-8081-exec-2] WARN  o.s.w.s.m.m.a.ExceptionHandlerExceptionResolver - Failure in @ExceptionHandler com.morohub.apsp.config.GlobalExceptionHandler#handleConnectivityException(ConnectivityException)
java.lang.NullPointerException: Cannot invoke "java.util.Map.put(Object, Object)" because the return value of "com.morohub.apsp.common.dto.ErrorResponse.getAdditionalInfo()" is null
	at com.morohub.apsp.config.GlobalExceptionHandler.handleConnectivityException(GlobalExceptionHandler.java:130)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:254)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:182)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.ExceptionHandlerExceptionResolver.doResolveHandlerMethodException(ExceptionHandlerExceptionResolver.java:432)
	at org.springframework.web.servlet.handler.AbstractHandlerMethodExceptionResolver.doResolveException(AbstractHandlerMethodExceptionResolver.java:74)
	at org.springframework.web.servlet.handler.AbstractHandlerExceptionResolver.resolveException(AbstractHandlerExceptionResolver.java:161)
	at org.springframework.web.servlet.handler.HandlerExceptionResolverComposite.resolveException(HandlerExceptionResolverComposite.java:80)
	at org.springframework.web.servlet.DispatcherServlet.processHandlerException(DispatcherServlet.java:1357)
	at org.springframework.web.servlet.DispatcherServlet.processDispatchResult(DispatcherServlet.java:1160)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1106)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:06:42.895 [http-nio-8081-exec-2] ERROR o.a.c.c.C.[.[.[.[dispatcherServlet] - Servlet.service() for servlet [dispatcherServlet] in context with path [] threw exception [Request processing failed: ConnectivityException{errorCode='CONN_005', endpoint='http://localhost:8090/api/secure/decrypt-invoice/0235:1234567891', httpStatusCode=0, responseTime=0, message='Failed to connect to decryption service: 500 : "Error: Authorization failed due to wrong key"'}] with root cause
org.springframework.web.client.HttpServerErrorException$InternalServerError: 500 : "Error: Authorization failed due to wrong key"
	at org.springframework.web.client.HttpServerErrorException.create(HttpServerErrorException.java:102)
	at org.springframework.web.client.DefaultResponseErrorHandler.handleError(DefaultResponseErrorHandler.java:186)
	at org.springframework.web.client.DefaultResponseErrorHandler.handleError(DefaultResponseErrorHandler.java:137)
	at org.springframework.web.client.ResponseErrorHandler.handleError(ResponseErrorHandler.java:63)
	at org.springframework.web.client.RestTemplate.handleResponse(RestTemplate.java:932)
	at org.springframework.web.client.RestTemplate.doExecute(RestTemplate.java:881)
	at org.springframework.web.client.RestTemplate.execute(RestTemplate.java:781)
	at org.springframework.web.client.RestTemplate.postForEntity(RestTemplate.java:529)
	at com.morohub.apsp.api.controller.PeppolSbdInvoiceController.sendPeppolSbdInvoice(PeppolSbdInvoiceController.java:124)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:254)
	at org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:182)
	at org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)
	at org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)
	at org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)
	at org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)
	at org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)
	at org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)
	at org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)
	at org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)
	at jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:205)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)
	at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)
	at org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:174)
	at org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:149)
	at org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)
	at org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)
	at org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:482)
	at org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)
	at org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)
	at org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)
	at org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:340)
	at org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:391)
	at org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)
	at org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:896)
	at org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1744)
	at org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1191)
	at org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)
	at org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:61)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:06:58.141 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 🔄 Starting message processing cycle
2025-07-02 11:06:58.141 [scheduling-1] DEBUG c.m.a.c.service.MessageQueueService - 📋 Found 1 messages ready for processing
2025-07-02 11:06:58.141 [scheduling-1] INFO  c.m.a.c.s.MessageProcessingService - 📦 Found 1 messages ready for processing
2025-07-02 11:06:58.141 [scheduling-1] INFO  c.m.a.c.s.MessageProcessingService - 🚀 Started processing 1 messages
2025-07-02 11:06:58.141 [pool-2-thread-1] INFO  c.m.a.c.s.MessageProcessingService - 🔄 Processing PEPPOL_SBD_INVOICE message: MSG-7d35bcb6-ed03-491b-8ff0-889f13f90e98
2025-07-02 11:06:58.141 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - 🔄 Processing retry queue...
2025-07-02 11:06:58.156 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - No errors ready for retry
2025-07-02 11:06:58.156 [pool-2-thread-1] DEBUG c.m.a.c.service.MessageQueueService - 📝 Updated message MSG-7d35bcb6-ed03-491b-8ff0-889f13f90e98 status to: PROCESSING
2025-07-02 11:06:58.188 [pool-2-thread-1] INFO  c.m.a.c.s.FlowSpecificRetryService - 🔄 Processing retry for PEPPOL_SBD_INVOICE flow, message: MSG-7d35bcb6-ed03-491b-8ff0-889f13f90e98
2025-07-02 11:06:58.188 [pool-2-thread-1] INFO  c.m.a.c.s.FlowSpecificRetryService - 🔄 Retrying Peppol SBD Invoice for message: MSG-7d35bcb6-ed03-491b-8ff0-889f13f90e98
2025-07-02 11:06:58.188 [pool-2-thread-1] INFO  c.m.apsp.core.service.InvoiceService - Generating invoice XML for country: DEFAULT and document type: INVOICE
2025-07-02 11:06:58.188 [pool-2-thread-1] DEBUG c.m.a.c.service.XmlGeneratorService - Generating invoice XML using country-specific configuration: DEFAULT/INVOICE
2025-07-02 11:06:58.188 [pool-2-thread-1] INFO  c.m.a.c.service.XmlGeneratorService - Using classes for DEFAULT/INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType and oasis.names.specification.ubl.schema.xsd.invoice_2.ObjectFactory
2025-07-02 11:06:58.188 [pool-2-thread-1] ERROR c.m.a.c.service.XmlGeneratorService - Validation error generating invoice XML for DEFAULT/INVOICE: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:06:58.188 [pool-2-thread-1] ERROR c.m.apsp.core.service.InvoiceService - Validation error generating invoice for DEFAULT/INVOICE: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:06:58.188 [pool-2-thread-1] ERROR c.m.a.c.s.FlowSpecificRetryService - ❌ Exception during Peppol SBD Invoice retry for message MSG-7d35bcb6-ed03-491b-8ff0-889f13f90e98: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:06:58.188 [pool-2-thread-1] INFO  c.m.a.c.service.MessageQueueService - 🔄 Message MSG-7d35bcb6-ed03-491b-8ff0-889f13f90e98 marked for retry (attempt 1)
2025-07-02 11:07:08.107 [http-nio-8081-exec-4] INFO  c.m.a.a.c.PeppolSbdInvoiceController - Processing Peppol SBD Invoice request: 723ce282-6ee7-4b5a-b83b-b127cc2920f4 for country: DEFAULT and document type: INVOICE
2025-07-02 11:07:08.112 [http-nio-8081-exec-4] INFO  c.m.a.c.service.MessageQueueService - ✅ Added PEPPOL_SBD_INVOICE message to processing queue: MSG-de3c612a-26f1-4f45-af20-83bc6ef3b710
2025-07-02 11:07:08.113 [http-nio-8081-exec-4] INFO  c.m.a.a.c.PeppolSbdInvoiceController - ✅ Peppol SBD Invoice request added to processing queue: 723ce282-6ee7-4b5a-b83b-b127cc2920f4
2025-07-02 11:07:08.114 [http-nio-8081-exec-4] INFO  c.m.a.a.c.PeppolSbdInvoiceController - Using UBL class: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType for DEFAULT/INVOICE
2025-07-02 11:07:08.814 [http-nio-8081-exec-4] INFO  c.m.apsp.core.service.InvoiceService - Generating invoice XML for country: DEFAULT and document type: INVOICE
2025-07-02 11:07:08.814 [http-nio-8081-exec-4] DEBUG c.m.a.c.service.XmlGeneratorService - Generating invoice XML using country-specific configuration: DEFAULT/INVOICE
2025-07-02 11:07:08.814 [http-nio-8081-exec-4] INFO  c.m.a.c.service.XmlGeneratorService - Using classes for DEFAULT/INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType and oasis.names.specification.ubl.schema.xsd.invoice_2.ObjectFactory
2025-07-02 11:07:08.816 [http-nio-8081-exec-4] DEBUG c.m.a.c.s.XmlJsonConversionService - 🔍 Creating UBL JAXB context for package: oasis.names.specification.ubl.schema.xsd.invoice_2
2025-07-02 11:07:08.816 [http-nio-8081-exec-4] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.invoice_2
2025-07-02 11:07:08.854 [http-nio-8081-exec-4] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.commonaggregatecomponents_2
2025-07-02 11:07:08.916 [http-nio-8081-exec-4] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.commonbasiccomponents_2
2025-07-02 11:07:08.918 [http-nio-8081-exec-4] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.commonextensioncomponents_2
2025-07-02 11:07:08.919 [http-nio-8081-exec-4] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.creditnote_2
2025-07-02 11:07:08.919 [http-nio-8081-exec-4] DEBUG c.m.a.c.s.XmlJsonConversionService - 🔍 Using UBL context path: oasis.names.specification.ubl.schema.xsd.invoice_2:oasis.names.specification.ubl.schema.xsd.commonaggregatecomponents_2:oasis.names.specification.ubl.schema.xsd.commonbasiccomponents_2:oasis.names.specification.ubl.schema.xsd.commonextensioncomponents_2:oasis.names.specification.ubl.schema.xsd.creditnote_2
2025-07-02 11:07:09.587 [http-nio-8081-exec-4] INFO  c.m.a.c.service.XmlGeneratorService - Successfully generated XML for DEFAULT/INVOICE
2025-07-02 11:07:09.588 [http-nio-8081-exec-4] DEBUG c.m.a.core.service.ValidationService - Validating XML using 2 schematron files
2025-07-02 11:07:09.601 [http-nio-8081-exec-4] DEBUG c.m.a.core.service.ValidationService - Validating with schematron file 1 of 2: PINT-UBL-validation-preprocessed.sch
2025-07-02 11:07:09.601 [http-nio-8081-exec-4] DEBUG c.m.a.core.service.ValidationService - Validating XML using schematron file: PINT-UBL-validation-preprocessed.sch
2025-07-02 11:07:09.606 [http-nio-8081-exec-4] INFO  c.m.a.core.service.ValidationService - Country-specific schematron validation passed for: PINT-UBL-validation-preprocessed.sch
2025-07-02 11:07:09.606 [http-nio-8081-exec-4] DEBUG c.m.a.core.service.ValidationService - Validating with schematron file 2 of 2: PINT-jurisdiction-aligned-rules.sch
2025-07-02 11:07:09.606 [http-nio-8081-exec-4] DEBUG c.m.a.core.service.ValidationService - Validating XML using schematron file: PINT-jurisdiction-aligned-rules.sch
2025-07-02 11:07:09.609 [http-nio-8081-exec-4] INFO  c.m.a.core.service.ValidationService - Country-specific schematron validation passed for: PINT-jurisdiction-aligned-rules.sch
2025-07-02 11:07:09.609 [http-nio-8081-exec-4] INFO  c.m.a.core.service.ValidationService - Country-specific schematron validation passed for all 2 files
2025-07-02 11:07:09.609 [http-nio-8081-exec-4] INFO  c.m.apsp.core.service.InvoiceService - Invoice XML generated and validated successfully for DEFAULT/INVOICE
2025-07-02 11:07:28.160 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 🔄 Starting message processing cycle
2025-07-02 11:07:28.160 [scheduling-1] DEBUG c.m.a.c.service.MessageQueueService - 📋 Found 2 messages ready for processing
2025-07-02 11:07:28.160 [scheduling-1] INFO  c.m.a.c.s.MessageProcessingService - 📦 Found 2 messages ready for processing
2025-07-02 11:07:28.160 [scheduling-1] INFO  c.m.a.c.s.MessageProcessingService - 🚀 Started processing 2 messages
2025-07-02 11:07:28.160 [pool-2-thread-3] INFO  c.m.a.c.s.MessageProcessingService - 🔄 Processing PEPPOL_SBD_INVOICE message: MSG-de3c612a-26f1-4f45-af20-83bc6ef3b710
2025-07-02 11:07:28.160 [pool-2-thread-2] INFO  c.m.a.c.s.MessageProcessingService - 🔄 Processing PEPPOL_SBD_INVOICE message: MSG-7d35bcb6-ed03-491b-8ff0-889f13f90e98
2025-07-02 11:07:28.160 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - 🔄 Processing retry queue...
2025-07-02 11:07:28.175 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - No errors ready for retry
2025-07-02 11:07:28.175 [pool-2-thread-3] DEBUG c.m.a.c.service.MessageQueueService - 📝 Updated message MSG-de3c612a-26f1-4f45-af20-83bc6ef3b710 status to: PROCESSING
2025-07-02 11:07:28.175 [pool-2-thread-2] DEBUG c.m.a.c.service.MessageQueueService - 📝 Updated message MSG-7d35bcb6-ed03-491b-8ff0-889f13f90e98 status to: PROCESSING
2025-07-02 11:07:28.175 [pool-2-thread-3] INFO  c.m.a.c.s.FlowSpecificRetryService - 🔄 Processing retry for PEPPOL_SBD_INVOICE flow, message: MSG-de3c612a-26f1-4f45-af20-83bc6ef3b710
2025-07-02 11:07:28.175 [pool-2-thread-3] INFO  c.m.a.c.s.FlowSpecificRetryService - 🔄 Retrying Peppol SBD Invoice for message: MSG-de3c612a-26f1-4f45-af20-83bc6ef3b710
2025-07-02 11:07:28.175 [pool-2-thread-3] INFO  c.m.apsp.core.service.InvoiceService - Generating invoice XML for country: DEFAULT and document type: INVOICE
2025-07-02 11:07:28.175 [pool-2-thread-3] DEBUG c.m.a.c.service.XmlGeneratorService - Generating invoice XML using country-specific configuration: DEFAULT/INVOICE
2025-07-02 11:07:28.175 [pool-2-thread-3] INFO  c.m.a.c.service.XmlGeneratorService - Using classes for DEFAULT/INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType and oasis.names.specification.ubl.schema.xsd.invoice_2.ObjectFactory
2025-07-02 11:07:28.175 [pool-2-thread-3] ERROR c.m.a.c.service.XmlGeneratorService - Validation error generating invoice XML for DEFAULT/INVOICE: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:07:28.175 [pool-2-thread-3] ERROR c.m.apsp.core.service.InvoiceService - Validation error generating invoice for DEFAULT/INVOICE: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:07:28.175 [pool-2-thread-3] ERROR c.m.a.c.s.FlowSpecificRetryService - ❌ Exception during Peppol SBD Invoice retry for message MSG-de3c612a-26f1-4f45-af20-83bc6ef3b710: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:07:28.175 [pool-2-thread-2] INFO  c.m.a.c.s.FlowSpecificRetryService - 🔄 Processing retry for PEPPOL_SBD_INVOICE flow, message: MSG-7d35bcb6-ed03-491b-8ff0-889f13f90e98
2025-07-02 11:07:28.175 [pool-2-thread-2] INFO  c.m.a.c.s.FlowSpecificRetryService - 🔄 Retrying Peppol SBD Invoice for message: MSG-7d35bcb6-ed03-491b-8ff0-889f13f90e98
2025-07-02 11:07:28.175 [pool-2-thread-2] INFO  c.m.apsp.core.service.InvoiceService - Generating invoice XML for country: DEFAULT and document type: INVOICE
2025-07-02 11:07:28.175 [pool-2-thread-2] DEBUG c.m.a.c.service.XmlGeneratorService - Generating invoice XML using country-specific configuration: DEFAULT/INVOICE
2025-07-02 11:07:28.175 [pool-2-thread-2] INFO  c.m.a.c.service.XmlGeneratorService - Using classes for DEFAULT/INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType and oasis.names.specification.ubl.schema.xsd.invoice_2.ObjectFactory
2025-07-02 11:07:28.175 [pool-2-thread-2] ERROR c.m.a.c.service.XmlGeneratorService - Validation error generating invoice XML for DEFAULT/INVOICE: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:07:28.175 [pool-2-thread-2] ERROR c.m.apsp.core.service.InvoiceService - Validation error generating invoice for DEFAULT/INVOICE: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:07:28.175 [pool-2-thread-2] ERROR c.m.a.c.s.FlowSpecificRetryService - ❌ Exception during Peppol SBD Invoice retry for message MSG-7d35bcb6-ed03-491b-8ff0-889f13f90e98: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:07:28.191 [pool-2-thread-3] INFO  c.m.a.c.service.MessageQueueService - 🔄 Message MSG-de3c612a-26f1-4f45-af20-83bc6ef3b710 marked for retry (attempt 1)
2025-07-02 11:07:28.191 [pool-2-thread-2] INFO  c.m.a.c.service.MessageQueueService - 🔄 Message MSG-7d35bcb6-ed03-491b-8ff0-889f13f90e98 marked for retry (attempt 2)
2025-07-02 11:07:58.176 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 🔄 Starting message processing cycle
2025-07-02 11:07:58.176 [scheduling-1] DEBUG c.m.a.c.service.MessageQueueService - 📋 Found 2 messages ready for processing
2025-07-02 11:07:58.176 [scheduling-1] INFO  c.m.a.c.s.MessageProcessingService - 📦 Found 2 messages ready for processing
2025-07-02 11:07:58.176 [pool-2-thread-4] INFO  c.m.a.c.s.MessageProcessingService - 🔄 Processing PEPPOL_SBD_INVOICE message: MSG-7d35bcb6-ed03-491b-8ff0-889f13f90e98
2025-07-02 11:07:58.176 [scheduling-1] INFO  c.m.a.c.s.MessageProcessingService - 🚀 Started processing 2 messages
2025-07-02 11:07:58.176 [pool-2-thread-5] INFO  c.m.a.c.s.MessageProcessingService - 🔄 Processing PEPPOL_SBD_INVOICE message: MSG-de3c612a-26f1-4f45-af20-83bc6ef3b710
2025-07-02 11:07:58.176 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - 🔄 Processing retry queue...
2025-07-02 11:07:58.176 [pool-2-thread-4] DEBUG c.m.a.c.service.MessageQueueService - 📝 Updated message MSG-7d35bcb6-ed03-491b-8ff0-889f13f90e98 status to: PROCESSING
2025-07-02 11:07:58.176 [pool-2-thread-5] DEBUG c.m.a.c.service.MessageQueueService - 📝 Updated message MSG-de3c612a-26f1-4f45-af20-83bc6ef3b710 status to: PROCESSING
2025-07-02 11:07:58.176 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - No errors ready for retry
2025-07-02 11:07:58.176 [pool-2-thread-4] INFO  c.m.a.c.s.FlowSpecificRetryService - 🔄 Processing retry for PEPPOL_SBD_INVOICE flow, message: MSG-7d35bcb6-ed03-491b-8ff0-889f13f90e98
2025-07-02 11:07:58.176 [pool-2-thread-4] INFO  c.m.a.c.s.FlowSpecificRetryService - 🔄 Retrying Peppol SBD Invoice for message: MSG-7d35bcb6-ed03-491b-8ff0-889f13f90e98
2025-07-02 11:07:58.176 [pool-2-thread-4] INFO  c.m.apsp.core.service.InvoiceService - Generating invoice XML for country: DEFAULT and document type: INVOICE
2025-07-02 11:07:58.192 [pool-2-thread-4] DEBUG c.m.a.c.service.XmlGeneratorService - Generating invoice XML using country-specific configuration: DEFAULT/INVOICE
2025-07-02 11:07:58.192 [pool-2-thread-4] INFO  c.m.a.c.service.XmlGeneratorService - Using classes for DEFAULT/INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType and oasis.names.specification.ubl.schema.xsd.invoice_2.ObjectFactory
2025-07-02 11:07:58.192 [pool-2-thread-4] ERROR c.m.a.c.service.XmlGeneratorService - Validation error generating invoice XML for DEFAULT/INVOICE: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:07:58.192 [pool-2-thread-4] ERROR c.m.apsp.core.service.InvoiceService - Validation error generating invoice for DEFAULT/INVOICE: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:07:58.192 [pool-2-thread-4] ERROR c.m.a.c.s.FlowSpecificRetryService - ❌ Exception during Peppol SBD Invoice retry for message MSG-7d35bcb6-ed03-491b-8ff0-889f13f90e98: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:07:58.192 [pool-2-thread-5] INFO  c.m.a.c.s.FlowSpecificRetryService - 🔄 Processing retry for PEPPOL_SBD_INVOICE flow, message: MSG-de3c612a-26f1-4f45-af20-83bc6ef3b710
2025-07-02 11:07:58.192 [pool-2-thread-5] INFO  c.m.a.c.s.FlowSpecificRetryService - 🔄 Retrying Peppol SBD Invoice for message: MSG-de3c612a-26f1-4f45-af20-83bc6ef3b710
2025-07-02 11:07:58.192 [pool-2-thread-5] INFO  c.m.apsp.core.service.InvoiceService - Generating invoice XML for country: DEFAULT and document type: INVOICE
2025-07-02 11:07:58.192 [pool-2-thread-5] DEBUG c.m.a.c.service.XmlGeneratorService - Generating invoice XML using country-specific configuration: DEFAULT/INVOICE
2025-07-02 11:07:58.192 [pool-2-thread-5] INFO  c.m.a.c.service.XmlGeneratorService - Using classes for DEFAULT/INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType and oasis.names.specification.ubl.schema.xsd.invoice_2.ObjectFactory
2025-07-02 11:07:58.192 [pool-2-thread-5] ERROR c.m.a.c.service.XmlGeneratorService - Validation error generating invoice XML for DEFAULT/INVOICE: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:07:58.192 [pool-2-thread-5] ERROR c.m.apsp.core.service.InvoiceService - Validation error generating invoice for DEFAULT/INVOICE: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:07:58.192 [pool-2-thread-5] ERROR c.m.a.c.s.FlowSpecificRetryService - ❌ Exception during Peppol SBD Invoice retry for message MSG-de3c612a-26f1-4f45-af20-83bc6ef3b710: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:07:58.192 [pool-2-thread-4] WARN  c.m.a.c.service.MessageQueueService - ⚠️ Message MSG-7d35bcb6-ed03-491b-8ff0-889f13f90e98 exceeded max retries, marking as ERROR
2025-07-02 11:07:58.192 [pool-2-thread-5] INFO  c.m.a.c.service.MessageQueueService - 🔄 Message MSG-de3c612a-26f1-4f45-af20-83bc6ef3b710 marked for retry (attempt 2)
2025-07-02 11:08:28.187 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 🔄 Starting message processing cycle
2025-07-02 11:08:28.187 [scheduling-1] DEBUG c.m.a.c.service.MessageQueueService - 📋 Found 1 messages ready for processing
2025-07-02 11:08:28.187 [scheduling-1] INFO  c.m.a.c.s.MessageProcessingService - 📦 Found 1 messages ready for processing
2025-07-02 11:08:28.187 [scheduling-1] INFO  c.m.a.c.s.MessageProcessingService - 🚀 Started processing 1 messages
2025-07-02 11:08:28.187 [pool-2-thread-1] INFO  c.m.a.c.s.MessageProcessingService - 🔄 Processing PEPPOL_SBD_INVOICE message: MSG-de3c612a-26f1-4f45-af20-83bc6ef3b710
2025-07-02 11:08:28.187 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - 🔄 Processing retry queue...
2025-07-02 11:08:28.187 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - No errors ready for retry
2025-07-02 11:08:28.187 [pool-2-thread-1] DEBUG c.m.a.c.service.MessageQueueService - 📝 Updated message MSG-de3c612a-26f1-4f45-af20-83bc6ef3b710 status to: PROCESSING
2025-07-02 11:08:28.203 [pool-2-thread-1] INFO  c.m.a.c.s.FlowSpecificRetryService - 🔄 Processing retry for PEPPOL_SBD_INVOICE flow, message: MSG-de3c612a-26f1-4f45-af20-83bc6ef3b710
2025-07-02 11:08:28.203 [pool-2-thread-1] INFO  c.m.a.c.s.FlowSpecificRetryService - 🔄 Retrying Peppol SBD Invoice for message: MSG-de3c612a-26f1-4f45-af20-83bc6ef3b710
2025-07-02 11:08:28.203 [pool-2-thread-1] INFO  c.m.apsp.core.service.InvoiceService - Generating invoice XML for country: DEFAULT and document type: INVOICE
2025-07-02 11:08:28.203 [pool-2-thread-1] DEBUG c.m.a.c.service.XmlGeneratorService - Generating invoice XML using country-specific configuration: DEFAULT/INVOICE
2025-07-02 11:08:28.203 [pool-2-thread-1] INFO  c.m.a.c.service.XmlGeneratorService - Using classes for DEFAULT/INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType and oasis.names.specification.ubl.schema.xsd.invoice_2.ObjectFactory
2025-07-02 11:08:28.203 [pool-2-thread-1] ERROR c.m.a.c.service.XmlGeneratorService - Validation error generating invoice XML for DEFAULT/INVOICE: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:08:28.203 [pool-2-thread-1] ERROR c.m.apsp.core.service.InvoiceService - Validation error generating invoice for DEFAULT/INVOICE: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:08:28.203 [pool-2-thread-1] ERROR c.m.a.c.s.FlowSpecificRetryService - ❌ Exception during Peppol SBD Invoice retry for message MSG-de3c612a-26f1-4f45-af20-83bc6ef3b710: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:08:28.219 [pool-2-thread-1] WARN  c.m.a.c.service.MessageQueueService - ⚠️ Message MSG-de3c612a-26f1-4f45-af20-83bc6ef3b710 exceeded max retries, marking as ERROR
2025-07-02 11:08:58.210 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 🔄 Starting message processing cycle
2025-07-02 11:08:58.210 [scheduling-1] DEBUG c.m.a.c.service.MessageQueueService - 📋 Found 0 messages ready for processing
2025-07-02 11:08:58.210 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 📋 No messages ready for processing
2025-07-02 11:08:58.210 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - 🔄 Processing retry queue...
2025-07-02 11:08:58.226 [scheduling-1] INFO  c.m.apsp.core.service.RetryService - Found 1 errors ready for retry
2025-07-02 11:08:58.226 [scheduling-1] INFO  c.m.apsp.core.service.RetryService - 🔄 Processing retry for error log: 3 (attempt 1)
2025-07-02 11:08:58.226 [scheduling-1] WARN  c.m.apsp.core.service.RetryService - No message ID found for error log 3, cannot retry
2025-07-02 11:08:58.226 [scheduling-1] WARN  c.m.apsp.core.service.RetryService - ❌ Retry failed for error log: 3, scheduling next retry
2025-07-02 11:08:58.226 [scheduling-1] INFO  c.m.apsp.core.service.RetryService - ✅ Processed 1 retry attempts
2025-07-02 11:09:16.003 [http-nio-8081-exec-7] INFO  c.m.a.a.c.PeppolSbdInvoiceController - Processing Peppol SBD Invoice request: d8e4cb5f-4d2a-4455-8368-ab80832a6781 for country: DEFAULT and document type: INVOICE
2025-07-02 11:09:16.008 [http-nio-8081-exec-7] INFO  c.m.a.c.service.MessageQueueService - ✅ Added PEPPOL_SBD_INVOICE message to processing queue: MSG-93a14006-9944-4cf2-b2c9-1fa50f81c0b7
2025-07-02 11:09:16.010 [http-nio-8081-exec-7] INFO  c.m.a.a.c.PeppolSbdInvoiceController - ✅ Peppol SBD Invoice request added to processing queue: d8e4cb5f-4d2a-4455-8368-ab80832a6781
2025-07-02 11:09:16.010 [http-nio-8081-exec-7] INFO  c.m.a.a.c.PeppolSbdInvoiceController - Using UBL class: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType for DEFAULT/INVOICE
2025-07-02 11:09:16.054 [http-nio-8081-exec-7] INFO  c.m.apsp.core.service.InvoiceService - Generating invoice XML for country: DEFAULT and document type: INVOICE
2025-07-02 11:09:16.054 [http-nio-8081-exec-7] DEBUG c.m.a.c.service.XmlGeneratorService - Generating invoice XML using country-specific configuration: DEFAULT/INVOICE
2025-07-02 11:09:16.054 [http-nio-8081-exec-7] INFO  c.m.a.c.service.XmlGeneratorService - Using classes for DEFAULT/INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType and oasis.names.specification.ubl.schema.xsd.invoice_2.ObjectFactory
2025-07-02 11:09:16.057 [http-nio-8081-exec-7] INFO  c.m.a.c.service.XmlGeneratorService - Successfully generated XML for DEFAULT/INVOICE
2025-07-02 11:09:16.057 [http-nio-8081-exec-7] DEBUG c.m.a.core.service.ValidationService - Validating XML using 2 schematron files
2025-07-02 11:09:16.060 [http-nio-8081-exec-7] DEBUG c.m.a.core.service.ValidationService - Validating with schematron file 1 of 2: PINT-UBL-validation-preprocessed.sch
2025-07-02 11:09:16.060 [http-nio-8081-exec-7] DEBUG c.m.a.core.service.ValidationService - Validating XML using schematron file: PINT-UBL-validation-preprocessed.sch
2025-07-02 11:09:16.063 [http-nio-8081-exec-7] INFO  c.m.a.core.service.ValidationService - Country-specific schematron validation passed for: PINT-UBL-validation-preprocessed.sch
2025-07-02 11:09:16.063 [http-nio-8081-exec-7] DEBUG c.m.a.core.service.ValidationService - Validating with schematron file 2 of 2: PINT-jurisdiction-aligned-rules.sch
2025-07-02 11:09:16.063 [http-nio-8081-exec-7] DEBUG c.m.a.core.service.ValidationService - Validating XML using schematron file: PINT-jurisdiction-aligned-rules.sch
2025-07-02 11:09:16.065 [http-nio-8081-exec-7] INFO  c.m.a.core.service.ValidationService - Country-specific schematron validation passed for: PINT-jurisdiction-aligned-rules.sch
2025-07-02 11:09:16.065 [http-nio-8081-exec-7] INFO  c.m.a.core.service.ValidationService - Country-specific schematron validation passed for all 2 files
2025-07-02 11:09:16.065 [http-nio-8081-exec-7] INFO  c.m.apsp.core.service.InvoiceService - Invoice XML generated and validated successfully for DEFAULT/INVOICE
2025-07-02 11:09:28.226 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 🔄 Starting message processing cycle
2025-07-02 11:09:28.542 [scheduling-1] DEBUG c.m.a.c.service.MessageQueueService - 📋 Found 1 messages ready for processing
2025-07-02 11:09:28.543 [scheduling-1] INFO  c.m.a.c.s.MessageProcessingService - 📦 Found 1 messages ready for processing
2025-07-02 11:09:28.543 [scheduling-1] INFO  c.m.a.c.s.MessageProcessingService - 🚀 Started processing 1 messages
2025-07-02 11:09:28.544 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - 🔄 Processing retry queue...
2025-07-02 11:09:28.545 [pool-2-thread-3] INFO  c.m.a.c.s.MessageProcessingService - 🔄 Processing PEPPOL_SBD_INVOICE message: MSG-93a14006-9944-4cf2-b2c9-1fa50f81c0b7
2025-07-02 11:09:28.546 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - No errors ready for retry
2025-07-02 11:09:28.548 [pool-2-thread-3] DEBUG c.m.a.c.service.MessageQueueService - 📝 Updated message MSG-93a14006-9944-4cf2-b2c9-1fa50f81c0b7 status to: PROCESSING
2025-07-02 11:09:28.551 [pool-2-thread-3] INFO  c.m.a.c.s.FlowSpecificRetryService - 🔄 Processing retry for PEPPOL_SBD_INVOICE flow, message: MSG-93a14006-9944-4cf2-b2c9-1fa50f81c0b7
2025-07-02 11:09:28.551 [pool-2-thread-3] INFO  c.m.a.c.s.FlowSpecificRetryService - 🔄 Retrying Peppol SBD Invoice for message: MSG-93a14006-9944-4cf2-b2c9-1fa50f81c0b7
2025-07-02 11:09:28.551 [pool-2-thread-3] INFO  c.m.apsp.core.service.InvoiceService - Generating invoice XML for country: DEFAULT and document type: INVOICE
2025-07-02 11:09:28.551 [pool-2-thread-3] DEBUG c.m.a.c.service.XmlGeneratorService - Generating invoice XML using country-specific configuration: DEFAULT/INVOICE
2025-07-02 11:09:28.552 [pool-2-thread-3] INFO  c.m.a.c.service.XmlGeneratorService - Using classes for DEFAULT/INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType and oasis.names.specification.ubl.schema.xsd.invoice_2.ObjectFactory
2025-07-02 11:09:28.552 [pool-2-thread-3] ERROR c.m.a.c.service.XmlGeneratorService - Validation error generating invoice XML for DEFAULT/INVOICE: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:09:28.552 [pool-2-thread-3] ERROR c.m.apsp.core.service.InvoiceService - Validation error generating invoice for DEFAULT/INVOICE: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:09:28.552 [pool-2-thread-3] ERROR c.m.a.c.s.FlowSpecificRetryService - ❌ Exception during Peppol SBD Invoice retry for message MSG-93a14006-9944-4cf2-b2c9-1fa50f81c0b7: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:09:28.558 [pool-2-thread-3] INFO  c.m.a.c.service.MessageQueueService - 🔄 Message MSG-93a14006-9944-4cf2-b2c9-1fa50f81c0b7 marked for retry (attempt 1)
2025-07-02 11:09:58.558 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 🔄 Starting message processing cycle
2025-07-02 11:09:58.564 [scheduling-1] DEBUG c.m.a.c.service.MessageQueueService - 📋 Found 1 messages ready for processing
2025-07-02 11:09:58.566 [scheduling-1] INFO  c.m.a.c.s.MessageProcessingService - 📦 Found 1 messages ready for processing
2025-07-02 11:09:58.566 [scheduling-1] INFO  c.m.a.c.s.MessageProcessingService - 🚀 Started processing 1 messages
2025-07-02 11:09:58.566 [pool-2-thread-2] INFO  c.m.a.c.s.MessageProcessingService - 🔄 Processing PEPPOL_SBD_INVOICE message: MSG-93a14006-9944-4cf2-b2c9-1fa50f81c0b7
2025-07-02 11:09:58.567 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - 🔄 Processing retry queue...
2025-07-02 11:09:58.571 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - No errors ready for retry
2025-07-02 11:09:58.572 [pool-2-thread-2] DEBUG c.m.a.c.service.MessageQueueService - 📝 Updated message MSG-93a14006-9944-4cf2-b2c9-1fa50f81c0b7 status to: PROCESSING
2025-07-02 11:09:58.577 [pool-2-thread-2] INFO  c.m.a.c.s.FlowSpecificRetryService - 🔄 Processing retry for PEPPOL_SBD_INVOICE flow, message: MSG-93a14006-9944-4cf2-b2c9-1fa50f81c0b7
2025-07-02 11:09:58.577 [pool-2-thread-2] INFO  c.m.a.c.s.FlowSpecificRetryService - 🔄 Retrying Peppol SBD Invoice for message: MSG-93a14006-9944-4cf2-b2c9-1fa50f81c0b7
2025-07-02 11:09:58.577 [pool-2-thread-2] INFO  c.m.apsp.core.service.InvoiceService - Generating invoice XML for country: DEFAULT and document type: INVOICE
2025-07-02 11:09:58.577 [pool-2-thread-2] DEBUG c.m.a.c.service.XmlGeneratorService - Generating invoice XML using country-specific configuration: DEFAULT/INVOICE
2025-07-02 11:09:58.578 [pool-2-thread-2] INFO  c.m.a.c.service.XmlGeneratorService - Using classes for DEFAULT/INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType and oasis.names.specification.ubl.schema.xsd.invoice_2.ObjectFactory
2025-07-02 11:09:58.578 [pool-2-thread-2] ERROR c.m.a.c.service.XmlGeneratorService - Validation error generating invoice XML for DEFAULT/INVOICE: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:09:58.579 [pool-2-thread-2] ERROR c.m.apsp.core.service.InvoiceService - Validation error generating invoice for DEFAULT/INVOICE: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:09:58.579 [pool-2-thread-2] ERROR c.m.a.c.s.FlowSpecificRetryService - ❌ Exception during Peppol SBD Invoice retry for message MSG-93a14006-9944-4cf2-b2c9-1fa50f81c0b7: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:09:58.588 [pool-2-thread-2] INFO  c.m.a.c.service.MessageQueueService - 🔄 Message MSG-93a14006-9944-4cf2-b2c9-1fa50f81c0b7 marked for retry (attempt 2)
2025-07-02 11:10:28.567 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 🔄 Starting message processing cycle
2025-07-02 11:10:28.567 [scheduling-1] DEBUG c.m.a.c.service.MessageQueueService - 📋 Found 1 messages ready for processing
2025-07-02 11:10:28.567 [scheduling-1] INFO  c.m.a.c.s.MessageProcessingService - 📦 Found 1 messages ready for processing
2025-07-02 11:10:28.567 [scheduling-1] INFO  c.m.a.c.s.MessageProcessingService - 🚀 Started processing 1 messages
2025-07-02 11:10:28.567 [pool-2-thread-4] INFO  c.m.a.c.s.MessageProcessingService - 🔄 Processing PEPPOL_SBD_INVOICE message: MSG-93a14006-9944-4cf2-b2c9-1fa50f81c0b7
2025-07-02 11:10:28.567 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - 🔄 Processing retry queue...
2025-07-02 11:10:28.567 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - No errors ready for retry
2025-07-02 11:10:28.567 [pool-2-thread-4] DEBUG c.m.a.c.service.MessageQueueService - 📝 Updated message MSG-93a14006-9944-4cf2-b2c9-1fa50f81c0b7 status to: PROCESSING
2025-07-02 11:10:28.583 [pool-2-thread-4] INFO  c.m.a.c.s.FlowSpecificRetryService - 🔄 Processing retry for PEPPOL_SBD_INVOICE flow, message: MSG-93a14006-9944-4cf2-b2c9-1fa50f81c0b7
2025-07-02 11:10:28.583 [pool-2-thread-4] INFO  c.m.a.c.s.FlowSpecificRetryService - 🔄 Retrying Peppol SBD Invoice for message: MSG-93a14006-9944-4cf2-b2c9-1fa50f81c0b7
2025-07-02 11:10:28.583 [pool-2-thread-4] INFO  c.m.apsp.core.service.InvoiceService - Generating invoice XML for country: DEFAULT and document type: INVOICE
2025-07-02 11:10:28.583 [pool-2-thread-4] DEBUG c.m.a.c.service.XmlGeneratorService - Generating invoice XML using country-specific configuration: DEFAULT/INVOICE
2025-07-02 11:10:28.583 [pool-2-thread-4] INFO  c.m.a.c.service.XmlGeneratorService - Using classes for DEFAULT/INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType and oasis.names.specification.ubl.schema.xsd.invoice_2.ObjectFactory
2025-07-02 11:10:28.583 [pool-2-thread-4] ERROR c.m.a.c.service.XmlGeneratorService - Validation error generating invoice XML for DEFAULT/INVOICE: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:10:28.583 [pool-2-thread-4] ERROR c.m.apsp.core.service.InvoiceService - Validation error generating invoice for DEFAULT/INVOICE: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:10:28.583 [pool-2-thread-4] ERROR c.m.a.c.s.FlowSpecificRetryService - ❌ Exception during Peppol SBD Invoice retry for message MSG-93a14006-9944-4cf2-b2c9-1fa50f81c0b7: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
com.morohub.apsp.common.exception.ValidationException: Invoice object is not of type oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType
	at com.morohub.apsp.core.service.XmlGeneratorService.generateInvoiceWithConfig(XmlGeneratorService.java:47)
	at com.morohub.apsp.core.service.InvoiceService.generateInvoiceWithConfig(InvoiceService.java:86)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.retryPeppolSbdInvoice(FlowSpecificRetryService.java:181)
	at com.morohub.apsp.core.service.FlowSpecificRetryService.processRetryForMessage(FlowSpecificRetryService.java:55)
	at java.base/jdk.internal.reflect.DirectMethodHandleAccessor.invoke(DirectMethodHandleAccessor.java:103)
	at java.base/java.lang.reflect.Method.invoke(Method.java:580)
	at org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.transaction.interceptor.TransactionInterceptor$1.proceedWithInvocation(TransactionInterceptor.java:123)
	at org.springframework.transaction.interceptor.TransactionAspectSupport.invokeWithinTransaction(TransactionAspectSupport.java:385)
	at org.springframework.transaction.interceptor.TransactionInterceptor.invoke(TransactionInterceptor.java:119)
	at org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)
	at org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)
	at org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)
	at com.morohub.apsp.core.service.FlowSpecificRetryService$$SpringCGLIB$$0.processRetryForMessage(<generated>)
	at com.morohub.apsp.core.service.MessageProcessingService.processMessage(MessageProcessingService.java:139)
	at com.morohub.apsp.core.service.MessageProcessingService.lambda$processMessages$0(MessageProcessingService.java:103)
	at java.base/java.util.concurrent.CompletableFuture$AsyncRun.run(CompletableFuture.java:1804)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1144)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:642)
	at java.base/java.lang.Thread.run(Thread.java:1583)
2025-07-02 11:10:28.583 [pool-2-thread-4] WARN  c.m.a.c.service.MessageQueueService - ⚠️ Message MSG-93a14006-9944-4cf2-b2c9-1fa50f81c0b7 exceeded max retries, marking as ERROR
2025-07-02 11:10:46.330 [SpringApplicationShutdownHook] INFO  c.m.a.c.service.AS4ConversionService - Shutting down AS4 service...
2025-07-02 11:10:46.346 [SpringApplicationShutdownHook] INFO  c.m.a.c.service.AS4ConversionService - ✅ Phase4 global scope cleaned up
2025-07-02 11:27:13.306 [main] INFO  com.morohub.apsp.AS4Application - Starting AS4Application using Java 21.0.3 with PID 13244 (C:\Work\as4-main\target\classes started by kushagrat in C:\Work\as4-main)
2025-07-02 11:27:13.314 [main] DEBUG com.morohub.apsp.AS4Application - Running with Spring Boot v3.2.0, Spring v6.1.0
2025-07-02 11:27:13.314 [main] INFO  com.morohub.apsp.AS4Application - The following 1 profile is active: "dummy"
2025-07-02 11:27:15.926 [main] INFO  c.m.a.c.Phase4ServletConfiguration - 🔧 Registering AS4 servlet for incoming message handling
2025-07-02 11:27:15.926 [main] INFO  c.m.a.c.Phase4ServletConfiguration - ✅ AS4 servlet registered at /reverse-flow/as4/*
2025-07-02 11:27:20.764 [main] INFO  c.m.a.config.AS4CryptoConfiguration - === Creating Configured AS4 Crypto Factory ===
2025-07-02 11:27:20.764 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 11:27:20.764 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 11:27:20.764 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 11:27:20.764 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 11:27:20.795 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Keystore loaded from: classpath: keystore/cert.p12
2025-07-02 11:27:20.795 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 11:27:20.795 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 11:27:20.795 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 11:27:20.795 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 11:27:20.811 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Truststore loaded from: classpath: keystore/cert.p12
2025-07-02 11:27:20.811 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Truststore loaded from: classpath: keystore/cert.p12
2025-07-02 11:27:20.811 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - Validating crypto configuration...
2025-07-02 11:27:20.811 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 11:27:20.811 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 11:27:20.842 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ Crypto configuration validation successful
2025-07-02 11:27:20.842 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 🔑 Private key algorithm: RSA
2025-07-02 11:27:20.842 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 📜 Certificate subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE
2025-07-02 11:27:20.842 [main] DEBUG c.m.a.config.AS4CryptoConfiguration - 📅 Certificate valid from: Wed Jul 10 05:30:00 IST 2024 to: Wed Jul 01 05:29:59 IST 2026
2025-07-02 11:27:20.842 [main] INFO  c.m.a.config.AS4CryptoConfiguration - ✅ AS4 Crypto Factory configured successfully and set as default
2025-07-02 11:27:20.842 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 📁 Keystore: cert.p12
2025-07-02 11:27:20.842 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 🔑 Key Alias: cert
2025-07-02 11:27:20.842 [main] INFO  c.m.a.config.AS4CryptoConfiguration - 🛡️ Truststore: cert.p12
2025-07-02 11:27:20.920 [main] INFO  c.m.a.c.CountryConfigurationService - Loading unified country configuration from: country-config.json
2025-07-02 11:27:20.920 [main] INFO  c.m.a.c.CountryConfigurationService - Resource location: classpath: country-config.json
2025-07-02 11:27:20.920 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: country-config.json
2025-07-02 11:27:20.920 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: country-config.json
2025-07-02 11:27:20.952 [main] INFO  c.m.a.c.CountryConfigurationService - ✅ Unified country configuration loaded successfully
2025-07-02 11:27:20.952 [main] INFO  c.m.a.c.CountryConfigurationService - 📊 Available countries: DE, FR, IT, NL, ES, DEFAULT
2025-07-02 11:27:21.030 [main] INFO  c.m.a.c.CountryConfigurationService - 📋 Configuration Summary:
2025-07-02 11:27:21.030 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 DE (Germany): 3 document types
2025-07-02 11:27:21.030 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL.sch
2025-07-02 11:27:21.030 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL.sch
2025-07-02 11:27:21.030 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL.sch
2025-07-02 11:27:21.030 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 FR (France): 3 document types
2025-07-02 11:27:21.030 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-FR.sch
2025-07-02 11:27:21.030 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-FR.sch
2025-07-02 11:27:21.030 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-FR.sch
2025-07-02 11:27:21.030 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 IT (Italy): 3 document types
2025-07-02 11:27:21.030 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-IT.sch
2025-07-02 11:27:21.030 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-IT.sch
2025-07-02 11:27:21.045 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-IT.sch
2025-07-02 11:27:21.045 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 NL (Netherlands): 3 document types
2025-07-02 11:27:21.045 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-NL.sch
2025-07-02 11:27:21.045 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-NL.sch
2025-07-02 11:27:21.045 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-NL.sch
2025-07-02 11:27:21.045 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 ES (Spain): 3 document types
2025-07-02 11:27:21.045 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | PEPPOL-EN16931-UBL-ES.sch
2025-07-02 11:27:21.045 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL-ES.sch
2025-07-02 11:27:21.045 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL-ES.sch
2025-07-02 11:27:21.045 [main] INFO  c.m.a.c.CountryConfigurationService -   🌍 DEFAULT (Default/Generic): 3 document types
2025-07-02 11:27:21.045 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType | null
2025-07-02 11:27:21.045 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 CREDITNOTE: oasis.names.specification.ubl.schema.xsd.creditnote_2.CreditNoteType | PEPPOL-EN16931-UBL.sch
2025-07-02 11:27:21.045 [main] DEBUG c.m.a.c.CountryConfigurationService -     📄 APPLICATIONRESPONSE: oasis.names.specification.ubl.schema.xsd.applicationresponse_2.ApplicationResponseType | PEPPOL-EN16931-UBL.sch
2025-07-02 11:27:21.045 [main] INFO  c.m.a.c.service.AS4ConversionService - === Initializing Production-Ready AS4 Service ===
2025-07-02 11:27:21.045 [main] INFO  c.m.a.c.service.AS4ConversionService - AS4 Mode: dummy
2025-07-02 11:27:21.045 [main] INFO  c.m.a.c.service.AS4ConversionService - Security Enabled: true
2025-07-02 11:27:21.045 [main] INFO  c.m.a.c.service.AS4ConversionService - Validation Enabled: false
2025-07-02 11:27:21.045 [main] INFO  c.m.a.c.service.AS4ConversionService - Endpoint: http://localhost:8081/reverse-flow/receive-as4-message
2025-07-02 11:27:21.045 [main] INFO  c.m.a.c.service.AS4ConversionService - === Checking Keystore Availability ===
2025-07-02 11:27:21.045 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 11:27:21.045 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 11:27:21.045 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Keystore found at: classpath: keystore/cert.p12
2025-07-02 11:27:21.045 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 11:27:21.045 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 11:27:21.045 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Private key accessible for alias: cert
2025-07-02 11:27:21.045 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Keystore validation successful
2025-07-02 11:27:21.045 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 11:27:21.045 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 11:27:21.045 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Truststore found at: classpath: keystore/cert.p12
2025-07-02 11:27:21.092 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Phase4 global scope initialized
2025-07-02 11:27:21.092 [main] INFO  c.m.a.c.service.AS4ConversionService - === Configuring Phase4 Crypto Settings ===
2025-07-02 11:27:21.092 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 11:27:21.092 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 11:27:21.092 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ Crypto configuration is properly set up
2025-07-02 11:27:21.092 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 11:27:21.092 [main] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 11:27:21.092 [main] INFO  c.m.a.c.service.AS4ConversionService - 📁 Keystore info: Keystore: cert.p12, Alias: cert, Subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE, Valid: Wed Jul 10 05:30:00 IST 2024 to Wed Jul 01 05:29:59 IST 2026
2025-07-02 11:27:21.249 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ MetaAS4Manager initialized
2025-07-02 11:27:21.249 [main] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 service initialized successfully for dummy mode
2025-07-02 11:27:21.550 [main] WARN  o.s.b.a.o.j.JpaBaseConfiguration$JpaWebConfiguration - spring.jpa.open-in-view is enabled by default. Therefore, database queries may be performed during view rendering. Explicitly configure spring.jpa.open-in-view to disable this warning
2025-07-02 11:27:22.097 [main] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - 🔧 Initializing AS4 Servlet for message processing
2025-07-02 11:27:22.097 [main] INFO  c.m.a.c.Phase4ServletConfiguration$Phase4AS4Servlet - ✅ AS4 Servlet initialized successfully
2025-07-02 11:27:22.112 [main] INFO  com.morohub.apsp.AS4Application - Started AS4Application in 9.732 seconds (process running for 10.409)
2025-07-02 11:27:22.112 [main] INFO  c.m.a.c.ErrorMasterDataInitializer - 🔧 Initializing error master data...
2025-07-02 11:27:22.112 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 🔄 Starting MLS message processing cycle
2025-07-02 11:27:22.263 [scheduling-1] DEBUG c.m.a.c.service.MessageQueueService - 📋 Found 0 MLS messages ready for processing
2025-07-02 11:27:22.263 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_001
2025-07-02 11:27:22.264 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_002
2025-07-02 11:27:22.264 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 📋 No messages ready for processing
2025-07-02 11:27:22.264 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_003
2025-07-02 11:27:22.264 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - 🔄 Processing retry queue...
2025-07-02 11:27:22.264 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AUTH_004
2025-07-02 11:27:22.264 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_001
2025-07-02 11:27:22.264 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_002
2025-07-02 11:27:22.264 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_003
2025-07-02 11:27:22.279 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: VAL_004
2025-07-02 11:27:22.279 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_001
2025-07-02 11:27:22.279 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - No errors ready for retry
2025-07-02 11:27:22.279 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_002
2025-07-02 11:27:22.279 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_003
2025-07-02 11:27:22.279 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_004
2025-07-02 11:27:22.295 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: CONN_005
2025-07-02 11:27:22.295 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: SYS_001
2025-07-02 11:27:22.295 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: SYS_002
2025-07-02 11:27:22.295 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: SYS_003
2025-07-02 11:27:22.295 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_001
2025-07-02 11:27:22.295 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_002
2025-07-02 11:27:22.295 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_003
2025-07-02 11:27:22.295 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: AS4_004
2025-07-02 11:27:22.311 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: PEPPOL_001
2025-07-02 11:27:22.311 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: PEPPOL_002
2025-07-02 11:27:22.311 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: PEPPOL_003
2025-07-02 11:27:22.311 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: MLS_001
2025-07-02 11:27:22.311 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: MLS_002
2025-07-02 11:27:22.311 [main] DEBUG c.m.a.c.ErrorMasterDataInitializer - Error master data already exists: RATE_001
2025-07-02 11:27:22.311 [main] INFO  c.m.a.c.ErrorMasterDataInitializer - Initialized 26 error master data entries
2025-07-02 11:27:22.311 [main] INFO  c.m.a.c.ErrorMasterDataInitializer - ✅ Error master data initialization completed
2025-07-02 11:27:38.519 [http-nio-8081-exec-2] INFO  c.m.a.a.c.PeppolSbdInvoiceController - Processing Peppol SBD Invoice request: ea6ddafd-0fde-4228-9780-cc698221bbe4 for country: DEFAULT and document type: INVOICE
2025-07-02 11:27:38.628 [http-nio-8081-exec-2] INFO  c.m.a.c.service.MessageQueueService - ✅ Added PEPPOL_SBD_INVOICE message to processing queue: MSG-b425c239-cc9e-415d-ab4f-b412b33e1c89 (status: COMPLETED)
2025-07-02 11:27:38.637 [http-nio-8081-exec-2] INFO  c.m.a.a.c.PeppolSbdInvoiceController - ✅ Peppol SBD Invoice request added to processing queue: ea6ddafd-0fde-4228-9780-cc698221bbe4
2025-07-02 11:27:38.640 [http-nio-8081-exec-2] INFO  c.m.a.a.c.PeppolSbdInvoiceController - Using UBL class: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType for DEFAULT/INVOICE
2025-07-02 11:27:39.484 [http-nio-8081-exec-2] INFO  c.m.apsp.core.service.InvoiceService - Generating invoice XML for country: DEFAULT and document type: INVOICE
2025-07-02 11:27:39.485 [http-nio-8081-exec-2] DEBUG c.m.a.c.service.XmlGeneratorService - Generating invoice XML using country-specific configuration: DEFAULT/INVOICE
2025-07-02 11:27:39.486 [http-nio-8081-exec-2] INFO  c.m.a.c.service.XmlGeneratorService - Using classes for DEFAULT/INVOICE: oasis.names.specification.ubl.schema.xsd.invoice_2.InvoiceType and oasis.names.specification.ubl.schema.xsd.invoice_2.ObjectFactory
2025-07-02 11:27:39.487 [http-nio-8081-exec-2] DEBUG c.m.a.c.s.XmlJsonConversionService - 🔍 Creating UBL JAXB context for package: oasis.names.specification.ubl.schema.xsd.invoice_2
2025-07-02 11:27:39.488 [http-nio-8081-exec-2] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.invoice_2
2025-07-02 11:27:39.521 [http-nio-8081-exec-2] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.commonaggregatecomponents_2
2025-07-02 11:27:39.604 [http-nio-8081-exec-2] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.commonbasiccomponents_2
2025-07-02 11:27:39.605 [http-nio-8081-exec-2] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.commonextensioncomponents_2
2025-07-02 11:27:39.606 [http-nio-8081-exec-2] DEBUG c.m.a.c.s.XmlJsonConversionService - ✅ Found UBL package: oasis.names.specification.ubl.schema.xsd.creditnote_2
2025-07-02 11:27:39.607 [http-nio-8081-exec-2] DEBUG c.m.a.c.s.XmlJsonConversionService - 🔍 Using UBL context path: oasis.names.specification.ubl.schema.xsd.invoice_2:oasis.names.specification.ubl.schema.xsd.commonaggregatecomponents_2:oasis.names.specification.ubl.schema.xsd.commonbasiccomponents_2:oasis.names.specification.ubl.schema.xsd.commonextensioncomponents_2:oasis.names.specification.ubl.schema.xsd.creditnote_2
2025-07-02 11:27:40.238 [http-nio-8081-exec-2] INFO  c.m.a.c.service.XmlGeneratorService - Successfully generated XML for DEFAULT/INVOICE
2025-07-02 11:27:40.239 [http-nio-8081-exec-2] DEBUG c.m.a.core.service.ValidationService - Validating XML using 2 schematron files
2025-07-02 11:27:40.254 [http-nio-8081-exec-2] DEBUG c.m.a.core.service.ValidationService - Validating with schematron file 1 of 2: PINT-UBL-validation-preprocessed.sch
2025-07-02 11:27:40.254 [http-nio-8081-exec-2] DEBUG c.m.a.core.service.ValidationService - Validating XML using schematron file: PINT-UBL-validation-preprocessed.sch
2025-07-02 11:27:40.258 [http-nio-8081-exec-2] INFO  c.m.a.core.service.ValidationService - Country-specific schematron validation passed for: PINT-UBL-validation-preprocessed.sch
2025-07-02 11:27:40.258 [http-nio-8081-exec-2] DEBUG c.m.a.core.service.ValidationService - Validating with schematron file 2 of 2: PINT-jurisdiction-aligned-rules.sch
2025-07-02 11:27:40.258 [http-nio-8081-exec-2] DEBUG c.m.a.core.service.ValidationService - Validating XML using schematron file: PINT-jurisdiction-aligned-rules.sch
2025-07-02 11:27:40.263 [http-nio-8081-exec-2] INFO  c.m.a.core.service.ValidationService - Country-specific schematron validation passed for: PINT-jurisdiction-aligned-rules.sch
2025-07-02 11:27:40.263 [http-nio-8081-exec-2] INFO  c.m.a.core.service.ValidationService - Country-specific schematron validation passed for all 2 files
2025-07-02 11:27:40.263 [http-nio-8081-exec-2] INFO  c.m.apsp.core.service.InvoiceService - Invoice XML generated and validated successfully for DEFAULT/INVOICE
2025-07-02 11:27:52.282 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 🔄 Starting MLS message processing cycle
2025-07-02 11:27:52.282 [scheduling-1] DEBUG c.m.a.c.service.MessageQueueService - 📋 Found 0 MLS messages ready for processing
2025-07-02 11:27:52.282 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 📋 No messages ready for processing
2025-07-02 11:27:52.282 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - 🔄 Processing retry queue...
2025-07-02 11:27:52.282 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - No errors ready for retry
2025-07-02 11:28:17.850 [http-nio-8081-exec-3] INFO  c.m.a.a.c.XmlValidationController - 🔍 Starting validation and AS4 conversion for request: e75a5af2-e4aa-4fb8-abd0-8c46b9b14111
2025-07-02 11:28:17.851 [http-nio-8081-exec-3] INFO  c.m.a.c.s.SchematronValidationService - Starting multiple Schematron validation with 2 files
2025-07-02 11:28:17.851 [http-nio-8081-exec-3] INFO  c.m.a.c.s.SchematronValidationService - Validating with schematron file 1 of 2: PINT-UBL-validation-preprocessed.sch
2025-07-02 11:28:17.851 [http-nio-8081-exec-3] INFO  c.m.a.c.s.SchematronValidationService - Starting Schematron validation for file: PINT-UBL-validation-preprocessed.sch
2025-07-02 11:28:17.851 [http-nio-8081-exec-3] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: schematron/PINT-UBL-validation-preprocessed.sch
2025-07-02 11:28:17.852 [http-nio-8081-exec-3] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: schematron/PINT-UBL-validation-preprocessed.sch
2025-07-02 11:28:17.853 [http-nio-8081-exec-3] INFO  c.m.a.c.s.SchematronValidationService - ✅ Loading schematron from: classpath: schematron/PINT-UBL-validation-preprocessed.sch
2025-07-02 11:28:18.571 [http-nio-8081-exec-3] INFO  c.m.a.c.s.SchematronValidationService - Schematron validation completed. Found 1 errors.
2025-07-02 11:28:18.572 [http-nio-8081-exec-3] INFO  c.m.a.c.s.SchematronValidationService - Validating with schematron file 2 of 2: PINT-jurisdiction-aligned-rules.sch
2025-07-02 11:28:18.572 [http-nio-8081-exec-3] INFO  c.m.a.c.s.SchematronValidationService - Starting Schematron validation for file: PINT-jurisdiction-aligned-rules.sch
2025-07-02 11:28:18.572 [http-nio-8081-exec-3] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: schematron/PINT-jurisdiction-aligned-rules.sch
2025-07-02 11:28:18.572 [http-nio-8081-exec-3] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: schematron/PINT-jurisdiction-aligned-rules.sch
2025-07-02 11:28:18.573 [http-nio-8081-exec-3] INFO  c.m.a.c.s.SchematronValidationService - ✅ Loading schematron from: classpath: schematron/PINT-jurisdiction-aligned-rules.sch
2025-07-02 11:28:18.590 [http-nio-8081-exec-3] INFO  c.m.a.c.s.SchematronValidationService - Schematron validation completed. Found 1 errors.
2025-07-02 11:28:18.590 [http-nio-8081-exec-3] INFO  c.m.a.c.s.SchematronValidationService - Multiple Schematron validation completed. Total errors: 2
2025-07-02 11:28:18.590 [http-nio-8081-exec-3] WARN  c.m.a.a.c.XmlValidationController - ❌ Schematron validation failed with 2 errors for request: e75a5af2-e4aa-4fb8-abd0-8c46b9b14111
2025-07-02 11:28:18.628 [http-nio-8081-exec-3] INFO  c.m.a.c.s.ErrorManagementService - Error logged: VAL_002 for message: 043fae0b-9d82-4c88-aa47-bf38f62f0af8
2025-07-02 11:28:18.638 [http-nio-8081-exec-3] ERROR c.m.a.config.GlobalExceptionHandler - ValidationException handled: 2 validation errors for 043fae0b-9d82-4c88-aa47-bf38f62f0af8
2025-07-02 11:28:22.294 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 🔄 Starting MLS message processing cycle
2025-07-02 11:28:22.294 [scheduling-1] DEBUG c.m.a.c.service.MessageQueueService - 📋 Found 0 MLS messages ready for processing
2025-07-02 11:28:22.294 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 📋 No messages ready for processing
2025-07-02 11:28:22.294 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - 🔄 Processing retry queue...
2025-07-02 11:28:22.294 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - No errors ready for retry
2025-07-02 11:28:48.509 [http-nio-8081-exec-5] INFO  c.m.a.a.c.XmlValidationController - 🔍 Starting validation and AS4 conversion for request: bcc4106e-26e0-497a-9aba-0139a1f9ae27
2025-07-02 11:28:48.510 [http-nio-8081-exec-5] INFO  c.m.a.c.s.SchematronValidationService - Starting multiple Schematron validation with 2 files
2025-07-02 11:28:48.510 [http-nio-8081-exec-5] INFO  c.m.a.c.s.SchematronValidationService - Validating with schematron file 1 of 2: PINT-UBL-validation-preprocessed.sch
2025-07-02 11:28:48.510 [http-nio-8081-exec-5] INFO  c.m.a.c.s.SchematronValidationService - Starting Schematron validation for file: PINT-UBL-validation-preprocessed.sch
2025-07-02 11:28:48.510 [http-nio-8081-exec-5] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: schematron/PINT-UBL-validation-preprocessed.sch
2025-07-02 11:28:48.510 [http-nio-8081-exec-5] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: schematron/PINT-UBL-validation-preprocessed.sch
2025-07-02 11:28:48.511 [http-nio-8081-exec-5] INFO  c.m.a.c.s.SchematronValidationService - ✅ Loading schematron from: classpath: schematron/PINT-UBL-validation-preprocessed.sch
2025-07-02 11:28:48.527 [http-nio-8081-exec-5] INFO  c.m.a.c.s.SchematronValidationService - Schematron validation completed. Found 1 errors.
2025-07-02 11:28:48.528 [http-nio-8081-exec-5] INFO  c.m.a.c.s.SchematronValidationService - Validating with schematron file 2 of 2: PINT-jurisdiction-aligned-rules.sch
2025-07-02 11:28:48.528 [http-nio-8081-exec-5] INFO  c.m.a.c.s.SchematronValidationService - Starting Schematron validation for file: PINT-jurisdiction-aligned-rules.sch
2025-07-02 11:28:48.528 [http-nio-8081-exec-5] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: schematron/PINT-jurisdiction-aligned-rules.sch
2025-07-02 11:28:48.528 [http-nio-8081-exec-5] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: schematron/PINT-jurisdiction-aligned-rules.sch
2025-07-02 11:28:48.529 [http-nio-8081-exec-5] INFO  c.m.a.c.s.SchematronValidationService - ✅ Loading schematron from: classpath: schematron/PINT-jurisdiction-aligned-rules.sch
2025-07-02 11:28:48.539 [http-nio-8081-exec-5] INFO  c.m.a.c.s.SchematronValidationService - Schematron validation completed. Found 1 errors.
2025-07-02 11:28:48.540 [http-nio-8081-exec-5] INFO  c.m.a.c.s.SchematronValidationService - Multiple Schematron validation completed. Total errors: 2
2025-07-02 11:28:48.540 [http-nio-8081-exec-5] WARN  c.m.a.a.c.XmlValidationController - ❌ Schematron validation failed with 2 errors for request: bcc4106e-26e0-497a-9aba-0139a1f9ae27
2025-07-02 11:28:48.549 [http-nio-8081-exec-5] INFO  c.m.a.c.s.ErrorManagementService - Error logged: VAL_002 for message: 7afe70c8-7def-4a02-9cbb-27e705c9d5df
2025-07-02 11:28:48.556 [http-nio-8081-exec-5] ERROR c.m.a.config.GlobalExceptionHandler - ValidationException handled: 2 validation errors for 7afe70c8-7def-4a02-9cbb-27e705c9d5df
2025-07-02 11:28:52.305 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 🔄 Starting MLS message processing cycle
2025-07-02 11:28:52.305 [scheduling-1] DEBUG c.m.a.c.service.MessageQueueService - 📋 Found 0 MLS messages ready for processing
2025-07-02 11:28:52.305 [scheduling-1] DEBUG c.m.a.c.s.MessageProcessingService - 📋 No messages ready for processing
2025-07-02 11:28:52.305 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - 🔄 Processing retry queue...
2025-07-02 11:28:52.305 [scheduling-1] DEBUG c.m.apsp.core.service.RetryService - No errors ready for retry
2025-07-02 11:29:04.274 [http-nio-8081-exec-7] INFO  c.m.a.a.c.XmlValidationController - 🔍 Starting validation and AS4 conversion for request: f1a45200-06eb-49f6-8c7d-cba39d370a58
2025-07-02 11:29:04.275 [http-nio-8081-exec-7] INFO  c.m.a.c.s.SchematronValidationService - Starting multiple Schematron validation with 2 files
2025-07-02 11:29:04.275 [http-nio-8081-exec-7] INFO  c.m.a.c.s.SchematronValidationService - Validating with schematron file 1 of 2: PINT-UBL-validation-preprocessed.sch
2025-07-02 11:29:04.275 [http-nio-8081-exec-7] INFO  c.m.a.c.s.SchematronValidationService - Starting Schematron validation for file: PINT-UBL-validation-preprocessed.sch
2025-07-02 11:29:04.275 [http-nio-8081-exec-7] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: schematron/PINT-UBL-validation-preprocessed.sch
2025-07-02 11:29:04.275 [http-nio-8081-exec-7] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: schematron/PINT-UBL-validation-preprocessed.sch
2025-07-02 11:29:04.276 [http-nio-8081-exec-7] INFO  c.m.a.c.s.SchematronValidationService - ✅ Loading schematron from: classpath: schematron/PINT-UBL-validation-preprocessed.sch
2025-07-02 11:29:04.299 [http-nio-8081-exec-7] INFO  c.m.a.c.s.SchematronValidationService - Schematron validation completed. Found 0 errors.
2025-07-02 11:29:04.300 [http-nio-8081-exec-7] INFO  c.m.a.c.s.SchematronValidationService - Validating with schematron file 2 of 2: PINT-jurisdiction-aligned-rules.sch
2025-07-02 11:29:04.300 [http-nio-8081-exec-7] INFO  c.m.a.c.s.SchematronValidationService - Starting Schematron validation for file: PINT-jurisdiction-aligned-rules.sch
2025-07-02 11:29:04.300 [http-nio-8081-exec-7] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: schematron/PINT-jurisdiction-aligned-rules.sch
2025-07-02 11:29:04.300 [http-nio-8081-exec-7] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: schematron/PINT-jurisdiction-aligned-rules.sch
2025-07-02 11:29:04.300 [http-nio-8081-exec-7] INFO  c.m.a.c.s.SchematronValidationService - ✅ Loading schematron from: classpath: schematron/PINT-jurisdiction-aligned-rules.sch
2025-07-02 11:29:04.319 [http-nio-8081-exec-7] INFO  c.m.a.c.s.SchematronValidationService - Schematron validation completed. Found 0 errors.
2025-07-02 11:29:04.319 [http-nio-8081-exec-7] INFO  c.m.a.c.s.SchematronValidationService - Multiple Schematron validation completed. Total errors: 0
2025-07-02 11:29:04.323 [http-nio-8081-exec-7] INFO  c.m.a.c.service.AS4ConversionService - === Starting AS4 Conversion ===
2025-07-02 11:29:04.323 [http-nio-8081-exec-7] INFO  c.m.a.c.service.AS4ConversionService - Transaction ID: 5d5cac8c-b619-4132-b359-b940a57666a3
2025-07-02 11:29:04.324 [http-nio-8081-exec-7] INFO  c.m.a.c.service.AS4ConversionService - Mode: dummy, Security: true
2025-07-02 11:29:04.332 [http-nio-8081-exec-7] INFO  c.m.a.c.service.MessageQueueService - ✅ Added FORWARD_FLOW message to processing queue: MSG-d2f0a49e-56f4-4435-8f9f-d0c806cb1bd1 (status: COMPLETED)
2025-07-02 11:29:04.334 [http-nio-8081-exec-7] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 conversion request added to processing queue: 5d5cac8c-b619-4132-b359-b940a57666a3
2025-07-02 11:29:04.335 [http-nio-8081-exec-7] INFO  c.m.a.c.service.AS4ConversionService - Extracting AS4 metadata from UBL XML
2025-07-02 11:29:04.393 [http-nio-8081-exec-7] WARN  c.m.a.c.service.AS4ConversionService - ⚠️ Unknown document type: Invoice, defaulting to INVOICE
2025-07-02 11:29:04.400 [http-nio-8081-exec-7] INFO  c.m.a.c.service.AS4ConversionService - 🌍 Found country code in address: AE
2025-07-02 11:29:04.400 [http-nio-8081-exec-7] INFO  c.m.a.c.service.AS4ConversionService - 📄 Document type: INVOICE, Country: AE
2025-07-02 11:29:04.401 [http-nio-8081-exec-7] DEBUG c.m.a.c.service.AS4ConversionService - Found participant ID in AccountingSupplierParty: **********
2025-07-02 11:29:04.402 [http-nio-8081-exec-7] DEBUG c.m.a.c.service.AS4ConversionService - Found scheme ID in AccountingSupplierParty: 0235
2025-07-02 11:29:04.440 [http-nio-8081-exec-7] INFO  c.m.a.c.service.AS4ConversionService - 👤 Extracted sender participant ID: ********** with scheme: 0235
2025-07-02 11:29:04.440 [http-nio-8081-exec-7] DEBUG c.m.a.c.service.AS4ConversionService - Found participant ID in AccountingCustomerParty: **********
2025-07-02 11:29:04.440 [http-nio-8081-exec-7] DEBUG c.m.a.c.service.AS4ConversionService - Found scheme ID in AccountingCustomerParty: 0235
2025-07-02 11:29:04.441 [http-nio-8081-exec-7] INFO  c.m.a.c.service.AS4ConversionService - 👥 Extracted receiver participant ID: ********** with scheme: 0235
2025-07-02 11:29:04.444 [http-nio-8081-exec-7] INFO  c.m.a.c.service.AS4ConversionService - 📋 Using document type ID: urn:oasis:names:specification:ubl:schema:xsd:Invoice-2::Invoice##urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1
2025-07-02 11:29:04.446 [http-nio-8081-exec-7] INFO  c.m.a.c.service.AS4ConversionService - 📋 Extracted ProfileExecutionID: ********
2025-07-02 11:29:04.447 [http-nio-8081-exec-7] INFO  c.m.a.c.service.AS4ConversionService - 📅 Extracted IssueDate: 2025-02-06
2025-07-02 11:29:04.450 [http-nio-8081-exec-7] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 metadata extracted successfully
2025-07-02 11:29:04.450 [http-nio-8081-exec-7] INFO  c.m.a.c.service.AS4ConversionService - 📄 Invoice ID: AE-01TEST
2025-07-02 11:29:04.451 [http-nio-8081-exec-7] INFO  c.m.a.c.service.AS4ConversionService - 📊 Message ID: MSG-1751435944447
2025-07-02 11:29:04.451 [http-nio-8081-exec-7] INFO  c.m.a.c.service.AS4ConversionService - 👤 Sender: [PeppolParticipantIdentifier@0x5a05d094: scheme=iso6523-actorid-upis; value=**********]
2025-07-02 11:29:04.453 [http-nio-8081-exec-7] INFO  c.m.a.c.service.AS4ConversionService - 👥 Receiver: [PeppolParticipantIdentifier@0x5d819e11: scheme=iso6523-actorid-upis; value=**********]
2025-07-02 11:29:04.453 [http-nio-8081-exec-7] INFO  c.m.a.c.service.AS4ConversionService - 📋 Document Type: [PeppolDocumentTypeIdentifier@0x7bccc2e6: scheme=busdox-docid-qns; value=urn:oasis:names:specification:ubl:schema:xsd:Invoice-2::Invoice##urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1]
2025-07-02 11:29:04.453 [http-nio-8081-exec-7] INFO  c.m.a.c.service.AS4ConversionService - ⚙️ Process: [PeppolProcessIdentifier@0x6bfa31a5: scheme=cenbii-procid-ubl; value=urn:peppol:bis:billing]
2025-07-02 11:29:04.453 [http-nio-8081-exec-7] INFO  c.m.a.c.service.AS4ConversionService - 🌍 Country Code: AE
2025-07-02 11:29:04.453 [http-nio-8081-exec-7] DEBUG c.m.a.c.service.AS4ConversionService - Validating AS4 message metadata...
2025-07-02 11:29:04.453 [http-nio-8081-exec-7] INFO  c.m.a.c.service.AS4ConversionService - ✅ AS4 message validation passed
2025-07-02 11:29:04.454 [http-nio-8081-exec-7] INFO  c.m.a.c.service.AS4ConversionService - 🏭 PRODUCTION/TEST MODE: Using Phase4PeppolSender
2025-07-02 11:29:04.454 [http-nio-8081-exec-7] INFO  c.m.a.c.service.AS4ConversionService - 🏭 Sending AS4 message in dummy mode
2025-07-02 11:29:04.454 [http-nio-8081-exec-7] INFO  c.m.a.c.service.AS4ConversionService - 🌍 Setting country code for SBDH: AE
2025-07-02 11:29:04.454 [http-nio-8081-exec-7] INFO  c.m.a.c.service.AS4ConversionService - 👤 From Party ID: **********
2025-07-02 11:29:04.454 [http-nio-8081-exec-7] INFO  c.m.a.c.service.AS4ConversionService - 👥 To Party ID: **********
2025-07-02 11:29:04.540 [http-nio-8081-exec-7] INFO  c.m.a.c.service.AS4ConversionService - ✅ Custom SBDH created with:
2025-07-02 11:29:04.540 [http-nio-8081-exec-7] INFO  c.m.a.c.service.AS4ConversionService -    📋 Instance ID: aff29bcb-7bb3-4098-8472-4da367a19890
2025-07-02 11:29:04.540 [http-nio-8081-exec-7] INFO  c.m.a.c.service.AS4ConversionService -    📅 Creation Date: 2025-07-02T11:29:04
2025-07-02 11:29:04.540 [http-nio-8081-exec-7] INFO  c.m.a.c.service.AS4ConversionService -    👤 Sender: 0235:**********
2025-07-02 11:29:04.540 [http-nio-8081-exec-7] INFO  c.m.a.c.service.AS4ConversionService -    👥 Receiver: 0235:**********
2025-07-02 11:29:04.540 [http-nio-8081-exec-7] INFO  c.m.a.c.service.AS4ConversionService -    🌍 Country: AE
2025-07-02 11:29:04.540 [http-nio-8081-exec-7] INFO  c.m.a.c.service.AS4ConversionService - Forward flow xml : <?xml version="1.0" encoding="UTF-8"?>
<StandardBusinessDocument xmlns="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader"
                          xmlns:sh="http://www.unece.org/cefact/namespaces/StandardBusinessDocumentHeader">
   <sh:StandardBusinessDocumentHeader>
      <sh:HeaderVersion>1.0</sh:HeaderVersion>
      <sh:Sender>
         <sh:Identifier Authority="iso6523-actorid-upis">0235:**********</sh:Identifier>
      </sh:Sender>
      <sh:Receiver>
         <sh:Identifier Authority="iso6523-actorid-upis">0235:**********</sh:Identifier>
      </sh:Receiver>
      <sh:DocumentIdentification>
         <sh:Standard>urn:oasis:names:specification:ubl:schema:xsd:Invoice-2</sh:Standard>
         <sh:TypeVersion>2.1</sh:TypeVersion>
         <sh:InstanceIdentifier>aff29bcb-7bb3-4098-8472-4da367a19890</sh:InstanceIdentifier>
         <sh:Type>Invoice</sh:Type>
         <sh:CreationDateAndTime>2025-07-02T11:29:04</sh:CreationDateAndTime>
      </sh:DocumentIdentification>
      <sh:BusinessScope>
         <sh:Scope>
            <sh:Type>DOCUMENTID</sh:Type>
            <sh:InstanceIdentifier>urn:oasis:names:specification:ubl:schema:xsd:Invoice-2::Invoice##urn:cen.eu:en16931:2017#compliant#urn:fdc:peppol.eu:2017:poacc:billing:3.0::2.1</sh:InstanceIdentifier>
            <sh:Identifier>kush-docid-qns</sh:Identifier>
         </sh:Scope>
         <sh:Scope>
            <sh:Type>PROCESSID</sh:Type>
            <sh:InstanceIdentifier>urn:peppol:bis:billing</sh:InstanceIdentifier>
            <sh:Identifier>cenbii-procid-ubl</sh:Identifier>
         </sh:Scope>
         <sh:Scope>
            <sh:Type>COUNTRY_C1</sh:Type>
            <sh:InstanceIdentifier>AE</sh:InstanceIdentifier>
         </sh:Scope>
      </sh:BusinessScope>
   </sh:StandardBusinessDocumentHeader>
   <Invoice xmlns="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
            xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2"
            xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
            xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
            xsi:schemaLocation="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2 http://docs.oasis-open.org/ubl/os-UBL-2.1/xsd/maindoc/UBL-Invoice-2.1.xsd">
      <cbc:CustomizationID>urn:peppol:pint:billing-1@ae-1</cbc:CustomizationID>
      <!--  IBT-024 -->
      <cbc:ProfileID>urn:peppol:bis:billing</cbc:ProfileID>
      <!--  IBT-023 -->
      <cbc:ProfileExecutionID>********</cbc:ProfileExecutionID>
      <cbc:ID>AE-01TEST</cbc:ID>
      <cbc:IssueDate>2025-02-06</cbc:IssueDate>
      <cbc:IssueTime>07:54:00</cbc:IssueTime>
      <cbc:DueDate>2025-02-13</cbc:DueDate>
      <cbc:InvoiceTypeCode>380</cbc:InvoiceTypeCode>
      <cbc:Note>Tax invoice</cbc:Note>
      <cbc:TaxPointDate>2025-01-30</cbc:TaxPointDate>
      <cbc:DocumentCurrencyCode>AED</cbc:DocumentCurrencyCode>
      <cbc:AccountingCost>Regular sales</cbc:AccountingCost>
      <cbc:BuyerReference>PO-AE-220</cbc:BuyerReference>
      <cac:InvoicePeriod>
         <cbc:StartDate>2025-01-31</cbc:StartDate>
         <cbc:EndDate>2025-02-06</cbc:EndDate>
      </cac:InvoicePeriod>
      <cac:OrderReference>
         <cbc:ID>PO-AE-220</cbc:ID>
         <cbc:SalesOrderID>Salesorder-2122</cbc:SalesOrderID>
      </cac:OrderReference>
      <cac:BillingReference>
         <cac:InvoiceDocumentReference>
            <cbc:ID>INV-234-2025</cbc:ID>
            <cbc:IssueDate>2025-02-06</cbc:IssueDate>
         </cac:InvoiceDocumentReference>
      </cac:BillingReference>
      <cac:DespatchDocumentReference>
         <cbc:ID>Memo-1000</cbc:ID>
      </cac:DespatchDocumentReference>
      <cac:OriginatorDocumentReference>
         <cbc:ID>PO-AE-220</cbc:ID>
      </cac:OriginatorDocumentReference>
      <cac:AdditionalDocumentReference>
         <cbc:ID>PO-AE-220</cbc:ID>
         <cac:Attachment>
            <cac:ExternalReference>
               <cbc:URI>https://www.site.ae/PO-AE-220.pdf</cbc:URI>
            </cac:ExternalReference>
         </cac:Attachment>
      </cac:AdditionalDocumentReference>
      <cac:AdditionalDocumentReference>
         <cbc:ID>PO-AE-220</cbc:ID>
         <cac:Attachment>
            <cbc:EmbeddedDocumentBinaryObject filename="PO-AE-220.pdf" mimeCode="application/pdf">QmFzZTY0IGNvbnRlbnQgZXhhbXBfZQ==</cbc:EmbeddedDocumentBinaryObject>
         </cac:Attachment>
      </cac:AdditionalDocumentReference>
      <cac:ProjectReference>
         <cbc:ID>Regular work</cbc:ID>
      </cac:ProjectReference>
      <cac:AccountingSupplierParty>
         <cac:Party>
            <cbc:EndpointID schemeID="0235">**********</cbc:EndpointID>
            <cac:PartyName>
               <cbc:Name>Party Trade Name</cbc:Name>
            </cac:PartyName>
            <cac:PostalAddress>
               <cbc:StreetName>Street Name</cbc:StreetName>
               <cbc:CityName>Sharjah</cbc:CityName>
               <cbc:CountrySubentity>SHJ</cbc:CountrySubentity>
               <cac:Country>
                  <cbc:IdentificationCode>AE</cbc:IdentificationCode>
               </cac:Country>
            </cac:PostalAddress>
            <cac:PartyTaxScheme>
               <cbc:CompanyID>***************</cbc:CompanyID>
               <!--  IBT-031 -->
               <cac:TaxScheme>
                  <cbc:ID>VAT</cbc:ID>
               </cac:TaxScheme>
            </cac:PartyTaxScheme>
            <cac:PartyLegalEntity>
               <cbc:RegistrationName>Supplier Legal Name</cbc:RegistrationName>
               <cbc:CompanyID schemeAgencyID="TL" schemeAgencyName="Trade License issuing Authority">***************</cbc:CompanyID>
               <!--  IBT-030, BTAE-15, BTAE-12 -->
               <cbc:CompanyLegalForm>Merchant</cbc:CompanyLegalForm>
            </cac:PartyLegalEntity>
            <cac:Contact>
               <cbc:Name>Contact Name</cbc:Name>
               <cbc:Telephone>Telephone number</cbc:Telephone>
               <cbc:ElectronicMail>Email address</cbc:ElectronicMail>
            </cac:Contact>
         </cac:Party>
      </cac:AccountingSupplierParty>
      <cac:AccountingCustomerParty>
         <cac:Party>
            <cbc:EndpointID schemeID="0235">**********</cbc:EndpointID>
            <cac:PartyName>
               <cbc:Name>Buyer Trade Name</cbc:Name>
            </cac:PartyName>
            <cac:PostalAddress>
               <cbc:StreetName>Street Name</cbc:StreetName>
               <cbc:CityName>Abu Dhabi</cbc:CityName>
               <cbc:CountrySubentity>AUH</cbc:CountrySubentity>
               <cac:Country>
                  <cbc:IdentificationCode>AE</cbc:IdentificationCode>
               </cac:Country>
            </cac:PostalAddress>
            <cac:PartyTaxScheme>
               <cbc:CompanyID>**********23003</cbc:CompanyID>
               <!--  IBT-048 -->
               <cac:TaxScheme>
                  <cbc:ID>VAT</cbc:ID>
               </cac:TaxScheme>
            </cac:PartyTaxScheme>
            <cac:PartyLegalEntity>
               <cbc:RegistrationName>Noor Electronics</cbc:RegistrationName>
               <cbc:CompanyID schemeAgencyID="TL" schemeAgencyName="Trade License issuing Authority">***************</cbc:CompanyID>
               <!--  IBT-047, BTAE-16, BTAE-11 -->
            </cac:PartyLegalEntity>
            <cac:Contact>
               <cbc:Name>Contact Name</cbc:Name>
               <cbc:Telephone>Telephone number</cbc:Telephone>
               <cbc:ElectronicMail>Email address</cbc:ElectronicMail>
            </cac:Contact>
         </cac:Party>
      </cac:AccountingCustomerParty>
      <cac:PayeeParty>
         <cac:PartyName>
            <cbc:Name>Payee Name</cbc:Name>
         </cac:PartyName>
         <cac:PartyLegalEntity>
            <cbc:CompanyID>**********</cbc:CompanyID>
         </cac:PartyLegalEntity>
      </cac:PayeeParty>
      <cac:PaymentMeans>
         <cbc:PaymentMeansCode name="Debit Card">55</cbc:PaymentMeansCode>
         <cac:CardAccount>
            <cbc:PrimaryAccountNumberID>XXXXXXXXXXXX1234</cbc:PrimaryAccountNumberID>
            <cbc:NetworkID>VISA</cbc:NetworkID>
            <cbc:HolderName>Card Holder Name</cbc:HolderName>
         </cac:CardAccount>
      </cac:PaymentMeans>
      <cac:PaymentTerms>
         <cbc:Note>Within a week</cbc:Note>
      </cac:PaymentTerms>
      <cac:AllowanceCharge>
         <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
         <cbc:AllowanceChargeReasonCode>100</cbc:AllowanceChargeReasonCode>
         <cbc:AllowanceChargeReason>Special Rebate</cbc:AllowanceChargeReason>
         <cbc:MultiplierFactorNumeric>2.5</cbc:MultiplierFactorNumeric>
         <cbc:Amount currencyID="AED">262.15</cbc:Amount>
         <cbc:BaseAmount currencyID="AED">10486</cbc:BaseAmount>
         <cac:TaxCategory>
            <cbc:ID>S</cbc:ID>
            <cbc:Percent>5</cbc:Percent>
            <cac:TaxScheme>
               <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
         </cac:TaxCategory>
      </cac:AllowanceCharge>
      <cac:AllowanceCharge>
         <cbc:ChargeIndicator>true</cbc:ChargeIndicator>
         <cbc:AllowanceChargeReasonCode>AAT</cbc:AllowanceChargeReasonCode>
         <cbc:AllowanceChargeReason>Rush Delivery</cbc:AllowanceChargeReason>
         <cbc:MultiplierFactorNumeric>4</cbc:MultiplierFactorNumeric>
         <cbc:Amount currencyID="AED">419.44</cbc:Amount>
         <cbc:BaseAmount currencyID="AED">10486</cbc:BaseAmount>
         <cac:TaxCategory>
            <cbc:ID>S</cbc:ID>
            <cbc:Percent>5</cbc:Percent>
            <cac:TaxScheme>
               <cbc:ID>VAT</cbc:ID>
            </cac:TaxScheme>
         </cac:TaxCategory>
      </cac:AllowanceCharge>
      <cac:TaxTotal>
         <cbc:TaxAmount currencyID="AED">532.16</cbc:TaxAmount>
         <cbc:TaxIncludedIndicator>false</cbc:TaxIncludedIndicator>
         <cac:TaxSubtotal>
            <cbc:TaxableAmount currencyID="AED">10643.29</cbc:TaxableAmount>
            <cbc:TaxAmount currencyID="AED">532.1645</cbc:TaxAmount>
            <cac:TaxCategory>
               <cbc:ID>S</cbc:ID>
               <cbc:Percent>5</cbc:Percent>
               <cac:TaxScheme>
                  <cbc:ID>VAT</cbc:ID>
               </cac:TaxScheme>
            </cac:TaxCategory>
         </cac:TaxSubtotal>
      </cac:TaxTotal>
      <cac:LegalMonetaryTotal>
         <cbc:LineExtensionAmount currencyID="AED">10486</cbc:LineExtensionAmount>
         <cbc:TaxExclusiveAmount currencyID="AED">10643.29</cbc:TaxExclusiveAmount>
         <cbc:TaxInclusiveAmount currencyID="AED">11175.45</cbc:TaxInclusiveAmount>
         <cbc:AllowanceTotalAmount currencyID="AED">262.15</cbc:AllowanceTotalAmount>
         <cbc:ChargeTotalAmount currencyID="AED">419.44</cbc:ChargeTotalAmount>
         <cbc:PayableRoundingAmount currencyID="AED">0.05</cbc:PayableRoundingAmount>
         <cbc:PayableAmount currencyID="AED">11175.5</cbc:PayableAmount>
      </cac:LegalMonetaryTotal>
      <cac:InvoiceLine>
         <cbc:ID>1</cbc:ID>
         <cbc:Note>All items</cbc:Note>
         <cbc:InvoicedQuantity unitCode="H87">2000</cbc:InvoicedQuantity>
         <cbc:LineExtensionAmount currencyID="AED">10486</cbc:LineExtensionAmount>
         <cac:InvoicePeriod>
            <cbc:StartDate>2025-01-31</cbc:StartDate>
            <cbc:EndDate>2025-01-31</cbc:EndDate>
         </cac:InvoicePeriod>
         <cac:OrderLineReference>
            <cbc:LineID>1</cbc:LineID>
            <cac:OrderReference>
               <cbc:ID>PO-AE-220</cbc:ID>
            </cac:OrderReference>
         </cac:OrderLineReference>
         <cac:AllowanceCharge>
            <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
            <cbc:AllowanceChargeReasonCode>95</cbc:AllowanceChargeReasonCode>
            <cbc:AllowanceChargeReason>Discount</cbc:AllowanceChargeReason>
            <cbc:MultiplierFactorNumeric>3</cbc:MultiplierFactorNumeric>
            <cbc:Amount currencyID="AED">294</cbc:Amount>
            <cbc:BaseAmount currencyID="AED">9800</cbc:BaseAmount>
         </cac:AllowanceCharge>
         <cac:AllowanceCharge>
            <cbc:ChargeIndicator>true</cbc:ChargeIndicator>
            <cbc:AllowanceChargeReasonCode>AAC</cbc:AllowanceChargeReasonCode>
            <cbc:AllowanceChargeReason>Technical Modification</cbc:AllowanceChargeReason>
            <cbc:MultiplierFactorNumeric>10</cbc:MultiplierFactorNumeric>
            <cbc:Amount currencyID="AED">980</cbc:Amount>
            <cbc:BaseAmount currencyID="AED">9800</cbc:BaseAmount>
         </cac:AllowanceCharge>
         <cac:TaxTotal>
            <cbc:TaxAmount currencyID="AED">524.3</cbc:TaxAmount>
         </cac:TaxTotal>
         <cac:Item>
            <cbc:Description>Item Description</cbc:Description>
            <cbc:Name>Item Name</cbc:Name>
            <cac:BuyersItemIdentification>
               <cbc:ID>Purchase goods</cbc:ID>
            </cac:BuyersItemIdentification>
            <cac:SellersItemIdentification>
               <cbc:ID>Sales Goods</cbc:ID>
            </cac:SellersItemIdentification>
            <cac:AdditionalItemIdentification>
               <cbc:ID schemeID="SAC">3242423</cbc:ID>
            </cac:AdditionalItemIdentification>
            <cac:OriginCountry>
               <cbc:IdentificationCode>AE</cbc:IdentificationCode>
            </cac:OriginCountry>
            <cac:CommodityClassification>
               <cbc:CommodityCode>S</cbc:CommodityCode>
            </cac:CommodityClassification>
            <cac:ClassifiedTaxCategory>
               <cbc:ID>S</cbc:ID>
               <cbc:Percent>5</cbc:Percent>
               <cac:TaxScheme>
                  <cbc:ID>VAT</cbc:ID>
               </cac:TaxScheme>
            </cac:ClassifiedTaxCategory>
            <cac:AdditionalItemProperty>
               <cbc:Name>Item details</cbc:Name>
               <cbc:Value>Item Value</cbc:Value>
            </cac:AdditionalItemProperty>
         </cac:Item>
         <cac:Price>
            <cbc:PriceAmount currencyID="AED">4.9</cbc:PriceAmount>
            <cbc:BaseQuantity unitCode="H87">1</cbc:BaseQuantity>
            <cac:AllowanceCharge>
               <cbc:ChargeIndicator>false</cbc:ChargeIndicator>
               <cbc:Amount currencyID="AED">0.1</cbc:Amount>
               <cbc:BaseAmount currencyID="AED">5</cbc:BaseAmount>
            </cac:AllowanceCharge>
         </cac:Price>
      </cac:InvoiceLine>
   </Invoice>
</StandardBusinessDocument>

2025-07-02 11:29:05.061 [http-nio-8081-exec-7] DEBUG c.m.a.config.AS4CryptoConfiguration - Configuring Phase4PeppolSender with crypto settings for mode: dummy
2025-07-02 11:29:05.062 [http-nio-8081-exec-7] DEBUG c.m.a.config.AS4CryptoConfiguration - ✅ Using configured crypto factory for signing
2025-07-02 11:29:05.062 [http-nio-8081-exec-7] DEBUG c.m.a.config.AS4CryptoConfiguration - 🔑 Crypto factory will use key alias: cert
2025-07-02 11:29:05.062 [http-nio-8081-exec-7] DEBUG c.m.a.config.AS4CryptoConfiguration - 🧪 DUMMY MODE: Configuring sender for testing with relaxed validation
2025-07-02 11:29:05.063 [http-nio-8081-exec-7] DEBUG c.m.a.config.AS4CryptoConfiguration - ✅ Phase4PeppolSender configured with crypto settings
2025-07-02 11:29:05.063 [http-nio-8081-exec-7] INFO  c.m.a.c.service.AS4ConversionService - 🔐 Crypto factory configured: ✅ Available
2025-07-02 11:29:05.063 [http-nio-8081-exec-7] DEBUG c.m.a.c.ConfigurableResourceLoader - Loading resource: keystore/cert.p12
2025-07-02 11:29:05.063 [http-nio-8081-exec-7] DEBUG c.m.a.c.ConfigurableResourceLoader - 📦 Loading resource from classpath: keystore/cert.p12
2025-07-02 11:29:05.068 [http-nio-8081-exec-7] INFO  c.m.a.c.service.AS4ConversionService - 🔐 Keystore info: Keystore: cert.p12, Alias: cert, Subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE, Valid: Wed Jul 10 05:30:00 IST 2024 to Wed Jul 01 05:29:59 IST 2026
2025-07-02 11:29:05.068 [http-nio-8081-exec-7] INFO  c.m.a.c.service.AS4ConversionService - 🏭 PRODUCTION MODE: Using real Peppol network
2025-07-02 11:29:05.069 [http-nio-8081-exec-7] WARN  c.m.a.c.service.AS4ConversionService - ⚠️ DUMMY MODE with direct endpoint - FOR TESTING ONLY
2025-07-02 11:29:05.069 [http-nio-8081-exec-7] INFO  c.m.a.c.service.AS4ConversionService - 🌐 Using direct endpoint: http://localhost:8081/reverse-flow/receive-as4-message
2025-07-02 11:29:05.069 [http-nio-8081-exec-7] WARN  c.m.a.c.service.AS4ConversionService - ⚠️ DUMMY MODE: Using direct endpoint with certificate validation BYPASSED
2025-07-02 11:29:05.070 [http-nio-8081-exec-7] WARN  c.m.a.c.s.TrustAllEndpointDetailProvider - ⚠️ TrustAllEndpointDetailProvider created - Certificate validation BYPASSED for endpoint: http://localhost:8081/reverse-flow/receive-as4-message
2025-07-02 11:29:05.070 [http-nio-8081-exec-7] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Using keystore: keystore/cert.p12, alias: cert
2025-07-02 11:29:05.071 [http-nio-8081-exec-7] INFO  c.m.a.c.service.AS4ConversionService - ✅ Certificate validation BYPASSED for development/testing
2025-07-02 11:29:05.073 [http-nio-8081-exec-7] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Initializing TrustAllEndpointDetailProvider for participant: [PeppolParticipantIdentifier@0x5d819e11: scheme=iso6523-actorid-upis; value=**********]
2025-07-02 11:29:05.073 [http-nio-8081-exec-7] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Loading dummy certificate for receiver AP to bypass validation
2025-07-02 11:29:05.073 [http-nio-8081-exec-7] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Loading dummy certificate from keystore for receiver AP
2025-07-02 11:29:05.074 [http-nio-8081-exec-7] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - 📦 Loading keystore from classpath: keystore/cert.p12
2025-07-02 11:29:05.076 [http-nio-8081-exec-7] INFO  c.m.a.c.s.TrustAllEndpointDetailProvider - ✅ Dummy certificate loaded for receiver AP - using same cert as sender
2025-07-02 11:29:05.076 [http-nio-8081-exec-7] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Certificate subject: CN=POP000688, OU=PEPPOL TEST AP, O=SunTec Business Solutions DMCC, C=AE
2025-07-02 11:29:05.076 [http-nio-8081-exec-7] DEBUG c.m.a.c.s.TrustAllEndpointDetailProvider - Using keystore: keystore/cert.p12, alias: cert
