package com.morohub.apsp.core.service;

import java.io.IOException;
import java.io.StringReader;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.xml.transform.stream.StreamSource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.stereotype.Service;
import com.morohub.apsp.config.ConfigurableResourceLoader;
import com.morohub.apsp.common.exception.AS4Exception;
import com.morohub.apsp.common.exception.ValidationException;
import com.morohub.apsp.core.entity.ErrorMasterData;

import net.sf.saxon.s9api.Processor;
import net.sf.saxon.s9api.Serializer;
import net.sf.saxon.s9api.XsltCompiler;
import net.sf.saxon.s9api.XsltExecutable;
import net.sf.saxon.s9api.XsltTransformer;

@Service
public class SchematronValidationService {

    private static final Logger LOGGER = LoggerFactory.getLogger(SchematronValidationService.class);

    private final Processor processor;

    @Autowired
    private ConfigurableResourceLoader resourceLoader;

    @Value("${app.config.schematron.base-path:schematron/}")
    private String schematronBasePath;

    public SchematronValidationService() {
        this.processor = new Processor(false);
    }

    public List<String> validateXml(String xmlContent, String schematronFilePath) throws Exception {
        try {
            LOGGER.info("Starting Schematron validation for file: {}", schematronFilePath);

            // Load Schematron file using configurable resource loader
            Resource schematronResource = resourceLoader.loadResource(schematronBasePath, schematronFilePath);
            if (!schematronResource.exists()) {
                throw new RuntimeException("Schematron file not found at: " +
                    resourceLoader.getResourceLocation(schematronBasePath + schematronFilePath));
            }

            LOGGER.info("✅ Loading schematron from: {}",
                resourceLoader.getResourceLocation(schematronBasePath + schematronFilePath));

            // Convert Schematron to XSLT (this is a simplified approach)
            // For full Schematron support, you'd need the ISO Schematron XSLT files
            String xsltContent = convertSchematronToXSLT(schematronResource);

            // Create XSLT transformer
            XsltCompiler compiler = processor.newXsltCompiler();
            XsltExecutable executable = compiler.compile(new StreamSource(new StringReader(xsltContent)));
            XsltTransformer transformer = executable.load();

            // Set input XML
            transformer.setSource(new StreamSource(new StringReader(xmlContent)));

            // Transform and capture output
            StringWriter resultWriter = new StringWriter();
            Serializer serializer = processor.newSerializer(resultWriter);
            transformer.setDestination(serializer);

            transformer.transform();

            String svrlResult = resultWriter.toString();

            // Parse SVRL result for failed assertions
            List<String> errors = parseSVRLForErrors(svrlResult);

            LOGGER.info("Schematron validation completed. Found {} errors.", errors.size());
            return errors;

        } catch (Exception e) {
            LOGGER.error("Exception during Schematron validation: {}", e.getMessage(), e);
            throw new AS4Exception("VAL_002", "Schematron validation failed: " + e.getMessage(),
                ErrorMasterData.ErrorType.SCHEMATRON_VALIDATION_FAILURE,
                ErrorMasterData.ErrorSeverity.HIGH, false, null, null, e);
        }
    }

    /**
     * Validate XML against multiple Schematron files in order
     * @param xmlContent The XML content to validate
     * @param schematronFilePaths List of schematron file paths to validate against (in order)
     * @return List of all validation errors from all schematron files
     * @throws Exception if validation fails
     */
    public List<String> validateXmlWithMultipleSchematrons(String xmlContent, List<String> schematronFilePaths) throws Exception {
        List<String> allErrors = new ArrayList<>();

        if (schematronFilePaths == null || schematronFilePaths.isEmpty()) {
            LOGGER.warn("No schematron files provided for validation");
            return allErrors;
        }

        LOGGER.info("Starting multiple Schematron validation with {} files", schematronFilePaths.size());

        for (int i = 0; i < schematronFilePaths.size(); i++) {
            String schematronFilePath = schematronFilePaths.get(i);
            try {
                LOGGER.info("Validating with schematron file {} of {}: {}", i + 1, schematronFilePaths.size(), schematronFilePath);
                List<String> errors = validateXml(xmlContent, schematronFilePath);

                // Add file context to errors
                for (String error : errors) {
                    allErrors.add(String.format("[File %d/%d: %s] %s", i + 1, schematronFilePaths.size(), schematronFilePath, error));
                }

            } catch (Exception e) {
                String errorMsg = String.format("Failed to validate with schematron file %d/%d (%s): %s",
                    i + 1, schematronFilePaths.size(), schematronFilePath, e.getMessage());
                LOGGER.error(errorMsg, e);

                // Add as validation error rather than throwing exception to continue with other files
                allErrors.add(errorMsg);
            }
        }

        LOGGER.info("Multiple Schematron validation completed. Total errors: {}", allErrors.size());
        return allErrors;
    }

    private String convertSchematronToXSLT(Resource schematronResource) throws IOException {
        // This is a very basic conversion - in a real implementation you'd use
        // the ISO Schematron XSLT stylesheets to convert .sch to .xsl
        // For now, let's create a simple XSLT that validates basic rules

        return """
            <?xml version="1.0" encoding="UTF-8"?>
            <xsl:stylesheet version="2.0" 
                xmlns:xsl="http://www.w3.org/1999/XSL/Transform"
                xmlns:svrl="http://purl.oclc.org/dsdl/svrl"
                xmlns:ubl="urn:oasis:names:specification:ubl:schema:xsd:Invoice-2"
                xmlns:cbc="urn:oasis:names:specification:ubl:schema:xsd:CommonBasicComponents-2"
                xmlns:cac="urn:oasis:names:specification:ubl:schema:xsd:CommonAggregateComponents-2">
                
                <xsl:output method="xml" indent="yes"/>
                
                <xsl:template match="/">
                    <svrl:schematron-output>
                        <xsl:apply-templates/>
                    </svrl:schematron-output>
                </xsl:template>
                
                <xsl:template match="ubl:Invoice">
                    <!-- Basic validation: Invoice must have an ID -->
                    <xsl:if test="not(cbc:ID) or cbc:ID = ''">
                        <svrl:failed-assert test="cbc:ID" location="/ubl:Invoice">
                            <svrl:text>Invoice must have an ID</svrl:text>
                        </svrl:failed-assert>
                    </xsl:if>
                    
                    <!-- Basic validation: Invoice must have a supplier -->
                    <xsl:if test="not(cac:AccountingSupplierParty)">
                        <svrl:failed-assert test="cac:AccountingSupplierParty" location="/ubl:Invoice">
                            <svrl:text>Invoice must have an accounting supplier party</svrl:text>
                        </svrl:failed-assert>
                    </xsl:if>
                    
                    <!-- Continue processing child elements -->
                    <xsl:apply-templates/>
                </xsl:template>
                
                <xsl:template match="text()">
                    <!-- Suppress text output -->
                </xsl:template>
                
            </xsl:stylesheet>
            """;
    }

    private List<String> parseSVRLForErrors(String svrlResult) {
        List<String> errors = new ArrayList<>();

        // Use regex to find failed assertions in SVRL output
        Pattern failedAssertPattern = Pattern.compile(
                "<svrl:failed-assert[^>]*test=\"([^\"]+)\"[^>]*location=\"([^\"]+)\"[^>]*>\\s*<svrl:text>([^<]+)</svrl:text>",
                Pattern.DOTALL
        );

        Matcher matcher = failedAssertPattern.matcher(svrlResult);
        while (matcher.find()) {
            String test = matcher.group(1);
            String location = matcher.group(2);
            String text = matcher.group(3);
            errors.add(String.format("Validation Error: %s at %s (Test: %s)", text, location, test));
        }

        // If no specific errors found but the validation failed, add a general error
        if (errors.isEmpty() && svrlResult.contains("failed-assert")) {
            errors.add("Schematron validation failed - check the XML structure");
        }

        return errors;
    }
}
