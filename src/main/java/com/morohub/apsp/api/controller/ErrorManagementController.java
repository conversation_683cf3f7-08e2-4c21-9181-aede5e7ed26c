package com.morohub.apsp.api.controller;

import com.morohub.apsp.core.entity.ErrorLog;
import com.morohub.apsp.core.entity.ErrorMasterData;
import com.morohub.apsp.core.service.ErrorManagementService;
import com.morohub.apsp.core.service.RetryService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * REST API controller for error management operations
 * Requirement 4: API facility to reprocess or discard errors
 */
@RestController
@RequestMapping("/api/error-management")
public class ErrorManagementController {

    private static final Logger logger = LoggerFactory.getLogger(ErrorManagementController.class);

    @Autowired
    private ErrorManagementService errorManagementService;

    @Autowired
    private RetryService retryService;

    /**
     * Get error master data by error code
     */
    @GetMapping("/master-data/{errorCode}")
    public ResponseEntity<ErrorMasterData> getErrorMasterData(@PathVariable String errorCode) {
        try {
            Optional<ErrorMasterData> errorMaster = errorManagementService.getErrorMasterData(errorCode);
            return errorMaster.map(ResponseEntity::ok)
                             .orElse(ResponseEntity.notFound().build());
        } catch (Exception e) {
            logger.error("Error retrieving master data for {}: {}", errorCode, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get error logs for a specific message
     */
    @GetMapping("/logs/message/{messageId}")
    public ResponseEntity<List<ErrorLog>> getErrorLogsForMessage(@PathVariable String messageId) {
        try {
            List<ErrorLog> errorLogs = errorManagementService.getErrorLogsForMessage(messageId);
            return ResponseEntity.ok(errorLogs);
        } catch (Exception e) {
            logger.error("Error retrieving error logs for message {}: {}", messageId, e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get unresolved errors
     */
    @GetMapping("/logs/unresolved")
    public ResponseEntity<List<ErrorLog>> getUnresolvedErrors(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        try {
            List<ErrorLog> unresolvedErrors = errorManagementService.getUnresolvedErrors();
            
            // Simple pagination
            int start = page * size;
            int end = Math.min(start + size, unresolvedErrors.size());
            
            if (start >= unresolvedErrors.size()) {
                return ResponseEntity.ok(List.of());
            }
            
            List<ErrorLog> paginatedErrors = unresolvedErrors.subList(start, end);
            return ResponseEntity.ok(paginatedErrors);
        } catch (Exception e) {
            logger.error("Error retrieving unresolved errors: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get errors ready for retry
     */
    @GetMapping("/logs/ready-for-retry")
    public ResponseEntity<List<ErrorLog>> getErrorsReadyForRetry() {
        try {
            List<ErrorLog> errorsReadyForRetry = errorManagementService.getErrorsReadyForRetry();
            return ResponseEntity.ok(errorsReadyForRetry);
        } catch (Exception e) {
            logger.error("Error retrieving errors ready for retry: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Mark error as resolved
     */
    @PostMapping("/logs/{errorLogId}/resolve")
    public ResponseEntity<Map<String, Object>> markErrorAsResolved(
            @PathVariable Long errorLogId,
            @RequestParam String resolvedBy,
            @RequestParam(required = false) String resolutionNotes) {
        try {
            errorManagementService.markErrorAsResolved(errorLogId, resolvedBy, resolutionNotes);
            
            Map<String, Object> response = Map.of(
                "success", true,
                "message", "Error marked as resolved",
                "errorLogId", errorLogId,
                "resolvedBy", resolvedBy,
                "resolvedAt", LocalDateTime.now()
            );
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Error marking error log {} as resolved: {}", errorLogId, e.getMessage(), e);
            
            Map<String, Object> response = Map.of(
                "success", false,
                "message", "Failed to mark error as resolved: " + e.getMessage(),
                "errorLogId", errorLogId
            );
            
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * Manually trigger retry for an error
     */
    @PostMapping("/logs/{errorLogId}/retry")
    public ResponseEntity<Map<String, Object>> manualRetry(
            @PathVariable Long errorLogId,
            @RequestParam String triggeredBy) {
        try {
            boolean success = retryService.manualRetry(errorLogId, triggeredBy);
            
            Map<String, Object> response = Map.of(
                "success", success,
                "message", success ? "Retry triggered successfully" : "Failed to trigger retry",
                "errorLogId", errorLogId,
                "triggeredBy", triggeredBy,
                "triggeredAt", LocalDateTime.now()
            );
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Error triggering manual retry for error log {}: {}", errorLogId, e.getMessage(), e);
            
            Map<String, Object> response = Map.of(
                "success", false,
                "message", "Failed to trigger retry: " + e.getMessage(),
                "errorLogId", errorLogId
            );
            
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * Discard an error (no more retries)
     */
    @PostMapping("/logs/{errorLogId}/discard")
    public ResponseEntity<Map<String, Object>> discardError(
            @PathVariable Long errorLogId,
            @RequestParam String discardedBy,
            @RequestParam String reason) {
        try {
            boolean success = retryService.discardError(errorLogId, discardedBy, reason);
            
            Map<String, Object> response = Map.of(
                "success", success,
                "message", success ? "Error discarded successfully" : "Failed to discard error",
                "errorLogId", errorLogId,
                "discardedBy", discardedBy,
                "reason", reason,
                "discardedAt", LocalDateTime.now()
            );
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("Error discarding error log {}: {}", errorLogId, e.getMessage(), e);
            
            Map<String, Object> response = Map.of(
                "success", false,
                "message", "Failed to discard error: " + e.getMessage(),
                "errorLogId", errorLogId
            );
            
            return ResponseEntity.internalServerError().body(response);
        }
    }

    /**
     * Get retry statistics
     */
    @GetMapping("/statistics/retry")
    public ResponseEntity<RetryService.RetryStatistics> getRetryStatistics() {
        try {
            RetryService.RetryStatistics statistics = retryService.getRetryStatistics();
            return ResponseEntity.ok(statistics);
        } catch (Exception e) {
            logger.error("Error retrieving retry statistics: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Get error summary/dashboard data
     */
    @GetMapping("/dashboard")
    public ResponseEntity<Map<String, Object>> getErrorDashboard() {
        try {
            RetryService.RetryStatistics retryStats = retryService.getRetryStatistics();
            List<ErrorLog> unresolvedErrors = errorManagementService.getUnresolvedErrors();
            List<ErrorLog> errorsReadyForRetry = errorManagementService.getErrorsReadyForRetry();
            
            Map<String, Object> dashboard = Map.of(
                "retryStatistics", retryStats,
                "unresolvedErrorsCount", unresolvedErrors.size(),
                "errorsReadyForRetryCount", errorsReadyForRetry.size(),
                "lastUpdated", LocalDateTime.now()
            );
            
            return ResponseEntity.ok(dashboard);
        } catch (Exception e) {
            logger.error("Error retrieving error dashboard: {}", e.getMessage(), e);
            return ResponseEntity.internalServerError().build();
        }
    }

    /**
     * Health check for error management system
     */
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> healthCheck() {
        try {
            Map<String, Object> health = Map.of(
                "status", "UP",
                "timestamp", LocalDateTime.now(),
                "errorManagementService", "ACTIVE",
                "retryService", "ACTIVE"
            );
            
            return ResponseEntity.ok(health);
        } catch (Exception e) {
            logger.error("Error in health check: {}", e.getMessage(), e);
            
            Map<String, Object> health = Map.of(
                "status", "DOWN",
                "timestamp", LocalDateTime.now(),
                "error", e.getMessage()
            );
            
            return ResponseEntity.internalServerError().body(health);
        }
    }
}
