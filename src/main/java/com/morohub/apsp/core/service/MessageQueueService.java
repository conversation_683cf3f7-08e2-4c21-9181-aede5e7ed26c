package com.morohub.apsp.core.service;

import com.morohub.apsp.core.entity.MessageProcessingQueue;
import com.morohub.apsp.core.entity.MessageProcessingQueue.ProcessingStatus;
import com.morohub.apsp.core.repository.MessageProcessingQueueRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

import com.morohub.apsp.common.exception.AS4Exception;
import com.morohub.apsp.core.entity.ErrorMasterData;

/**
 * Service for managing message processing queue operations
 */
@Service
@Transactional
public class MessageQueueService {

    private static final Logger logger = LoggerFactory.getLogger(MessageQueueService.class);

    @Autowired
    private MessageProcessingQueueRepository messageQueueRepository;

    /**
     * Add a new message to the processing queue
     */
    public MessageProcessingQueue addMessageToQueue(String ublXml, String countryCode, String documentType, Phase4AS4ReceiverService.SbdhInfo sbdhInfo) {
        try {
            String messageId = generateMessageId();
            
            MessageProcessingQueue message = new MessageProcessingQueue();
            message.setMessageId(messageId);
            message.setUblXml(ublXml);
            message.setCountryCode(countryCode);
            message.setDocumentType(documentType);
            message.setOriginalSbdhBusinessScope(sbdhInfo.getBusinessScope());
            message.setOriginalDocumentInstanceId(sbdhInfo.getDocumentInstanceId());
            message.setStatus(ProcessingStatus.READY_TO_SEND);

            
            // Extract participant IDs from UBL XML
            extractParticipantIds(message, ublXml);
            
            MessageProcessingQueue savedMessage = messageQueueRepository.save(message);
            
            logger.info("✅ Message added to queue: {} with status: {}", messageId, ProcessingStatus.READY_TO_SEND);
            return savedMessage;
            
        } catch (Exception e) {
            logger.error("❌ Error adding message to queue: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to add message to queue: " + e.getMessage(), e);
        }
    }

    /**
     * Get messages ready for processing
     */
    @Transactional(readOnly = true)
    public List<MessageProcessingQueue> getMessagesReadyForProcessing() {
        return getMlsMessagesReadyForProcessing();
    }

    /**
     * Get only MLS messages ready for processing
     */
    @Transactional(readOnly = true)
    public List<MessageProcessingQueue> getMlsMessagesReadyForProcessing() {
        try {
            List<MessageProcessingQueue> messages = messageQueueRepository.findByFlowTypeAndStatusOrderByCreatedDateAsc(
                MessageProcessingQueue.FlowType.MLS_MESSAGE,
                MessageProcessingQueue.ProcessingStatus.READY_TO_SEND);
            logger.debug("📋 Found {} MLS messages ready for processing", messages.size());
            return messages;
        } catch (Exception e) {
            logger.error("❌ Error retrieving MLS messages ready for processing: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to retrieve MLS messages: " + e.getMessage(), e);
        }
    }

    /**
     * Update message status
     */
    public void updateMessageStatus(Long messageId, ProcessingStatus status, String errorMessage) {
        try {
            Optional<MessageProcessingQueue> messageOpt = messageQueueRepository.findById(messageId);
            if (messageOpt.isPresent()) {
                MessageProcessingQueue message = messageOpt.get();
                message.setStatus(status);
                message.setLastProcessed(LocalDateTime.now());
                
                if (errorMessage != null) {
                    message.setErrorMessage(errorMessage);
                }
                
                messageQueueRepository.save(message);
                logger.debug("📝 Updated message {} status to: {}", message.getMessageId(), status);
            } else {
                logger.warn("⚠️ Message not found for ID: {}", messageId);
            }
        } catch (Exception e) {
            logger.error("❌ Error updating message status: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to update message status: " + e.getMessage(), e);
        }
    }

    /**
     * Mark message as processing
     */
    public void markMessageAsProcessing(Long messageId) {
        updateMessageStatus(messageId, ProcessingStatus.PROCESSING, null);
    }

    /**
     * Mark message as completed
     */
    public void markMessageAsCompleted(Long messageId, String mlsMessageId) {
        try {
            Optional<MessageProcessingQueue> messageOpt = messageQueueRepository.findById(messageId);
            if (messageOpt.isPresent()) {
                MessageProcessingQueue message = messageOpt.get();
                message.setStatus(ProcessingStatus.COMPLETED);
                message.setMlsMessageId(mlsMessageId);
                message.setLastProcessed(LocalDateTime.now());
                
                messageQueueRepository.save(message);
                logger.info("✅ Message {} marked as completed with MLS ID: {}", message.getMessageId(), mlsMessageId);
            }
        } catch (Exception e) {
            logger.error("❌ Error marking message as completed: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to mark message as completed: " + e.getMessage(), e);
        }
    }

    /**
     * Mark message for retry
     */
    public void markMessageForRetry(Long messageId, String errorMessage) {
        try {
            Optional<MessageProcessingQueue> messageOpt = messageQueueRepository.findById(messageId);
            if (messageOpt.isPresent()) {
                MessageProcessingQueue message = messageOpt.get();
                message.incrementRetryCount();
                
                if (message.hasExceededMaxRetries()) {
                    message.setStatus(ProcessingStatus.ERROR);
                    logger.warn("⚠️ Message {} exceeded max retries, marking as ERROR", message.getMessageId());
                } else {
                    message.setStatus(ProcessingStatus.RETRY);
                    logger.info("🔄 Message {} marked for retry (attempt {})", message.getMessageId(), message.getRetryCount());
                }
                
                message.setErrorMessage(errorMessage);
                message.setLastProcessed(LocalDateTime.now());
                
                messageQueueRepository.save(message);
            }
        } catch (Exception e) {
            logger.error("❌ Error marking message for retry: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to mark message for retry: " + e.getMessage(), e);
        }
    }

    /**
     * Update message with SBDH information
     */
    public void updateMessageWithSbdhInfo(Long messageId, String businessScope, String documentInstanceId) {
        try {
            Optional<MessageProcessingQueue> messageOpt = messageQueueRepository.findById(messageId);
            if (messageOpt.isPresent()) {
                MessageProcessingQueue message = messageOpt.get();
                message.setOriginalSbdhBusinessScope(businessScope);
                message.setOriginalDocumentInstanceId(documentInstanceId);

                messageQueueRepository.save(message);
                logger.debug("📝 Updated message {} with SBDH information", message.getMessageId());
            } else {
                logger.warn("⚠️ Message not found for ID: {}", messageId);
            }
        } catch (Exception e) {
            logger.error("❌ Error updating message with SBDH info: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to update message with SBDH info: " + e.getMessage(), e);
        }
    }

    /**
     * Get queue statistics
     */
    @Transactional(readOnly = true)
    public QueueStatistics getQueueStatistics() {
        try {
            QueueStatistics stats = new QueueStatistics();
            stats.setReadyToSend(messageQueueRepository.countByStatus(ProcessingStatus.READY_TO_SEND));
            stats.setProcessing(messageQueueRepository.countByStatus(ProcessingStatus.PROCESSING));
            stats.setRetry(messageQueueRepository.countByStatus(ProcessingStatus.RETRY));
            stats.setCompleted(messageQueueRepository.countByStatus(ProcessingStatus.COMPLETED));
            stats.setError(messageQueueRepository.countByStatus(ProcessingStatus.ERROR));

            return stats;
        } catch (Exception e) {
            logger.error("❌ Error getting queue statistics: {}", e.getMessage(), e);
            throw new RuntimeException("Failed to get queue statistics: " + e.getMessage(), e);
        }
    }

    /**
     * Generate unique message ID
     */
    private String generateMessageId() {
        return "MSG-" + UUID.randomUUID().toString();
    }

    /**
     * Extract participant IDs from UBL XML
     */
    private void extractParticipantIds(MessageProcessingQueue message, String ublXml) {
        try {
            // This is a simplified extraction - you may want to enhance this
            // based on your specific UBL structure
            
            // For now, set default values - you can enhance this logic
            message.setSenderParticipantId("9908:sender-default");
            message.setReceiverParticipantId("9908:receiver-default");
            
            logger.debug("📋 Extracted participant IDs - Sender: {}, Receiver: {}", 
                        message.getSenderParticipantId(), message.getReceiverParticipantId());
                        
        } catch (Exception e) {
            logger.warn("⚠️ Error extracting participant IDs from UBL XML: {}", e.getMessage());
            // Set defaults on error
            message.setSenderParticipantId("9908:sender-default");
            message.setReceiverParticipantId("9908:receiver-default");
        }
    }

    /**
     * Inner class for queue statistics
     */
    public static class QueueStatistics {
        private long readyToSend;
        private long processing;
        private long retry;
        private long completed;
        private long error;

        // Getters and setters
        public long getReadyToSend() { return readyToSend; }
        public void setReadyToSend(long readyToSend) { this.readyToSend = readyToSend; }
        
        public long getProcessing() { return processing; }
        public void setProcessing(long processing) { this.processing = processing; }
        
        public long getRetry() { return retry; }
        public void setRetry(long retry) { this.retry = retry; }
        
        public long getCompleted() { return completed; }
        public void setCompleted(long completed) { this.completed = completed; }
        
        public long getError() { return error; }
        public void setError(long error) { this.error = error; }
        
        public long getTotal() {
            return readyToSend + processing + retry + completed + error;
        }
    }

    // ===== NEW METHODS FOR DIFFERENT FLOW TYPES =====

    /**
     * Add XML validation request to queue for reprocessing capability
     */
    public MessageProcessingQueue addXmlValidationToQueue(String xml, String requestId) {
        return addToQueueWithFlowType(xml, "DEFAULT", "VALIDATION",
            MessageProcessingQueue.FlowType.XML_VALIDATION, requestId, null);
    }

    /**
     * Add Peppol SBD Invoice request to queue for reprocessing capability
     */
    public MessageProcessingQueue addPeppolSbdInvoiceToQueue(String requestData, String countryCode,
                                                           String documentType, String requestId) {
        return addToQueueWithFlowType(requestData, countryCode, documentType,
            MessageProcessingQueue.FlowType.PEPPOL_SBD_INVOICE, requestId, null);
    }

    /**
     * Add AS4 conversion request to queue for reprocessing capability
     */
    public MessageProcessingQueue addAS4ConversionToQueue(String xml, String countryCode, String documentType,
                                                        String requestId, String endpointUrl) {
        return addToQueueWithFlowType(xml, countryCode, documentType,
            MessageProcessingQueue.FlowType.FORWARD_FLOW, requestId, endpointUrl);
    }

    /**
     * Generic method to add message to queue with flow type
     * Status is set based on flow type:
     * - MLS_MESSAGE: READY_TO_SEND (for polling)
     * - Others: COMPLETED (for reprocessing only)
     */
    private MessageProcessingQueue addToQueueWithFlowType(String data, String countryCode, String documentType,
                                                        MessageProcessingQueue.FlowType flowType, String requestId,
                                                        String endpointUrl) {
        try {
            String messageId = generateMessageId();

            MessageProcessingQueue message = new MessageProcessingQueue();
            message.setMessageId(messageId);
            message.setUblXml(data);
            message.setCountryCode(countryCode);
            message.setDocumentType(documentType);
            message.setFlowType(flowType);
            message.setRequestId(requestId);
            message.setEndpointUrl(endpointUrl);

            // Set status based on flow type
            if (flowType == MessageProcessingQueue.FlowType.MLS_MESSAGE) {
                message.setStatus(MessageProcessingQueue.ProcessingStatus.READY_TO_SEND);
            } else {
                // For non-MLS flows, mark as completed since they're processed immediately
                // Queue entry is for reprocessing capability only
                message.setStatus(MessageProcessingQueue.ProcessingStatus.COMPLETED);
            }

            MessageProcessingQueue saved = messageQueueRepository.save(message);
            logger.info("✅ Added {} message to processing queue: {} (status: {})",
                       flowType, messageId, message.getStatus());

            return saved;

        } catch (Exception e) {
            logger.error("❌ Failed to add {} message to queue: {}", flowType, e.getMessage(), e);
            throw new AS4Exception("SYS_001", "Failed to add message to processing queue: " + e.getMessage(),
                ErrorMasterData.ErrorType.SYSTEM_ERROR,
                ErrorMasterData.ErrorSeverity.HIGH, false, null, requestId, e);
        }
    }
}
