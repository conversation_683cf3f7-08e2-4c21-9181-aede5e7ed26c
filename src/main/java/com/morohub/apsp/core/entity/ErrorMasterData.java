package com.morohub.apsp.core.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.time.LocalDateTime;

/**
 * Master data entity for all error types in the Access Point system
 * Requirement 1: Master data for all errors (technical, schematron validation and any other)
 */
@Entity
@Table(name = "error_master_data")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ErrorMasterData {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * Error Code (HTTP error code, Data error code as defined by Xelerate etc.) (Mandatory)
     */
    @Column(name = "error_code", unique = true, nullable = false, length = 50)
    private String errorCode;

    /**
     * HTTP Status Code associated with this error
     */
    @Column(name = "http_status_code", nullable = false)
    private Integer httpStatusCode;

    /**
     * Error Type (Technical Connectivity Error, Schema Validation Failure, Document Capability Mismatch, Others) (Mandatory)
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "error_type", nullable = false)
    private ErrorType errorType;

    /**
     * Error Description (Mandatory)
     */
    @Column(name = "error_description", nullable = false, columnDefinition = "TEXT")
    private String errorDescription;

    /**
     * Retry Required Y/N (Mandatory)
     */
    @Column(name = "retry_required", nullable = false)
    private Boolean retryRequired;

    /**
     * Retry Interval in milliseconds (Mandatory if "Retry Required Y/N" is Y)
     */
    @Column(name = "retry_interval_ms")
    private Long retryIntervalMs;

    /**
     * Maximum number of retry attempts
     */
    @Column(name = "max_retry_attempts")
    private Integer maxRetryAttempts;

    /**
     * Whether this error is active/enabled
     */
    @Column(name = "active")
    private Boolean active = true;

    /**
     * Category for grouping related errors
     */
    @Column(name = "category", length = 100)
    private String category;

    /**
     * Severity level of the error
     */
    @Enumerated(EnumType.STRING)
    @Column(name = "severity")
    private ErrorSeverity severity;

    @Column(name = "created_date")
    private LocalDateTime createdDate;

    @Column(name = "last_updated")
    private LocalDateTime lastUpdated;

    @Column(name = "created_by", length = 100)
    private String createdBy;

    @Column(name = "updated_by", length = 100)
    private String updatedBy;

    /**
     * Error Type Enumeration
     */
    public enum ErrorType {
        TECHNICAL_CONNECTIVITY_ERROR,
        SCHEMA_VALIDATION_FAILURE,
        SCHEMATRON_VALIDATION_FAILURE,
        DOCUMENT_CAPABILITY_MISMATCH,
        AUTHENTICATION_FAILURE,
        AUTHORIZATION_FAILURE,
        BUSINESS_RULE_VIOLATION,
        SYSTEM_ERROR,
        CONFIGURATION_ERROR,
        TIMEOUT_ERROR,
        CERTIFICATE_ERROR,
        ENCRYPTION_ERROR,
        SIGNATURE_ERROR,
        OTHERS
    }

    /**
     * Error Severity Enumeration
     */
    public enum ErrorSeverity {
        CRITICAL,
        HIGH,
        MEDIUM,
        LOW,
        INFO
    }

    @PrePersist
    public void prePersist() {
        this.createdDate = LocalDateTime.now();
        this.lastUpdated = LocalDateTime.now();
        if (this.active == null) {
            this.active = true;
        }
    }

    @PreUpdate
    public void preUpdate() {
        this.lastUpdated = LocalDateTime.now();
    }

    /**
     * Validation method to ensure retry interval is set when retry is required
     */
    public boolean isValidRetryConfiguration() {
        if (Boolean.TRUE.equals(retryRequired)) {
            return retryIntervalMs != null && retryIntervalMs > 0 && 
                   maxRetryAttempts != null && maxRetryAttempts > 0;
        }
        return true;
    }
}
