package com.morohub.apsp.common.exception;

import com.morohub.apsp.core.entity.ErrorMasterData;

/**
 * Exception for authentication and authorization failures
 * These errors are typically not retryable
 */
public class AuthenticationException extends AS4Exception {

    private final String authenticationType;
    private final String userId;
    private final String clientId;

    public AuthenticationException(String errorCode, String message) {
        super(errorCode, message, ErrorMasterData.ErrorType.AUTHENTICATION_FAILURE, 
              ErrorMasterData.ErrorSeverity.HIGH, false);
        this.authenticationType = null;
        this.userId = null;
        this.clientId = null;
    }

    public AuthenticationException(String errorCode, String message, String authenticationType) {
        super(errorCode, message, ErrorMasterData.ErrorType.AUTHENTICATION_FAILURE, 
              ErrorMasterData.ErrorSeverity.HIGH, false);
        this.authenticationType = authenticationType;
        this.userId = null;
        this.clientId = null;
    }

    public AuthenticationException(String errorCode, String message, String authenticationType, 
                                 String userId, String requestId) {
        super(errorCode, message, ErrorMasterData.ErrorType.AUTHENTICATION_FAILURE, 
              ErrorMasterData.ErrorSeverity.HIGH, false, null, requestId);
        this.authenticationType = authenticationType;
        this.userId = userId;
        this.clientId = null;
    }

    public AuthenticationException(String errorCode, String message, String authenticationType, 
                                 String userId, String clientId, String requestId) {
        super(errorCode, message, ErrorMasterData.ErrorType.AUTHENTICATION_FAILURE, 
              ErrorMasterData.ErrorSeverity.HIGH, false, null, requestId);
        this.authenticationType = authenticationType;
        this.userId = userId;
        this.clientId = clientId;
    }

    // Authorization failure constructor
    public static AuthenticationException authorizationFailure(String errorCode, String message, 
                                                             String userId, String requestId) {
        AuthenticationException ex = new AuthenticationException(errorCode, message, "AUTHORIZATION", userId, requestId);
        return new AuthenticationException(errorCode, message, ErrorMasterData.ErrorType.AUTHORIZATION_FAILURE, 
                                         ErrorMasterData.ErrorSeverity.HIGH, false, null, requestId);
    }

    // Certificate authentication failure
    public static AuthenticationException certificateFailure(String errorCode, String message, 
                                                            String clientId, String requestId) {
        return new AuthenticationException(errorCode, message, "CERTIFICATE", null, clientId, requestId);
    }

    // Token authentication failure
    public static AuthenticationException tokenFailure(String errorCode, String message, 
                                                      String userId, String requestId) {
        return new AuthenticationException(errorCode, message, "TOKEN", userId, requestId);
    }

    // API Key authentication failure
    public static AuthenticationException apiKeyFailure(String errorCode, String message, 
                                                       String clientId, String requestId) {
        return new AuthenticationException(errorCode, message, "API_KEY", null, clientId, requestId);
    }

    // Basic authentication failure
    public static AuthenticationException basicAuthFailure(String errorCode, String message, 
                                                          String userId, String requestId) {
        return new AuthenticationException(errorCode, message, "BASIC_AUTH", userId, requestId);
    }

    // OAuth authentication failure
    public static AuthenticationException oauthFailure(String errorCode, String message, 
                                                      String clientId, String requestId) {
        return new AuthenticationException(errorCode, message, "OAUTH", null, clientId, requestId);
    }

    // Constructor for authorization failures
    private AuthenticationException(String errorCode, String message, ErrorMasterData.ErrorType errorType, 
                                   ErrorMasterData.ErrorSeverity severity, boolean retryable, 
                                   String messageId, String requestId) {
        super(errorCode, message, errorType, severity, retryable, messageId, requestId);
        this.authenticationType = "AUTHORIZATION";
        this.userId = null;
        this.clientId = null;
    }

    public String getAuthenticationType() {
        return authenticationType;
    }

    public String getUserId() {
        return userId;
    }

    public String getClientId() {
        return clientId;
    }

    /**
     * Get the appropriate HTTP status code for this authentication error
     */
    public int getHttpStatusCode() {
        if (getErrorType() == ErrorMasterData.ErrorType.AUTHORIZATION_FAILURE) {
            return 403; // Forbidden
        }
        return 401; // Unauthorized
    }

    @Override
    public String toString() {
        return String.format("AuthenticationException{errorCode='%s', authenticationType='%s', userId='%s', clientId='%s', message='%s'}", 
                           getErrorCode(), authenticationType, userId, clientId, getMessage());
    }
}
